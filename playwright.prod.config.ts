import type { PlaywrightTestConfig } from '@playwright/test';
import { devices } from '@playwright/test';
import path from 'path';

// Use absolute path for auth file
const authFile = path.join(process.cwd(), 'playwright', '.auth', 'prod-user.json');

/**
 * Configuration for Production environment tests
 */
const config: PlaywrightTestConfig = {
	testDir: './tests/e2e',
	timeout: 30 * 1000,
	expect: {
		timeout: 5000
	},
	/* Run tests in files in parallel */
	fullyParallel: true,
	/* Fail the build on CI if you accidentally left test.only in the source code. */
	forbidOnly: !!process.env.CI,
	/* Retry on CI only */
	retries: process.env.CI ? 2 : 0,
	/* Opt out of parallel tests on CI. */
	workers: process.env.CI ? 1 : undefined,
	/* Reporter to use. See https://playwright.dev/docs/test-reporters */
	reporter: 'html',
	/* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
	use: {
		headless: process.env.CI ? true : false, // Headless in CI, headed locally
		actionTimeout: 0,
		baseURL: 'https://app.totm.space',
		trace: 'on-first-retry',
		ignoreHTTPSErrors: true,
		screenshot: 'only-on-failure',
		video: 'retain-on-failure'
	},

	/* Configure projects for major browsers */
	projects: [
		{
			name: 'prod-setup',
			testDir: './tests/setup',
			testMatch: /prod-auth\.setup\.ts/,
			timeout: 5 * 60 * 1000, // 5 minutes timeout for auth setup
			use: {
				headless: process.env.CI ? true : false,
				ignoreHTTPSErrors: true,
				actionTimeout: 60000 // Increase action timeout for auth flow
			}
		},
		{
			name: 'prod-chromium',
			use: {
				...devices['Desktop Chrome'],
				storageState: authFile, // Use absolute path
				ignoreHTTPSErrors: true
			}
		},
		{
			name: 'prod-firefox',
			use: {
				...devices['Desktop Firefox'],
				storageState: authFile // Use absolute path
			}
		}
	]
};

export default config;
