{"project": {"name": "totm-search", "type": "SvelteKit + TypeScript", "description": "A search application with complex database relationships and authentication"}, "self_modification": {"enabled": true, "communication_protocol": {"announcement": {"emoji": "⚠️", "message": "Rule Change Proposal", "format": "⚠️ Rule Change Proposal:\n- Current Rule: [description]\n- Proposed Change: [description]\n- Reason: [explanation]\n- Impact: [potential effects]\n\nShall I proceed with this change? (✅ to approve)"}, "status": {"proposing": "⚠️", "approved": "✅", "completed": "🎯", "warning": "⚡", "error": "❌"}}, "triggers": [{"event": "rule_conflict", "description": "When two or more rules conflict in practice", "action": "Propose resolution with clear tradeoffs"}, {"event": "rule_inefficiency", "description": "When a rule creates unnecessary complexity or slows development", "action": "Suggest optimization with concrete examples"}, {"event": "missing_pattern", "description": "When encountering recurring patterns not covered by existing rules", "action": "Propose new rule based on observed patterns"}, {"event": "outdated_rule", "description": "When a rule no longer aligns with project evolution or best practices", "action": "Suggest update with migration path"}, {"event": "regression_reported", "description": "When a user reports functionality that previously worked is now broken", "action": "Create test case and propose new testing rule"}], "modification_process": {"steps": [{"step": "identification", "action": "Identify rule that needs modification", "emoji": "🔍"}, {"step": "analysis", "action": "Analyze impact on existing codebase", "emoji": "📊"}, {"step": "proposal", "action": "Present change with emoji protocol", "emoji": "⚠️"}, {"step": "approval", "action": "Wait for user approval", "emoji": "⏳"}, {"step": "implementation", "action": "Apply approved changes", "emoji": "🔄"}, {"step": "documentation", "action": "Update learning_persistence with change", "emoji": "📝"}], "constraints": ["Never modify core project structure rules without explicit approval", "Maintain backward compatibility when possible", "Document all changes in learning_persistence", "Ensure changes align with project goals", "Preserve existing type safety and security rules"]}, "change_tracking": {"location": ".cursor/rule_changes.json", "format": {"id": "string", "timestamp": "ISO date", "rule_path": "path to modified rule", "previous_value": "old rule content", "new_value": "new rule content", "reason": "justification for change", "approved_by": "user confirmation timestamp"}}, "rule_management": {"synchronization": {"primary_source": ".cursorrules", "linked_files": {"rule_changes": ".cursor/rule_changes.json", "rule_docs": ".cursor/rules/**/*.md"}, "update_process": {"steps": ["Update primary source (.cursorrules)", "Generate/update corresponding .md files", "Track changes in rule_changes.json", "Validate rule consistency across files"], "validation": {"checks": ["All referenced files exist", "All rules have corresponding documentation", "All changes are tracked in rule_changes.json", "No orphaned rule files"]}}}, "templates": {"rule_change": {"format": "⚠️ Rule Change Proposal:\n- Current Rule: [description]\n- Proposed Change: [description]\n- Reason: [explanation]\n- Impact: [potential effects]\n\nShall I proceed with this change? (✅ to approve)"}, "md_file": {"sections": ["# Rule Name", "## Purpose", "## Guidelines", "## Examples", "## Best Practices"]}, "rule_change_entry": {"format": {"id": "rule_[category]_[timestamp]", "timestamp": "ISO date", "rule_path": "path to modified rule", "previous_value": "old rule content", "new_value": "new rule content", "reason": "justification for change", "approved_by": "user confirmation timestamp"}}}, "auto_documentation": {"enabled": true, "generators": {"md_files": {"trigger": "rule_change", "template": "md_file", "location": ".cursor/rules/"}, "change_log": {"trigger": "rule_change", "template": "rule_change_entry", "location": ".cursor/rule_changes.json"}}}}}, "rules": {"general_rules": [{"description": "Before modifying any database-related code, analyze all foreign key relationships and RLS policies that might be affected. Reference and update the README.md's TODO section for any changes to database relationships.", "type": "database_analysis"}, {"description": "When modifying authentication or user-related code, ensure all Supabase RLS policies are properly maintained and documented in README.md.", "type": "auth_policy_preservation"}, {"description": "Maintain strict TypeScript types for all database entities as defined in database.types.ts. Update types when database schema changes.", "type": "type_safety"}, {"description": "Follow SvelteKit's file-based routing conventions and keep route handlers (+page.server.ts, +server.ts) focused on their specific responsibilities.", "type": "routing_conventions"}], "living_documentation": [{"description": "When database schema changes are made, automatically update the README.md's TODO section with new foreign key relationships and their cascade behaviors.", "trigger": "database_schema_change", "update_location": "README.md#TODO"}, {"description": "When new RLS policies are added or modified, document them in README.md with their roles, commands, and definitions.", "trigger": "rls_policy_change", "update_location": "README.md#RLS_Policies"}, {"description": "Keep package.json dependencies organized by separating dev dependencies and production dependencies clearly.", "trigger": "dependency_change", "update_location": "package.json"}], "ai_reasoning": [{"description": "Before modifying any Supabase-related code, analyze the impact on existing RLS policies and data access patterns.", "type": "supabase_analysis"}, {"description": "When working with OpenAI integration, ensure proper error handling and rate limiting are implemented.", "type": "ai_integration"}, {"description": "For any UI components, verify TailwindCSS classes follow the project's existing patterns and maintain consistency.", "type": "styling_consistency"}], "testing": {"structure": {"directories": {"e2e": {"location": "tests/e2e", "purpose": "End-to-end tests using <PERSON><PERSON>", "naming": "[feature].test.ts"}, "integration": {"location": "tests/integration", "purpose": "Integration tests for Supabase and API endpoints", "naming": "[feature].test.ts"}, "unit": {"location": "src/**/__tests__", "purpose": "Unit tests for components and utilities", "naming": "[filename].test.ts"}, "utils": {"location": "tests/utils", "purpose": "Shared test utilities and helpers", "naming": "[utility].ts"}}, "file_organization": {"test_file_location": "Place test files next to the code they test when possible", "helper_location": "Place test helpers in tests/utils directory", "fixture_location": "Place test fixtures in tests/fixtures directory"}}, "supabase": {"test_environment": {"setup": ["Use separate test database for integration tests", "Reset database state before each test suite", "Use test-specific RLS policies"], "data_management": ["Clean up test data after each test", "Use unique identifiers for test data", "Avoid test data collision between parallel tests"]}, "auth_testing": {"user_management": ["Create unique test users for each test suite", "Clean up test users after tests complete", "Test both authenticated and unauthenticated states"], "session_handling": ["Test session expiration scenarios", "Verify proper logout behavior", "Test session refresh flows"]}, "rls_testing": {"required_tests": ["Test access to own resources", "Test access to other users' resources", "Test different role permissions", "Verify policy combinations"], "best_practices": ["Test both positive and negative cases", "Verify cascading policy effects", "Test policy changes with different user roles"]}}, "e2e": {"best_practices": ["Use data-testid attributes for element selection", "Avoid brittle selectors like CSS classes", "Test complete user workflows", "Handle loading and transition states"], "required_tests": {"auth": ["Login flow", "Registration flow", "Password reset flow", "Session management"], "data": ["CRUD operations", "Search functionality", "Filtering and sorting", "Pagination"], "ui": ["Responsive design", "Error states", "Loading states", "Success messages"]}, "supabase_integration": {"auth_flows": {"setup": ["Use test-specific email domains (e.g., @e2e-test.com)", "Create unique test users with predictable patterns", "Clean up test users after each test run", "Isolate test data by test suite"], "required_scenarios": ["Email/password signup and confirmation", "Magic link authentication", "OAuth provider authentication", "Session persistence across page reloads", "Session timeout handling", "Password reset flow", "Account deletion flow"], "best_practices": ["Use separate test database for E2E tests", "Reset database state before each test suite", "Mock external auth providers in test environment", "Test both success and failure paths"]}, "data_management": {"setup": ["Create isolated test data per test suite", "Use unique identifiers with test prefixes", "Implement comprehensive cleanup routines", "Handle cascading deletes properly"], "patterns": {"data_isolation": "prefix_[test_suite]_[entity_type]_[timestamp]", "cleanup_order": ["child_tables", "junction_tables", "parent_tables"], "state_verification": ["database_state", "client_state", "ui_state"]}}, "rls_testing": {"scenarios": ["Anonymous access restrictions", "Authenticated user permissions", "Role-based access control", "Cross-user data isolation", "Organization/team-based permissions"], "verification_steps": ["Verify direct database access", "Verify API endpoint access", "Verify UI element visibility", "Test permission inheritance"]}}, "state_management": {"patterns": ["Test store initialization", "Verify state persistence", "Check state synchronization", "Test store cleanup"], "scenarios": {"auth_state": ["Initial load state", "Post-authentication state", "Session refresh", "Logout cleanup"], "data_state": ["Loading states", "Error states", "Empty states", "Pagination state", "Filter/sort state"], "form_state": ["Initial values", "Validation states", "Submission states", "Error recovery"]}}, "test_organization": {"file_structure": {"auth": ["login.test.ts", "signup.test.ts", "password-reset.test.ts"], "companies": ["company-creation.test.ts", "company-management.test.ts"], "search": ["search-basic.test.ts", "search-advanced.test.ts"], "shared": ["fixtures.ts", "helpers.ts"]}, "naming_conventions": {"test_files": "[feature]-[scenario].test.ts", "test_descriptions": "should [expected behavior] when [condition]", "fixtures": "[entity]-[scenario].fixture.ts"}}, "performance_testing": {"metrics": ["Page load time", "Search response time", "UI interaction responsiveness", "Resource usage"], "thresholds": {"page_load": "< 3s", "search_response": "< 1s", "ui_interaction": "< 100ms"}}, "error_handling": {"scenarios": ["Network failures", "Database connection issues", "Invalid input handling", "Rate limiting", "Concurrent operations"], "verification": ["Error message display", "Recovery options", "State consistency", "User guidance"]}, "accessibility": {"compliance": ["WCAG 2.1", "ARIA labels", "Keyboard navigation"], "testing_tools": ["Playwright accessibility tools", "Screen reader compatibility"], "scenarios": ["Navigation without mouse", "Screen reader announcements", "Color contrast", "Focus management"]}}, "mocking": {"strategies": {"supabase": "Use test client with controlled responses", "api": "Mock external API calls", "environment": "Use test-specific environment variables"}, "best_practices": ["Keep mocks close to actual data structure", "Version mock data with schema changes", "Document mock data assumptions"]}, "ci_integration": {"requirements": ["Run all tests in CI pipeline", "Maintain test coverage thresholds", "Report test results clearly", "Cache test dependencies"], "workflow": {"order": ["1. Unit tests", "2. Integration tests", "3. E2E tests"], "parallelization": {"unit": "Run in parallel", "integration": "Run in parallel with clean database instances", "e2e": "Run sequentially or in isolated environments"}}}, "maintenance": {"regular_tasks": ["Update test data with schema changes", "Review and update mocks", "Clean up obsolete tests", "Maintain documentation"], "documentation": {"required_sections": ["Test setup instructions", "Mock data documentation", "Test environment configuration", "Common testing patterns"]}}, "playwright": {"recording_workflow": {"authentication": {"setup": "Create an auth.setup.ts file that logs in and saves authentication state", "usage": "Use npx playwright codegen --load-storage=playwright/.auth/user.json --ignore-https-errors", "best_practices": ["Store auth state in playwright/.auth/user.json", "Use storageState: 'playwright/.auth/user.json' in test.use()", "Separate authentication from test logic", "Increase timeouts for auth operations (recommended: 5 minutes)"]}, "test_recording": {"command": "npx playwright codegen [URL] --load-storage=playwright/.auth/user.json --ignore-https-errors", "workflow": ["Record the basic happy path first", "Clean up the generated code to make it robust", "Add assertions for key elements", "Test in headless mode to verify"]}}, "test_structure": {"setup": ["Use descriptive test names that explain the workflow", "Store test data in global variables for cleanup", "Use timestamps in test data to ensure uniqueness", "Set appropriate timeouts for the entire test"], "organization": ["Divide tests into logical sections with clear comments", "Use try/catch blocks for each major section", "Continue the test even if non-critical sections fail", "Use console.log to provide clear progress information"], "cleanup": ["Implement test.afterEach() for cleanup operations", "Be prepared for cleanup to fail - provide manual cleanup instructions", "Consider background cleanup scripts for test data instead of in-test cleanup", "Log cleanup status clearly"], "resilience": ["Use permissive selectors that won't break with minor UI changes", "Handle expected redirects and authentication flows", "Add reasonable timeouts to all async operations", "Use .first() when multiple elements might match"]}, "selector_strategies": {"priorities": ["Prefer role-based selectors (getByRole) over CSS selectors", "Use data-testid attributes for critical elements", "For text content, use getByText() with case-insensitive regex", "Use .filter() to refine selections when necessary"], "dynamic_content": ["Use waitForURL with patterns for dynamic URL elements", "Add explicit waits when loading dynamic content", "Check element visibility before interacting with it", "<PERSON>le 'not found' cases gracefully"]}, "common_patterns": {"form_submission": {"pattern": ["Fill form fields with unique values", "Submit form with button click or Enter press", "Wait for navigation or success indicator", "Verify redirect or success state"]}, "navigation": {"pattern": ["Use navigation links when possible instead of direct URL navigation", "Wait for load state after navigation ('networkidle' recommended)", "Verify current URL contains expected path", "Check for expected elements after navigation"]}, "data_verification": {"pattern": ["Check for specific text or elements that confirm success", "Use permissive assertions for content that might vary", "Log actual values for debugging purposes", "Implement retries for flaky verifications"]}, "error_handling": {"pattern": ["Wrap test sections in try/catch blocks", "Log detailed error information", "Continue test execution for non-critical failures", "Fail the test immediately for critical failures"]}}}}, "code_style": {"language": "TypeScript", "framework": "SvelteKit", "styling": "TailwindCSS", "database": {"primary": "Supabase", "type_management": {"generation": {"command": "supabase gen types typescript --local > database.types.ts", "triggers": ["After running any migration", "After modifying database schema through Supabase Studio", "After pulling schema changes from other environments", "Before starting work on new features that interact with the database"]}, "best_practices": ["Never manually modify database.types.ts", "Create minimal intermediate types that match exact query shape", "Use `as unknown as IntermediateType[]` pattern for safe type casting", "Remove unnecessary complex types when simpler ones will suffice", "Prefer querying from views when they exist"], "required_files": [{"purpose": "Type management rules", "path": ".cursor/rules/supabase/database-types.md", "when_to_include": "When working with database types or queries"}]}, "ai_prompts": {"required_files": [{"purpose": "SQL formatting and style", "path": ".cursor/rules/supabase/code-format-sql.md", "when_to_include": "When writing any SQL code or reviewing SQL style"}, {"purpose": "Database migrations", "path": ".cursor/rules/supabase/database-create-migration.md", "when_to_include": "When creating or modifying database migrations"}, {"purpose": "Database functions", "path": ".cursor/rules/supabase/database-functions.md", "when_to_include": "When creating or modifying database functions"}, {"purpose": "RLS policies", "path": ".cursor/rules/supabase/database-rls-policies.md", "when_to_include": "When working with Row Level Security policies"}], "usage": {"instruction": "Include the relevant AI prompt file using @file before proceeding with any database-related operations", "examples": ["For migrations: @file .cursor/rules/supabase/database-create-migration.md", "For SQL style: @file .cursor/rules/supabase/code-format-sql.md", "For functions: @file .cursor/rules/supabase/database-functions.md", "For RLS: @file .cursor/rules/supabase/database-rls-policies.md"]}}}, "package_management": {"primary": "pnpm", "rules": ["Always use pnpm for package management", "Use exact versions for production dependencies", "Group dev dependencies with -D flag", "Use pnpm workspace features for monorepo management if needed", "Maintain clean lockfile with pnpm prune"], "commands": {"install": "pnpm install", "add": "pnpm add", "add_dev": "pnpm add -D", "remove": "pnpm remove", "update": "pnpm update", "audit": "pnpm audit", "clean": "pnpm store prune"}, "preferences": ["Use pnpm-lock.yaml for deterministic builds", "Leverage pnpm's disk space efficiency", "Utilize strict package management with pnpm", "Keep node_modules clean with symlinks"]}, "patterns": {"components": {"location": "src/lib/components", "naming": "PascalCase.svelte", "ui_framework": {"primary": "shadcn-svelte", "installation": "pnpm dlx shadcn-svelte@latest add [component-name]", "component_location": "src/lib/components/ui", "preferences": ["Use shadcn-svelte CLI to add components: pnpm dlx shadcn-svelte@latest add [component-name]", "Customize using Tailwind classes", "Follow shadcn-svel<PERSON>'s accessibility patterns", "Use the shadcn CLI for adding components"], "component_categories": {"data_display": ["Table", "Card", "Avatar", "Badge"], "inputs": ["<PERSON><PERSON>", "Input", "Select", "Checkbox", "Radio", "Switch", "Textarea"], "navigation": ["Tabs", "Pagination", "Breadcrumb", "Dropdown"], "feedback": ["<PERSON><PERSON>", "Toast", "Progress", "Skeleton"], "overlay": ["Dialog", "Drawer", "Popover", "<PERSON><PERSON><PERSON>"], "layout": ["Accordion", "Collapsible", "Separator", "Sheet"]}, "customization": {"location": "src/lib/components/ui/theme", "approach": "Extend shadcn components using Tailwind classes and CSS variables"}}}, "routes": {"location": "src/routes", "structure": "feature-based", "handlers": ["+page.svelte", "+page.server.ts", "+server.ts"]}, "types": {"location": "src/lib/types", "naming": "camelCase.types.ts"}, "utils": {"location": "src/lib/utils", "naming": "camelCase.ts"}}, "preferences": {"state_management": "prefer SvelteKit stores over complex state management", "api_structure": "use +server.ts for API endpoints", "form_handling": "use Superforms with Zod validation", "styling": "use TailwindCSS with @tailwind/typography", "icons": "use Lucide icons", "ui_components": {"primary": "shadcn-svelte", "rules": ["Always check shadcn-svelte component library first before building custom components", "Follow shadcn-svelte's theming system for consistent styling", "Use shadcn-svelte's form components with Superforms integration", "Maintain accessibility features provided by shadcn-svelte components", "Use shadcn-svelte's color scheme system for dark/light mode"], "customization": ["Customize using Tailwind classes first", "Extend components through composition rather than modification", "Keep shadcn-svelte base components separate from custom implementations", "Document any component extensions or modifications"]}}}, "learning_persistence": {"storage": {"location": ".cursor/lessons.json", "structure": {"issues": [{"id": "string", "type": "error | warning | improvement", "category": "database | auth | ui | performance | security", "description": "Detailed description of the issue", "solution": "Applied solution", "context": {"files": ["affected files"], "components": ["affected components"], "date": "timestamp", "commit": "git commit hash if available"}, "prevention": "Steps to prevent this issue in future"}], "patterns": [{"id": "string", "type": "success | failure", "pattern": "Description of the code pattern", "usage": "When to use this pattern", "examples": ["code examples"], "alternatives": ["alternative approaches"], "context": {"framework": "relevant framework", "category": "category of pattern"}}], "regression_tests": [{"id": "string", "reported_date": "ISO date", "feature": "affected feature", "description": "what stopped working", "test_location": "path to test file", "test_case": "description of test case", "original_behavior": "how it used to work", "fix": "how it was fixed", "prevention": "how to prevent similar issues"}]}}, "triggers": [{"event": "error_occurrence", "action": "Record error details, solution, and prevention steps", "update_location": ".cursor/lessons.json#issues"}, {"event": "pattern_identification", "action": "Record successful or problematic patterns", "update_location": ".cursor/lessons.json#patterns"}, {"event": "performance_issue", "action": "Document performance bottleneck and solution", "update_location": ".cursor/lessons.json#issues"}, {"event": "regression_reported", "action": "Document regression and create test case", "update_location": ".cursor/lessons.json#regression_tests", "test_template": {"format": "describe('[Feature]', () => {\n  test('should [expected behavior]', () => {\n    // Arrange\n    // Act\n    // Assert\n  });\n});"}}], "reference_rules": [{"description": "Before suggesting a solution, check .cursor/lessons.json for similar issues or patterns", "type": "historical_analysis"}, {"description": "When encountering an error, verify if it's a known issue in lessons.json before proposing a solution", "type": "error_verification"}, {"description": "After resolving an issue, document the solution and prevention steps in lessons.json", "type": "solution_documentation"}], "categories": {"database": {"subabase_errors": "Common Supabase-related issues and solutions", "rls_mistakes": "Frequent RLS policy mistakes and fixes", "schema_issues": "Database schema problems and resolutions"}, "auth": {"token_handling": "Authentication token issues and best practices", "permission_errors": "Common permission-related problems", "session_management": "Session handling issues and solutions"}, "ui": {"component_patterns": "Reusable UI patterns and anti-patterns", "styling_conflicts": "TailwindCSS conflicts and resolutions", "accessibility": "Common accessibility issues and fixes"}, "performance": {"query_optimization": "Database query performance improvements", "render_efficiency": "UI rendering optimization patterns", "api_bottlenecks": "API performance issues and solutions"}, "testing": {"regression_tests": "Test cases created from reported regressions", "test_patterns": "Common testing patterns and best practices", "test_coverage": "Areas needing more test coverage"}}}, "code_quality": {"dry_principles": {"rules": ["Before implementing new functionality, search for similar existing implementations", "Extract repeated logic into reusable utility functions", "Create shared components for repeated UI patterns", "Use TypeScript types/interfaces to avoid duplicating type definitions", "Leverage SvelteKit layouts to avoid duplicating layout code", "Create hooks for repeated stateful logic", "Use constants for repeated values"], "patterns": {"utilities": {"location": "src/lib/utils", "naming": "camelCase.ts", "organization": "Group related utilities in feature-specific files"}, "shared_components": {"location": "src/lib/components/shared", "naming": "PascalCase.svelte", "documentation": "Document component props and usage examples"}, "types": {"location": "src/lib/types", "naming": "camelCase.types.ts", "organization": "Group related types in domain-specific files"}, "constants": {"location": "src/lib/constants", "naming": "UPPER_SNAKE_CASE", "organization": "Group related constants by feature or domain"}}, "triggers": [{"event": "duplicate_code_detected", "action": "Propose extraction into shared utility or component", "threshold": "2 or more occurrences of similar code"}, {"event": "repeated_logic_pattern", "action": "Suggest creation of custom hook or utility function"}, {"event": "duplicate_type_definition", "action": "Move to shared types file and import where needed"}], "analysis": {"scope": ["Check for similar implementations across the codebase", "Identify repeated UI patterns", "Look for repeated type definitions", "Detect similar business logic implementations"], "suggestions": ["Propose appropriate abstraction level", "Suggest location for shared code", "Provide refactoring steps", "Consider impact on existing code"]}}}, "violation_handling": {"detection": {"triggers": [{"event": "dry_violation", "description": "Code duplication or pattern violation detected", "severity": "warning", "emoji": "⚡"}, {"event": "structure_violation", "description": "Project structure or naming convention violation", "severity": "warning", "emoji": "⚡"}, {"event": "pattern_violation", "description": "Established pattern or best practice violation", "severity": "warning", "emoji": "⚡"}, {"event": "critical_violation", "description": "Security, type safety, or database integrity violation", "severity": "error", "emoji": "❌"}, {"event": "migration_violation", "description": "Database migration file or content violates guidelines", "severity": "error", "emoji": "❌", "checks": ["File naming format", "SQL style guidelines", "RLS policy implementation", "Required comments", "Security best practices"]}, {"event": "supabase_prompt_missing", "description": "Required Supabase AI prompt file not included for database operation", "severity": "warning", "emoji": "⚡", "checks": ["SQL formatting prompt for SQL operations", "Migration prompt for database migrations", "Function prompt for database functions", "RLS prompt for security policies"]}], "notification_format": {"emoji": "⚡", "template": "Rule Violation Detected:\n- Rule: [rule description]\n- Location: [file/line]\n- Violation: [description]\n- Suggested Fix: [fix description]\n\nShall I implement the fix? (✅ to approve)"}}, "resolution": {"steps": [{"step": "identification", "action": "Identify specific rule being violated", "emoji": "🔍"}, {"step": "analysis", "action": "Analyze impact and complexity of fix", "emoji": "📊"}, {"step": "proposal", "action": "Propose specific fix with examples", "emoji": "💡"}, {"step": "approval", "action": "Wait for user approval", "emoji": "⏳"}, {"step": "implementation", "action": "Apply approved fix", "emoji": "🔄"}, {"step": "verification", "action": "Verify fix resolves violation", "emoji": "✔️"}], "documentation": {"location": ".cursor/violations.json", "format": {"id": "string", "rule_violated": "reference to rule", "detection_date": "ISO date", "location": {"file": "relative path", "line": "line number or range"}, "violation_type": "dry | structure | pattern | critical", "description": "detailed violation description", "fix_applied": "description of fix", "approved_by": "user confirmation timestamp", "prevention": "steps to prevent similar violations"}}}, "prevention": {"actions": ["Update learning_persistence with violation pattern", "Add regression test if applicable", "Document prevention steps in violations.json", "Propose new rules if pattern is recurring"]}}, "test_fix_tracking": {"location": ".cursor/test_fixes.json", "format": {"id": "string", "test_file": "path to test file", "test_name": "name of failing test", "error": "error message", "status": "pending | in_progress | fixed", "fix_steps": ["array of steps needed"], "dependencies": ["other tests that must be fixed first"], "priority": "1-5"}}}}