/** @type {import('ts-jest').JestConfigWithTsJest} */
export default {
  preset: 'ts-jest/presets/default-esm',
  moduleNameMapper: {
    '^\\$lib/(.*)$': '<rootDir>/src/lib/$1',
    '^\\$env/dynamic/public$': '<rootDir>/src/mocks/dynamic/public.ts',
    '^svelte/(.*)$': ['<rootDir>/src/mocks/svelte/$1.ts'],
    '^svelte$': ['<rootDir>/src/mocks/svelte/index.ts']
  },
  extensionsToTreatAsEsm: ['.ts'],
  transform: {
    '^.+\\.(t|j)sx?$': ['ts-jest', {
      useESM: true,
      tsconfig: 'tsconfig.test.json',
      isolatedModules: true
    }]
  },
  testEnvironment: 'node',
  setupFiles: [
    '<rootDir>/src/test-utils/test-setup.ts'
  ],
  testMatch: [
    '**/__tests__/**/*.integration.test.[jt]s?(x)',
  ],
  testTimeout: 30000, // 30 seconds for integration tests
  verbose: true,
  transformIgnorePatterns: [
    'node_modules/(?!(@supabase|supabase-js)/)'
  ],
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node']
}; 