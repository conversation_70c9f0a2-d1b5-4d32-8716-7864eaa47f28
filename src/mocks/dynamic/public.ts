import dotenv from 'dotenv';
import path from 'path';

// Load test environment variables
dotenv.config({ path: path.resolve(process.cwd(), '.env.test') });

export const env = {
	PUBLIC_AZURE_CLIENT_ID: process.env.PUBLIC_AZURE_CLIENT_ID || '',
	PUBLIC_BASE_URL: process.env.PUBLIC_BASE_URL || 'https://localhost:5173',
	PUBLIC_SUPABASE_ANON_KEY: process.env.PUBLIC_SUPABASE_ANON_KEY || '',
	PUBLIC_SUPABASE_URL: process.env.PUBLIC_SUPABASE_URL || '',
	PUBLIC_SHAREPOINT_SITE_URL: process.env.PUBLIC_SHAREPOINT_SITE_URL || ''
} as const;

export const dynamic = {
	public: env
};
