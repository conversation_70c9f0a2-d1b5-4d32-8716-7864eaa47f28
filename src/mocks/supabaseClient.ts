import { createClient } from "@supabase/supabase-js";
import { env } from "$env/dynamic/public";

// Mock data for tests
const MOCK_DATA = {
  datasource: {
    id: "mock-datasource-id",
    secret_oauth: "mock-oauth-secret-id",
    company_id: process.env.TEST_COMPANY_ID,
    type: "sharepoint",
  },
  vaultSecret: {
    id: "mock-oauth-secret-id",
    json: {
      refresh_token: "mock-refresh-token",
      access_token: "mock-access-token",
    },
  },
};

// Mock Supabase client
const mockClient = {
  from: (table: string) => ({
    select: (query?: string) => ({
      eq: (field: string, value: string) => {
        if (table === "datasources") {
          return {
            eq: (field2: string, value2: string) => ({
              single: () => {
                if (
                  field === "company_id" && field2 === "type" &&
                  value2 === "sharepoint"
                ) {
                  return Promise.resolve({
                    data: MOCK_DATA.datasource,
                    error: null,
                  });
                }
                return Promise.resolve({
                  data: null,
                  error: { message: "Not found" },
                });
              },
            }),
          };
        } else if (table === "vault.decrypted_secrets") {
          return {
            single: () => {
              if (
                field === "id" && value === MOCK_DATA.datasource.secret_oauth
              ) {
                return Promise.resolve({
                  data: MOCK_DATA.vaultSecret,
                  error: null,
                });
              }
              return Promise.resolve({
                data: null,
                error: { message: "Not found" },
              });
            },
          };
        }
        return {
          single: () =>
            Promise.resolve({
              data: null,
              error: { message: "Table not found" },
            }),
        };
      },
    }),
  }),
};

// Mock createClient function
export const createClient = () => mockClient;

// Export mock client instance
export const supabase = mockClient;
