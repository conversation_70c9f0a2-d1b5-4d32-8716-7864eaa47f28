// Helper file for product logos

// Import types
interface ProductLogo {
	src: string;
	alt: string;
}

// Map of product types to their logo paths
export const productLogos: Record<string, ProductLogo> = {
	sharepoint: {
		src: '/product-logos/sharepoint-logo.svg',
		alt: 'SharePoint Logo'
	},
	onedrive: {
		src: '/product-logos/onedrive-logo.svg',
		alt: 'OneDrive Logo'
	},
	googledrive: {
		src: '/product-logos/googledrive-logo.svg',
		alt: 'Google Drive Logo'
	},
	dropbox: {
		src: '/product-logos/dropbox-logo.svg',
		alt: 'Dropbox Logo'
	},
	slack: {
		src: '/product-logos/slack-logo.svg',
		alt: 'Slack Logo'
	},
	teams: {
		src: '/product-logos/teams-logo.svg',
		alt: 'Microsoft Teams Logo'
	},
	whatsapp: {
		src: '/product-logos/whatsapp-logo.svg',
		alt: 'WhatsApp Business Logo'
	},
	email: {
		src: '/product-logos/email-logo.svg',
		alt: 'Email Logo'
	},
	ai: {
		src: '/product-logos/ai-logo.svg',
		alt: 'AI Logo'
	},
	upstack: {
		src: '/product-logos/upstack-logo.svg',
		alt: 'Upstack Logo'
	}
};

/**
 * Get the logo information for a product based on its name
 * @param productName The name of the product
 * @returns The logo information including src and alt text
 */
export function getProductLogo(productName?: string): ProductLogo {
	// Handle undefined or null productName
	if (!productName) {
		return {
			src: '/product-logos/default-logo.svg',
			alt: 'Unknown Product Logo'
		};
	}

	const normalizedName = productName.toLowerCase().replace(/\s+/g, '');

	// Try to match the product name with our known logos
	for (const [key, logo] of Object.entries(productLogos)) {
		if (normalizedName.includes(key)) {
			return logo;
		}
	}

	// Return a default logo if no match is found
	return {
		src: '/product-logos/default-logo.svg',
		alt: productName + ' Logo'
	};
}

/**
 * Determine if a product is a storage provider
 * @param product The product object
 * @returns True if the product is a storage provider
 */
export function isStorageProvider(productName: string): boolean {
	const normalizedName = productName.toLowerCase();
	return (
		normalizedName.includes('sharepoint') ||
		normalizedName.includes('onedrive') ||
		normalizedName.includes('googledrive') ||
		normalizedName.includes('google drive') ||
		normalizedName.includes('dropbox')
	);
}

/**
 * Get the connection route for a storage provider
 * @param providerName The name of the storage provider
 * @returns The API route to connect to the provider
 */
export function getProviderRoute(providerName: string): string {
	const normalizedName = providerName.toLowerCase().replace(/\s+/g, '');

	if (normalizedName.includes('sharepoint')) {
		return '/api/connect-sharepoint';
	} else if (normalizedName.includes('onedrive')) {
		return '/api/connect-onedrive';
	} else if (normalizedName.includes('googledrive') || normalizedName.includes('google drive')) {
		return '/api/connect-googledrive';
	} else if (normalizedName.includes('dropbox')) {
		return '/api/connect-dropbox';
	}

	// Default to SharePoint if no match is found
	return '/api/connect-sharepoint';
}
