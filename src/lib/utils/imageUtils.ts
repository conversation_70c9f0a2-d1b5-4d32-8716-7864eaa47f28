export async function validateAndResizeImage(file: File) {
  // Validate file type
  if (!file.type.startsWith("image/")) {
    throw new Error("File must be an image");
  }

  // Validate file size (e.g., 5MB max)
  if (file.size > 5 * 1024 * 1024) {
    throw new Error("File size must be less than 5MB");
  }

  // You could add image resizing here using a library like browser-image-compression
  return file;
}
