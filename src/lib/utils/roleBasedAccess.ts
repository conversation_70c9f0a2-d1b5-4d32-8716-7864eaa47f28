/**
 * Utility to verify user role-based access to specific resources
 */

type ResourcePermission = {
	requiredRoles: string[];
	description: string;
};

// Map of resources to the roles that can access them
const RESOURCE_PERMISSIONS: Record<string, ResourcePermission> = {
	// Company resources
	dashboard: {
		requiredRoles: ['admin', 'member', 'guest', 'expert'],
		description: 'Access to company dashboard'
	},
	'manage-company': {
		requiredRoles: ['admin'],
		description: 'Manage company settings'
	},
	products: {
		requiredRoles: ['admin'],
		description: 'Manage company products'
	},
	vault: {
		requiredRoles: ['admin', 'expert'],
		description: 'Access to document vault'
	},
	users: {
		requiredRoles: ['admin'],
		description: 'Manage company users'
	}
	// Add more resource permissions as needed
};

/**
 * Verifies if a user has the required role for a specific resource
 *
 * @param user The user object
 * @param companyId The company ID
 * @param resource The resource to check access for
 * @param supabase The Supabase client
 * @returns Promise<boolean> Whether the user has access
 */
export async function verifyUserRoleForUrl(
	user: any | null,
	companyId: string,
	resource: string,
	supabase: any
): Promise<boolean> {
	// If no user is authenticated, deny access
	if (!user) {
		return false;
	}

	// Get the resource permission configuration
	const permission = RESOURCE_PERMISSIONS[resource];

	if (!permission) {
		console.warn(`No permission configuration found for resource: ${resource}`);
		return false;
	}

	try {
		// Fetch the user's role in this company
		const { data, error } = await supabase
			.from('company_members')
			.select('role')
			.eq('company_id', companyId)
			.eq('user_id', user.id)
			.single();

		if (error || !data) {
			console.error('Error fetching user role:', error);
			return false;
		}

		const userRole = data.role;

		// Check if the user's role is in the list of required roles
		return permission.requiredRoles.includes(userRole);
	} catch (error) {
		console.error('Error verifying user role:', error);
		return false;
	}
}
