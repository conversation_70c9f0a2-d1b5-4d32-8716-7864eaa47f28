/**
 * UI Helper functions for handling common UI-related tasks and issues
 */

/**
 * Force a UI update by triggering a DOM reflow
 * Useful when components don't update properly or need to sync with DOM state changes
 * @param elementId Optional ID of element to target, or undefined for document body
 */
export function forceUiUpdate(elementId?: string) {
  // Use setTimeout to ensure this runs after the current execution context
  setTimeout(() => {
    try {
      // Get the target element
      const targetElement = elementId
        ? document.getElementById(elementId)
        : document.body;

      if (targetElement) {
        // Get computed style to force a reflow
        const _ = window.getComputedStyle(targetElement).getPropertyValue(
          "display",
        );

        // Dispatch a custom event in case any component is listening
        const event = new CustomEvent("ui-update", {
          bubbles: true,
          detail: { elementId, timestamp: Date.now() },
        });
        targetElement.dispatchEvent(event);
      }
    } catch (error) {
      console.warn("Error forcing UI update:", error);
    }
  }, 0);
}

/**
 * Force any open popovers, dropdowns, or menus to close
 * Useful when UI elements get stuck in the open state
 */
export function closeAllMenus() {
  try {
    // Click on the document body to close any open menus
    document.body.click();

    // Force escape key event to close modals/dialogs
    const escEvent = new KeyboardEvent("keydown", {
      key: "Escape",
      code: "Escape",
      keyCode: 27,
      which: 27,
      bubbles: true,
    });
    document.dispatchEvent(escEvent);

    // Get all elements commonly used for dropdowns
    const dropdownElements = document.querySelectorAll(
      '[data-state="open"], [aria-expanded="true"], [aria-hidden="false"]',
    );

    // Set their states to closed
    dropdownElements.forEach((element) => {
      if (element instanceof HTMLElement) {
        element.setAttribute("data-state", "closed");
        element.setAttribute("aria-expanded", "false");
        element.setAttribute("aria-hidden", "true");
      }
    });
  } catch (error) {
    console.warn("Error closing menus:", error);
  }
}

/**
 * Check for and fix z-index conflicts in the UI
 * Useful for debugging UI element overlap issues
 */
export function fixZIndexConflicts() {
  try {
    // Find potential elements with z-index that might be causing conflicts
    const zIndexElements = document.querySelectorAll(
      '.fixed, .absolute, .sticky, [style*="z-index"]',
    );

    // Log potential conflicts for debugging
    const conflicts: Array<{ element: Element; zIndex: number }> = [];

    zIndexElements.forEach((element) => {
      const style = window.getComputedStyle(element);
      const zIndex = parseInt(style.zIndex);

      if (!isNaN(zIndex) && zIndex > 40) {
        conflicts.push({ element, zIndex });
      }
    });

    if (conflicts.length > 0) {
      console.warn("Potential z-index conflicts:", conflicts);
    }
  } catch (error) {
    console.warn("Error checking z-index conflicts:", error);
  }
}
