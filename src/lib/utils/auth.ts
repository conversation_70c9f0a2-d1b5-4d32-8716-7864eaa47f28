import { goto } from "$app/navigation";
import { supabase } from "$lib/supabaseClient";

/**
 * Signs out the current user from the application
 * Clears all cookies, signs out from Supabase, and redirects to the auth page
 */
export async function signOut() {
  try {
    // Clear all Supabase and custom cookies
    const cookies = document.cookie.split("; ");
    cookies.forEach((cookie) => {
      const cookieName = cookie.split("=")[0];
      // Clear cookie for root path and all subdomains
      document.cookie =
        `${cookieName}=; Max-Age=0; path=/; domain=${window.location.hostname}`;
      // Also clear cookie without domain specification
      document.cookie = `${cookieName}=; Max-Age=0; path=/;`;
    });

    // Sign out from Supabase
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error("Logout error:", error);
      return;
    }

    // Use goto for client-side navigation to auth page
    await goto("/auth", { replaceState: true });

    // Force a page reload to ensure all state is cleared
    window.location.reload();
  } catch (err) {
    console.error("Unexpected error during logout:", err);
  }
}
