/**
 * Filters search results to only include files the user has access to
 *
 * @param searchResults - The original search results
 * @param userEmail - The email of the user to check access for
 * @returns A promise that resolves to the filtered search results
 */
export async function filterSearchResultsByAccess(
  searchResults: any[],
  userEmail: string,
): Promise<any[]> {
  if (!searchResults || !searchResults.length || !userEmail) {
    return searchResults || [];
  }

  try {
    // Extract all file IDs from the search results
    // Use metadata.file_id which contains the external_id needed for access checking
    const fileIds = searchResults
      .filter((result) => result.metadata?.file_id)
      .map((result) => result.metadata.file_id);

    if (!fileIds.length) {
      console.warn("No valid file IDs found in search results metadata");
      return [];
    }

    console.log(
      `Checking access for user ${userEmail} to ${fileIds.length} files`,
    );

    // Call the API to check which files the user has access to
    const response = await fetch("/api/file-access/check-access", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        userEmail,
        fileIds,
      }),
    });

    if (!response.ok) {
      console.error("Error checking file access:", await response.text());
      return searchResults; // Return original results if there's an error
    }

    const { accessibleFileIds } = await response.json();

    if (!accessibleFileIds || !accessibleFileIds.length) {
      return []; // No accessible files
    }

    console.log(
      `User has access to ${accessibleFileIds.length} out of ${fileIds.length} files`,
    );

    // Filter search results to only include accessible files
    return searchResults.filter((result) =>
      result.metadata?.file_id &&
      accessibleFileIds.includes(result.metadata.file_id)
    );
  } catch (error) {
    console.error("Error filtering search results by access:", error);
    return searchResults; // Return original results if there's an error
  }
}
