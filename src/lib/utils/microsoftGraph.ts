import { PUBLIC_MICROSOFT_GRAPH_API_URL } from '$env/static/public';
import type { DriveItem } from '@microsoft/microsoft-graph-types';

interface GraphResponse {
	value: DriveItem[];
}

interface SimplifiedDriveItem {
	id: string;
	name: string;
	folder: boolean;
	size: number;
	lastModifiedDateTime: string;
	webUrl: string;
}

interface FilePermission {
	id: string;
	roles: string[];
	grantedToV2?: {
		user?: {
			id: string;
			displayName: string;
		};
		group?: {
			id: string;
			displayName: string;
		};
		siteGroup?: {
			id: string;
			displayName: string;
		};
		objectType: 'user' | 'group' | 'siteGroup';
	};
}

interface FilePermissionResponse {
	value: FilePermission[];
}

interface GroupMember {
	id: string;
	displayName: string;
	mail?: string;
	'@odata.type': '#microsoft.graph.user';
}

interface GroupMembersResponse {
	value: GroupMember[];
}

interface FileAccessMember {
	id: string;
	displayName: string;
	email?: string;
	accessType: 'direct' | 'group';
	roles: string[];
	groupName?: string;
}

interface GroupDetails {
	id: string;
	displayName: string;
	description?: string;
	groupTypes: string[];
}

// Add new interface for SharePoint site users
interface SharePointSiteUser {
	id: string;
	displayName: string;
	email?: string;
	userType: 'Person' | 'SharePointGroup' | 'DomainGroup';
	isPerson: boolean;
	principalName?: string;
}

export async function fetchFromGraph(
	accessToken: string,
	endpoint: string,
	fetch: typeof global.fetch,
	additionalHeaders?: Record<string, string>
): Promise<any> {
	const headers: Record<string, string> = {
		Authorization: `Bearer ${accessToken}`,
		Accept: 'application/json',
		...(additionalHeaders || {})
	};

	const response = await fetch(`${PUBLIC_MICROSOFT_GRAPH_API_URL}${endpoint}`, {
		headers
	});

	if (!response.ok) {
		const errorText = await response.text();
		console.error('Graph API error:', {
			status: response.status,
			statusText: response.statusText,
			error: errorText,
			endpoint
		});
		throw new Error(`Graph API error: ${response.statusText}`);
	}

	return response.json();
}

export async function fetchFolderContents(
	accessToken: string,
	driveId: string,
	fetch: typeof global.fetch,
	folderId?: string
): Promise<SimplifiedDriveItem[]> {
	try {
		let endpoint;
		if (folderId && folderId !== 'root') {
			endpoint = `/drives/${driveId}/items/${folderId}/children`;
		} else {
			endpoint = `/drives/${driveId}/root/children`;
		}

		const response: GraphResponse = await fetchFromGraph(accessToken, endpoint, fetch);

		return response.value.map((item) => ({
			id: item.id || '',
			name: item.name || '',
			folder: !!item.folder,
			size: item.size || 0,
			lastModifiedDateTime: item.lastModifiedDateTime || '',
			webUrl: item.webUrl || ''
		}));
	} catch (error) {
		console.error('Error fetching folder contents:', error);
		throw error;
	}
}

export async function fetchDriveItem(
	accessToken: string,
	driveId: string,
	itemId: string,
	fetch: typeof global.fetch
): Promise<SimplifiedDriveItem> {
	const endpoint = `/drives/${driveId}/items/${itemId}`;
	const item = await fetchFromGraph(accessToken, endpoint, fetch);

	return {
		id: item.id || '',
		name: item.name || '',
		folder: !!item.folder,
		size: item.size || 0,
		lastModifiedDateTime: item.lastModifiedDateTime || '',
		webUrl: item.webUrl || ''
	};
}

export async function fetchFileAccessMembers(
	accessToken: string,
	siteId: string,
	fileId: string,
	fetch: typeof global.fetch
): Promise<FileAccessMember[]> {
	try {
		// Step 1: Get file permissions
		const permissionsEndpoint = `/sites/${siteId}/drive/items/${fileId}/permissions`;
		const permissionsResponse: FilePermissionResponse = await fetchFromGraph(
			accessToken,
			permissionsEndpoint,
			fetch
		);

		const members: FileAccessMember[] = [];
		const processedGroups = new Set<string>();

		// Process each permission entry
		for (const permission of permissionsResponse.value) {
			const grantedTo = permission.grantedToV2;
			if (!grantedTo) continue;

			if (grantedTo.user) {
				// Direct user permission
				members.push({
					id: grantedTo.user.id,
					displayName: grantedTo.user.displayName,
					accessType: 'direct',
					roles: permission.roles
				});
			} else if (grantedTo.group && !processedGroups.has(grantedTo.group.id)) {
				// Microsoft 365 or Security group
				processedGroups.add(grantedTo.group.id);
				try {
					// First get the full group details
					const groupDetailsEndpoint = `/groups/${grantedTo.group.id}`;
					const groupDetails: GroupDetails = await fetchFromGraph(
						accessToken,
						groupDetailsEndpoint,
						fetch
					);

					// Then get the group members using the full group ID
					const groupMembersEndpoint = `/groups/${groupDetails.id}/members?$select=id,displayName,mail`;
					const groupMembersResponse: GroupMembersResponse = await fetchFromGraph(
						accessToken,
						groupMembersEndpoint,
						fetch
					);

					groupMembersResponse.value.forEach((member) => {
						members.push({
							id: member.id,
							displayName: member.displayName,
							email: member.mail,
							accessType: 'group',
							roles: permission.roles,
							groupName: groupDetails.displayName
						});
					});
				} catch (error) {
					console.error(`Error fetching details/members for group ${grantedTo.group.id}:`, error);
					// Add the group itself as a member since we can't fetch its members
					members.push({
						id: grantedTo.group.id,
						displayName: grantedTo.group.displayName,
						accessType: 'group',
						roles: permission.roles,
						groupName: 'Microsoft 365 Group'
					});
				}
			} else if (grantedTo.siteGroup && !processedGroups.has(grantedTo.siteGroup.id)) {
				// SharePoint group
				processedGroups.add(grantedTo.siteGroup.id);
				try {
					const siteGroupEndpoint = `/sites/${siteId}/siteGroups/${grantedTo.siteGroup.id}`;
					const siteGroupResponse = await fetchFromGraph(accessToken, siteGroupEndpoint, fetch);

					const siteGroupMembersEndpoint = `/sites/${siteId}/siteGroups/${grantedTo.siteGroup.id}/members`;
					const siteGroupMembersResponse: GroupMembersResponse = await fetchFromGraph(
						accessToken,
						siteGroupMembersEndpoint,
						fetch
					);

					if (siteGroupMembersResponse.value) {
						siteGroupMembersResponse.value.forEach((member) => {
							members.push({
								id: member.id,
								displayName: member.displayName,
								email: member.mail,
								accessType: 'group',
								roles: permission.roles,
								groupName: siteGroupResponse.displayName || grantedTo.siteGroup?.displayName
							});
						});
					} else {
						members.push({
							id: grantedTo.siteGroup.id,
							displayName: siteGroupResponse.displayName || grantedTo.siteGroup.displayName,
							accessType: 'group',
							roles: permission.roles,
							groupName: 'SharePoint Group'
						});
					}
				} catch (error) {
					console.error(`Error fetching members for site group ${grantedTo.siteGroup.id}:`, error);
					members.push({
						id: grantedTo.siteGroup.id,
						displayName: grantedTo.siteGroup.displayName,
						accessType: 'group',
						roles: permission.roles,
						groupName: 'SharePoint Group'
					});
				}
			}
		}

		// Remove duplicates based on user ID
		const uniqueMembers = Array.from(
			new Map(members.map((member) => [member.id, member])).values()
		);

		return uniqueMembers;
	} catch (error) {
		console.error('Error fetching file access members:', error);
		throw error;
	}
}

/**
 * Fetches users from a SharePoint site's User Information List
 * This includes Person objects and Group objects
 *
 * @param accessToken - Microsoft Graph API access token
 * @param siteId - The SharePoint site ID
 * @param fetch - Fetch function to use for API calls
 * @returns Promise with an array of SharePointSiteUser objects
 */
export async function fetchSharePointSiteUsers(
	accessToken: string,
	siteId: string,
	fetch: typeof global.fetch
): Promise<SharePointSiteUser[]> {
	try {
		console.log(`Fetching users from SharePoint site: ${siteId}`);

		// Get all items from the User Information List with fields expanded
		const endpoint = `/sites/${siteId}/lists/User Information List/items?expand=fields`;

		// Use the Prefer header to avoid issues with non-indexed fields
		const userInfoList = await fetchFromGraph(accessToken, endpoint, fetch, {
			Prefer: 'HonorNonIndexedQueriesWarningMayFailRandomly'
		});

		console.log(`Retrieved ${userInfoList.value?.length || 0} items from User Information List`);

		if (!userInfoList.value || userInfoList.value.length === 0) {
			console.log('No items found in User Information List');
			return [];
		}

		const siteUsers: SharePointSiteUser[] = [];

		// Process each item in the User Information List
		for (const item of userInfoList.value) {
			const fields = item.fields;
			const contentType = fields?.ContentType;

			// Skip if no fields or missing critical data
			if (!fields || !fields.id || !fields.Title) continue;

			if (contentType === 'Person') {
				// Skip system accounts and entries without email
				if (
					fields.Title.includes('System Account') ||
					fields.Title.startsWith('NT ') ||
					!fields.EMail
				) {
					continue;
				}

				siteUsers.push({
					id: fields.id,
					displayName: fields.Title,
					email: fields.EMail,
					userType: 'Person',
					isPerson: true,
					principalName: fields.Name
				});
			} else if (contentType === 'SharePointGroup') {
				siteUsers.push({
					id: fields.id,
					displayName: fields.Title,
					email: fields.EMail || `${fields.Title.replace(/\s+/g, '.')}@sharepoint`,
					userType: 'SharePointGroup',
					isPerson: false,
					principalName: fields.Name
				});
			} else if (contentType === 'DomainGroup') {
				siteUsers.push({
					id: fields.id,
					displayName: fields.Title,
					email: fields.EMail || `${fields.Title.replace(/\s+/g, '.')}@domain`,
					userType: 'DomainGroup',
					isPerson: false,
					principalName: fields.Name
				});
			}
		}

		console.log(`Processed ${siteUsers.length} users from SharePoint site`);
		return siteUsers;
	} catch (error) {
		console.error('Error fetching SharePoint site users:', error);
		throw error;
	}
}
