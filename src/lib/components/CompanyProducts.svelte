<script lang="ts">
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import ProductSelector from './ProductSelector.svelte';
	import { createEventDispatcher } from 'svelte';

	// Define product types
	interface Product {
		id: string;
		name: string;
		description: string;
		is_coming_soon?: boolean;
		category?: string;
		features?: string[];
	}

	interface CompanyProduct {
		id: string;
		company_id: string;
		product_id: string;
		is_enabled: boolean;
	}

	export let companyId: string;
	export let products: Product[] = [];
	export let companyProducts: CompanyProduct[] = [];
	export let useAccordion: boolean = true;

	// Setup event dispatcher
	const dispatch = createEventDispatcher();

	// Group products by category
	$: productCategories = products.reduce(
		(acc, product) => {
			const category = product.category || 'Other';
			if (!acc[category]) {
				acc[category] = [];
			}
			acc[category].push(product);
			return acc;
		},
		{} as Record<string, Product[]>
	);

	// Handle product toggle events
	function handleProductToggled(event) {
		dispatch('productToggled', event.detail);
	}
</script>

{#if Object.keys(productCategories).length === 0}
	<Card>
		<CardContent class="flex flex-col items-center justify-center p-6 space-y-4">
			<div class="p-4 rounded-full bg-muted">
				<svg
					xmlns="http://www.w3.org/2000/svg"
					width="24"
					height="24"
					viewBox="0 0 24 24"
					fill="none"
					stroke="currentColor"
					stroke-width="2"
					stroke-linecap="round"
					stroke-linejoin="round"
					class="text-muted-foreground"
				>
					<path d="M9.88 9.88a3 3 0 1 0 4.24 4.24" />
					<path d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68" />
					<path d="M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61" />
					<line x1="2" x2="22" y1="2" y2="22" />
				</svg>
			</div>
			<h3 class="text-lg font-medium">No Products Available</h3>
			<p class="text-sm text-center text-muted-foreground">
				There are no product options available at this time. Please contact support for assistance.
			</p>
		</CardContent>
	</Card>
{:else}
	<div class="grid gap-6">
		{#each Object.entries(productCategories) as [category, categoryProducts]}
			<Card>
				<CardHeader>
					<CardTitle>{category} Integrations</CardTitle>
					<CardDescription>
						Manage which {category.toLowerCase()} integrations are available for your company
					</CardDescription>
				</CardHeader>
				<CardContent>
					<ProductSelector
						{companyId}
						{products}
						{companyProducts}
						{category}
						on:productToggled={handleProductToggled}
					/>
				</CardContent>
			</Card>
		{/each}
	</div>
{/if}
