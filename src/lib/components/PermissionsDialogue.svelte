<script lang="ts">
	import { onMount } from 'svelte';
	import { createEventDispatcher } from 'svelte';
	import { transformPermissionsData } from '$lib/utils';
	import * as Dialog from '$lib/components/ui/dialog';
	import Button from './ui/button/button.svelte';
	import { Separator } from './ui/separator';
	import { Badge } from './ui/badge';
	import { UserIcon, UsersIcon } from 'lucide-svelte';
	import * as Tabs from './ui/tabs';
	import { ScrollArea } from './ui/scroll-area';

	const dispatch = createEventDispatcher();

	export let item;
	export let permissionsData;

	interface FileAccessMember {
		id: string;
		displayName: string;
		email?: string;
		accessType: 'direct' | 'group';
		roles: string[];
		groupName?: string;
	}

	let members: FileAccessMember[] = [];
	let directMembers: FileAccessMember[] = [];
	let groupMembers: FileAccessMember[] = [];
	let selectedTab = 'all';

	$: {
		if (Array.isArray(permissionsData)) {
			members = permissionsData;
			directMembers = members.filter((m) => m.accessType === 'direct');
			groupMembers = members.filter((m) => m.accessType === 'group');
		}
	}

	function getRoleBadgeVariant(role: string) {
		switch (role.toLowerCase()) {
			case 'owner':
				return 'destructive';
			case 'write':
				return 'default';
			case 'read':
				return 'secondary';
			default:
				return 'outline';
		}
	}
</script>

<Dialog.Root open>
	<Dialog.Content class="max-w-2xl">
		<Dialog.Header>
			<Dialog.Title>{item.name}</Dialog.Title>
			<Dialog.Description>Access Permissions</Dialog.Description>
		</Dialog.Header>

		<Tabs.Root value={selectedTab} onValueChange={(value) => (selectedTab = value)} class="w-full">
			<Tabs.List class="grid w-full grid-cols-3">
				<Tabs.Trigger value="all">
					All ({members.length})
				</Tabs.Trigger>
				<Tabs.Trigger value="direct">
					Direct Access ({directMembers.length})
				</Tabs.Trigger>
				<Tabs.Trigger value="groups">
					Via Groups ({groupMembers.length})
				</Tabs.Trigger>
			</Tabs.List>

			<ScrollArea class="h-[400px] w-full p-4">
				<Tabs.Content value="all" class="space-y-4">
					{#each members as member}
						<div class="flex items-start justify-between p-3 rounded-lg bg-secondary/10">
							<div class="flex items-start gap-3">
								{#if member.accessType === 'direct'}
									<UserIcon class="w-4 h-4 mt-1" />
								{:else}
									<UsersIcon class="w-4 h-4 mt-1" />
								{/if}
								<div>
									<p class="font-medium">{member.displayName}</p>
									{#if member.email}
										<p class="text-sm text-muted-foreground">{member.email}</p>
									{/if}
									{#if member.groupName}
										<p class="text-sm text-muted-foreground">Via: {member.groupName}</p>
									{/if}
								</div>
							</div>
							<div class="flex flex-wrap gap-2">
								{#each member.roles as role}
									<Badge variant={getRoleBadgeVariant(role)}>{role}</Badge>
								{/each}
							</div>
						</div>
					{/each}
				</Tabs.Content>

				<Tabs.Content value="direct" class="space-y-4">
					{#each directMembers as member}
						<div class="flex items-start justify-between p-3 rounded-lg bg-secondary/10">
							<div class="flex items-start gap-3">
								<UserIcon class="w-4 h-4 mt-1" />
								<div>
									<p class="font-medium">{member.displayName}</p>
									{#if member.email}
										<p class="text-sm text-muted-foreground">{member.email}</p>
									{/if}
								</div>
							</div>
							<div class="flex flex-wrap gap-2">
								{#each member.roles as role}
									<Badge variant={getRoleBadgeVariant(role)}>{role}</Badge>
								{/each}
							</div>
						</div>
					{/each}
				</Tabs.Content>

				<Tabs.Content value="groups" class="space-y-4">
					{#each groupMembers as member}
						<div class="flex items-start justify-between p-3 rounded-lg bg-secondary/10">
							<div class="flex items-start gap-3">
								<UsersIcon class="w-4 h-4 mt-1" />
								<div>
									<p class="font-medium">{member.displayName}</p>
									{#if member.email}
										<p class="text-sm text-muted-foreground">{member.email}</p>
									{/if}
									<p class="text-sm text-muted-foreground">Via: {member.groupName}</p>
								</div>
							</div>
							<div class="flex flex-wrap gap-2">
								{#each member.roles as role}
									<Badge variant={getRoleBadgeVariant(role)}>{role}</Badge>
								{/each}
							</div>
						</div>
					{/each}
				</Tabs.Content>
			</ScrollArea>
		</Tabs.Root>

		<Separator class="my-4" />

		<div class="flex justify-between">
			<Button href={item.webUrl} target="_blank" variant="outline">
				View {item.folder ? 'folder' : 'file'} on SharePoint
			</Button>
			<Button variant="outline" on:click={() => dispatch('close')}>Close</Button>
		</div>
	</Dialog.Content>
</Dialog.Root>
