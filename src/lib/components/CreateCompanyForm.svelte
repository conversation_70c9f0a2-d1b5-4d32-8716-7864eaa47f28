<script lang="ts">
	import { supabase } from '$lib/supabaseClient'; // Ensure you have the Supabase client set up
	import { createEventDispatcher } from 'svelte';
	import Button from './ui/button/button.svelte';

	const dispatch = createEventDispatcher();
	export let userId;
	let companyName = '';
	let inviteEmails = '';
	// let emailArray = []; // Array to hold the emails
	let errorMessage = '';
	let successMessage = '';

	const handleEmailInput = (event: KeyboardEvent) => {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault(); // Prevent default behavior (like form submission)
			const trimmedEmail = inviteEmails.trim();
			// if (trimmedEmail && !emailArray.includes(trimmedEmail)) {
			// 	emailArray.push(trimmedEmail); // Add email to the array
			// 	inviteEmails = ''; // Clear the input field
			// }
		}
	};

	const handleSubmit = async (event: SubmitEvent) => {
		event.preventDefault();
		errorMessage = '';
		successMessage = '';

		if (!companyName) {
			errorMessage = 'Please fill in all fields.';
			return;
		}

		try {
			// Create the company in your database
			const { data, error } = await supabase
				.from('companies') // Adjust the table name as necessary
				.insert([{ name: companyName, admin_uid: userId }]);

			if (error) throw error;

			// Assuming the company creation was successful, invite users
			// for (const email of emailArray) {
			// 	await supabase.auth.admin.inviteUserByEmail(email); // Adjust this method as necessary
			// }

			successMessage = 'Company created!';
			dispatch('companyCreated', { companyName }); // Dispatch an event if needed
		} catch (error) {
			errorMessage =
				error instanceof Error ? error.message : 'An error occurred while creating the company.';
		}
	};
</script>

<form on:submit={handleSubmit} class="space-y-4">
	<div>
		<label for="companyName" class="block text-sm font-medium">Company Name</label>
		<input
			type="text"
			id="companyName"
			bind:value={companyName}
			class="block w-full p-2 mt-1 border border-gray-300 rounded-md"
			placeholder="Enter company name"
		/>
	</div>
	<div>
		<!-- <label for="inviteEmails" class="block text-sm font-medium"
			>Invite Users (press Enter or Space to add)</label
		>
		<input
			type="text"
			id="inviteEmails"
			bind:value={inviteEmails}
			on:keydown={handleEmailInput}
			class="block w-full p-2 mt-1 border border-gray-300 rounded-md"
			placeholder="Enter email addresses"
		/>
		<ul>
			{#each emailArray as email}
				<li>{email}</li>
				
			{/each}
		</ul> -->
	</div>
	{#if errorMessage}
		<p class="text-red-500">{errorMessage}</p>
	{/if}
	{#if successMessage}
		<p class="text-green-500">{successMessage}</p>
	{/if}
	<Button type="submit" class="p-2 mt-4 text-white bg-blue-500 rounded-md">Create Company</Button>
</form>
