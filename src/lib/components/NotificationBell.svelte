<script lang="ts">
    import { Bell } from 'lucide-svelte';
    import Button from './ui/button/button.svelte';
    import * as Popover from '$lib/components/ui/popover';
    import { notifications, type Notification } from '$lib/stores/notifications';
    import { cn } from '$lib/utils';
    import { scale } from 'svelte/transition';

    let unreadCount = 0;
    
    $: {
        unreadCount = $notifications.filter(n => !n.read).length;
    }

    function handleNotificationClick(notification: Notification) {
        notifications.markAsRead(notification.id);
        if (notification.link) {
            window.location.href = notification.link;
        }
    }

    const transitionConfig = {
        duration: 200,
        y: -4,
        opacity: 0
    };
</script>

<Popover.Root portal={null}>
    <Popover.Trigger asChild let:builder>
        <Button
            variant="ghost"
            size="icon"
            class="relative aspect-square"
            builders={[builder]}
        >
            <Bell class="w-5 h-5" />
            {#if unreadCount > 0}
                <span 
                    class="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-red-500 text-[10px] font-medium text-white flex items-center justify-center"
                    transition:scale={{duration: 200}}
                >
                    {unreadCount}
                </span>
            {/if}
        </Button>
    </Popover.Trigger>
    
    <Popover.Content 
        class="w-80 p-4 z-[100]" 
        sideOffset={8}
    >
        <div class="space-y-4">
            <div class="flex items-center justify-between">
                <h4 class="font-semibold">Notifications</h4>
                {#if $notifications.length > 0}
                    <Button
                        variant="ghost"
                        size="sm"
                        on:click={() => notifications.markAllAsRead()}
                    >
                        Mark all as read
                    </Button>
                {/if}
            </div>
            
            {#if $notifications.length === 0}
                <p class="py-4 text-sm text-center text-muted-foreground">
                    No notifications
                </p>
            {:else}
                <div class="space-y-2 max-h-[300px] overflow-y-auto">
                    {#each $notifications as notification}
                        <button
                            class={cn(
                                "w-full text-left p-2 rounded-lg hover:bg-muted transition-colors",
                                !notification.read && "bg-muted/50"
                            )}
                            on:click={() => handleNotificationClick(notification)}
                        >
                            <div class="flex items-start gap-2">
                                <div class={cn(
                                    "w-2 h-2 mt-2 rounded-full",
                                    notification.type === 'error' && "bg-destructive",
                                    notification.type === 'warning' && "bg-warning",
                                    notification.type === 'success' && "bg-success",
                                    notification.type === 'info' && "bg-info"
                                )} />
                                <div class="flex-1 space-y-1">
                                    <p class="text-sm">{notification.message}</p>
                                    {#if notification.link}
                                        <p class="text-xs text-primary">
                                            {notification.linkText || 'Click to view'}
                                        </p>
                                    {/if}
                                    <p class="text-xs text-muted-foreground">
                                        {new Date(notification.createdAt).toLocaleDateString()}
                                    </p>
                                </div>
                            </div>
                        </button>
                    {/each}
                </div>
            {/if}
        </div>
    </Popover.Content>
</Popover.Root> 