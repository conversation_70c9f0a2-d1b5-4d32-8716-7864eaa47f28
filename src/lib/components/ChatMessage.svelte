<script lang="ts">
  import { marked } from 'marked';
  import type { Renderer, Token } from 'marked';
  
 marked.use({
  renderer: {
    link({ href, title, text }) {
      let validHref = href; // Initialize validHref with the provided href
      try {
        // Validate and encode the URL if needed
        if (validHref && !validHref.includes('%20')) {
          validHref = encodeURI(validHref);
        }
        new URL(validHref); // Check if it's a valid URL
      } catch (e) {
        console.warn('Invalid URL in markdown:', href, e);
        validHref = '#'; // Fallback for invalid URLs
      }

      // Determine the display text
      const displayText = text ? text.replace(/^_+|_+$/g, '') : validHref;

      // Return the formatted link
      return `<a href="${validHref}" title="${title || displayText}" target="_blank" rel="noopener noreferrer"><b><i>${displayText}</i></b></a>`;
    },
  },
});

  
  export let message: {
    type: 'question' | 'answer';
    text: string;
    relevantSource?: {
      metadata?: {
        file_name: string;
      };
      content: string;
    };
    sources?: Array<any>;
  };
  
  export let onViewSources: (sources: Array<any>) => void;
  
  function markdownToHtml(markdown: string) {
    return marked(markdown);
  }
</script>

<div class="w-full message">
  {#if message.type === 'question'}
    <div class="flex justify-end">
      <div class="max-w-[80%] p-4 text-white rounded-xl rounded-tr-none bg-[hsl(var(--primary))] shadow-md">
        {message.text}
      </div>
    </div>
  {:else if message.type === 'answer'}
    <div class="flex items-start max-w-[80%]">
      <div class="flex flex-col w-full gap-4">
        <div class="p-4 prose-sm prose bg-[hsl(var(--card))] text-[hsl(var(--card-foreground))] rounded-xl rounded-tl-none break-words shadow-md">
          {@html markdownToHtml(message.text)}
        </div>
        {#if message.relevantSource}
          <div class="w-full p-4 text-sm bg-[hsl(var(--muted))] text-[hsl(var(--muted-foreground))] rounded-xl break-words rounded-tl-none shadow-sm hover:shadow-md transition-shadow duration-200">
            <span class="font-semibold">{message.relevantSource?.metadata?.file_name}</span>
            <p class="mt-1 overflow-hidden text-ellipsis line-clamp-3">
              {message.relevantSource?.content}
            </p>
            <button
              class="mt-2 text-[hsl(var(--primary))] hover:underline focus:outline-none focus:ring-2 focus:ring-[hsl(var(--primary))] focus:ring-offset-2 rounded-sm"
              on:click={() => onViewSources(message.sources ?? [])}
            >
              View Sources
            </button>
          </div>
        {/if}
      </div>
    </div>
  {/if}
</div>
