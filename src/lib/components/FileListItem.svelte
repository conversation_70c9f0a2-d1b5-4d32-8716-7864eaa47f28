<script lang="ts">
	import {
		FileIcon,
		FolderIcon,
		Trash2Icon,
		UploadIcon,
		FileTextIcon,
		EllipsisVertical
	} from 'lucide-svelte';
	import type { SharePointFile } from '$lib/types';
	import ItemActions from './ItemActions.svelte';
	import * as Tooltip from '$lib/components/ui/tooltip';

	export let sourceId;
	export let file: SharePointFile;
	export let onFolderClick: () => void;
	export let user;
	export let isIngested: boolean;
	$: isFolder = !!file.folder;

	const handleClick = () => {
		if (isFolder) {
			onFolderClick();
		} else if (file.webUrl) {
			// Open SharePoint file in a new tab
			window.open(file.webUrl, '_blank');
		}
	};

	function formatFileSize(bytes: number): string {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	}
</script>

<div
	class="flex flex-row items-center justify-between w-full min-w-0 p-2 rounded-md hover:bg-secondary/10"
>
	<div class="flex items-baseline flex-grow min-w-0 gap-2">
		{#if isFolder}
			<button on:click={handleClick} class="flex-none w-4 h-4">
				<FolderIcon class="w-4 h-4" />
			</button>
		{:else if !isFolder && isIngested}
			<button on:click={handleClick} class="flex-none w-4 h-4 -ml-1">
				<span>☑️</span>
			</button>
		{:else}
			<button on:click={handleClick} class="flex-none w-4 h-4">
				<FileIcon class="w-4 h-4" />
			</button>
		{/if}
		<div class="flex-grow min-w-0">
			<Tooltip.Root>
				<Tooltip.Trigger>
					<span
						on:click={handleClick}
						class="block w-full overflow-hidden whitespace-nowrap text-ellipsis text-sm font-medium text-left {!isFolder
							? 'cursor-pointer'
							: ''}"
						tabindex="0"
						role="button"
						aria-label="Open file"
					>
						{file.name}
					</span>
				</Tooltip.Trigger>
				<Tooltip.Content>
					{file.name}
				</Tooltip.Content>
			</Tooltip.Root>
		</div>
	</div>
	<div class="flex flex-col items-end flex-shrink-0 pr-4 text-right w-28">
		<span class="text-xs truncate text-muted-foreground">
			{new Date(file.lastModifiedDateTime).toLocaleString()}
		</span>
		<span class="text-xs truncate text-muted-foreground">
			{formatFileSize(file.size)}
		</span>
	</div>
	<div class="flex-shrink-0">
		<ItemActions class="w-6" {file} {sourceId} {user} {isIngested} />
	</div>
</div>
