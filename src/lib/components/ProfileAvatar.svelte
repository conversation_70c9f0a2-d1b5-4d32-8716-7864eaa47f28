<script lang="ts">
	import {
		Avatar,
		AvatarFallback,
		AvatarImage
	} from '$lib/components/ui/avatar/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import {
		DropdownMenu,
		DropdownMenuContent,
		DropdownMenuItem,
		DropdownMenuLabel,
		DropdownMenuSeparator,
		DropdownMenuTrigger
	} from '$lib/components/ui/dropdown-menu/index.js';
	import { page } from '$app/stores';
	import { signOut } from '$lib/utils/auth';
	import { onMount } from 'svelte';
	import { forceUiUpdate } from '$lib/utils/uiHelpers';
	import { goto } from '$app/navigation';

	export let avatarAlt = 'User';

	// Get user data from the page store
	$: user = $page.data.user || null;
	$: profile = $page.data.profile || null;
	
	// Get company ID and user role from the page store
	$: companyId = $page.params.companyId;
	$: userRole = $page?.data?.userRole;
	
	// Whether user is an admin
	$: isAdmin = userRole === 'admin';

	// Create a unique ID for this menu to prevent conflicts
	const menuId = `profile-dropdown-${Math.random().toString(36).substring(2, 9)}`;
</script>

<DropdownMenu>
	<DropdownMenuTrigger asChild let:builder class="hover:cursor-pointer focus:outline-none focus:ring-0">
		<Button builders={[builder]} variant="ghost" size="icon" class="relative w-8 h-8 rounded-full">
			<Avatar class="w-8 h-8">
				{#if profile?.avatar_url}
					<AvatarImage src={profile.avatar_url} alt={avatarAlt} />
				{/if}
				<AvatarFallback>
					{#if user?.email}
						{user.email[0].toUpperCase()}
					{:else}
						U
					{/if}
				</AvatarFallback>
			</Avatar>
		</Button>
	</DropdownMenuTrigger>
	<DropdownMenuContent class="w-56" align="end">
		<DropdownMenuLabel class="font-normal">
			<div class="flex flex-col space-y-1">
				<p class="text-sm font-medium leading-none">{profile?.full_name || 'User'}</p>
				<p class="text-xs leading-none text-muted-foreground">
					{user?.email || '<EMAIL>'}
				</p>
			</div>
		</DropdownMenuLabel>
		<DropdownMenuSeparator />
		{#if companyId}
			{#if isAdmin}
			<DropdownMenuItem on:click={() => goto(`/company/${companyId}/manage-company`)} class="cursor-pointer">
				Manage Company
			</DropdownMenuItem>
			{/if}
		{/if}
		<DropdownMenuItem on:click={() => goto('/profile')} class="cursor-pointer"> Profile </DropdownMenuItem>
		<DropdownMenuItem on:click={signOut} class="cursor-pointer"> Log out </DropdownMenuItem>
	</DropdownMenuContent>
</DropdownMenu>
