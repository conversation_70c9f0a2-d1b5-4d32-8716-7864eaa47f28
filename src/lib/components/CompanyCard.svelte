<!-- src/lib/components/CompanyCard.svelte -->
<script lang="ts">
	import { onMount } from 'svelte';
	import * as Card from '$lib/components/ui/card';
	import { goto } from '$app/navigation';
	import type { Database } from '../../../database.types';
	import { Badge } from '$lib/components/ui/badge';
	import CompanyActions from '$lib/components/CompanyActions.svelte';
	import Separator from './ui/separator/separator.svelte';
	import { createEventDispatcher } from 'svelte';
	import { toast } from 'svelte-sonner';
	import { page } from '$app/stores';
	import { mode } from 'mode-watcher';

	export let company: Database['public']['Tables']['companies']['Row'];
	
	let { supabase, user } = $page.data
	const dispatch = createEventDispatcher();

	// Add reactive statement for icon visibility
	$: isDark = $mode === 'dark';

	function selectCompany() {
		goto(`${company.id}`);
	}

	// Format date to be more readable
	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});
	};

	// Calculate progress percentage
	$: timeAllocated = company.transcription_time_allocated || 0;
	$: timeUsed = company.transcription_time_used || 0;
	$: progressPercentage = timeAllocated > 0 ? (timeUsed / timeAllocated) * 100 : 0;

	type StatusVariant = 'secondary' | 'default' | 'destructive' | 'outline';

	// Get status variant
	$: statusVariant = (company.status === 'trial' 
		? 'secondary'
		: company.status === 'paid'
		? 'default'
		: company.status === 'suspended'
		? 'destructive'
		: 'outline') satisfies StatusVariant;

	function handleCompanyDelete(event: CustomEvent) {
		dispatch('companyDelete', event.detail);
	}
</script>
<a href={`/company/${company.id}`}>

	<Card.Root 
		on:click={selectCompany} 
		class="relative p-4 transition-all duration-300 border rounded-lg cursor-pointer company-card"
	>
	<button class="absolute z-10 flex gap-2 top-4 right-4" on:click|stopPropagation>
		<CompanyActions 
			{company} 
			on:companyDelete={handleCompanyDelete}
		/>
	</button>
	
	<Card.Header class="flex flex-col items-start">
		<Card.Title class="text-xl font-semibold">{company.name}</Card.Title>
		<Card.Description>
		</Card.Description>
	</Card.Header>
	
	<Card.Content class="space-y-3">
		<div class="space-y-2">
			<div class="flex flex-col justify-between text-sm">
				<span class="text-xs text-muted-foreground">Transcription Usage</span>
				<span class="text-sm font-medium">{timeUsed}/{timeAllocated} mins</span>
			</div>
			<div class="relative w-full h-2 overflow-hidden rounded-full bg-secondary dark:bg-tertiary">
				<div
					class="h-full transition-all duration-200 bg-primary dark:bg-[var(--teal-accent)]"
					style="width: {progressPercentage}%"
				/>
			</div>
		</div>
		
		<Separator class="dark:opacity-10" />
		<p class="text-xs text-muted-foreground">Created {formatDate(company.created_at)}</p>
		<Badge 
			variant={statusVariant} 
			class="transition-colors duration-200"
		>
			{company.status}
		</Badge>
	</Card.Content>
</Card.Root>

</a>