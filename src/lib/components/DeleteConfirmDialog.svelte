<script lang="ts">
    import * as AlertDialog from "$lib/components/ui/alert-dialog";
    import { createEventDispatcher } from 'svelte';

    export let title = "Delete Confirmation";
    export let description = "Are you sure? This action cannot be undone.";
    export let cancelText = "Cancel";
    export let confirmText = "Delete";
    export let open = false;

    const dispatch = createEventDispatcher();

    function handleConfirm() {
        dispatch('confirm');
        open = false;
    }

    function handleCancel() {
        dispatch('cancel');
        open = false;
    }
</script>

<AlertDialog.Root bind:open>
    <AlertDialog.Content>
        <AlertDialog.Header>
            <AlertDialog.Title>{title}</AlertDialog.Title>
            <AlertDialog.Description>
                {description}
            </AlertDialog.Description>
        </AlertDialog.Header>
        <AlertDialog.Footer>
            <AlertDialog.Cancel on:click={handleCancel}>{cancelText}</AlertDialog.Cancel>
            <AlertDialog.Action on:click={handleConfirm} class="bg-destructive hover:bg-destructive/90">
                {confirmText}
            </AlertDialog.Action>
        </AlertDialog.Footer>
    </AlertDialog.Content>
</AlertDialog.Root> 