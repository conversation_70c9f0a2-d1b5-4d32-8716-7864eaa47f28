import type { Infer } from "sveltekit-superforms";
import { z } from "zod";

export const formSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  phone: z.string().regex(
    /^\d{11}$/,
    "Phone number must be 11 digits (including country code, e.g. 44123456789)",
  ),
  userId: z.string().uuid(),
  role: z.enum(["admin", "expert", "member"], {
    required_error: "Please select a role",
  }),
});

export type FormSchema = typeof formSchema;
