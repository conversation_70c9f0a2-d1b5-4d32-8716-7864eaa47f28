<script lang="ts">
	import { superForm } from "sveltekit-superforms/client";
	import { formSchema } from "./schema";
	import { Button } from "$lib/components/ui/button";
	import { Input } from "$lib/components/ui/input";
	import { Label } from "$lib/components/ui/label";
	import { cn } from "$lib/utils";
	import type { SuperValidated } from "sveltekit-superforms";
	import type { FormSchema } from "./schema";
	import * as Select from "$lib/components/ui/select";

	export let data: SuperValidated<FormSchema>;
	export let user: {
		user_id: string;
		user_email: string | null;
		user_phone_number: string | null;
		role?: string;
	};

	console.log("EditContactForm mounted with user:", user);

	const { form, errors, enhance } = superForm(data, {
		id: "contact-form",
		taintedMessage: null,
		resetForm: false,
		onUpdate: ({ form }) => {
			console.log("Form updated:", form);
		},
		onSubmit: ({ formData }) => {
			console.log("Form submitting with data:", formData);
			return true;
		},
		onResult: ({ result }) => {
			console.log("Form submission result:", result);
		}
	});

	// Set form values when user data changes
	$: if (user && $form) {
		console.log("EditContactForm received user data:", {
			userId: user.user_id,
			email: user.user_email,
			phone: user.user_phone_number,
			role: user.role || 'member'
		});
		console.log("Current form values before update:", $form);

		// Update form values with user data
		$form.userId = user.user_id;
		$form.email = user.user_email || '';
		$form.phone = user.user_phone_number || '';
		$form.role = (user.role as "admin" | "expert" | "member") || 'member';

		console.log("Updated form values:", $form);
	}
</script>

<form method="POST" action="?/updateContactInfo" use:enhance>
	<input type="hidden" name="userId" bind:value={$form.userId} />
	<input type="hidden" name="role" bind:value={$form.role} />
	
	<div class="space-y-4">
		<div class="space-y-2">
			<Label for="email">Email</Label>
			<Input
				id="email"
				name="email"
				type="email"
				bind:value={$form.email}
				class={cn(
					$errors.email && "border-red-500 focus-visible:ring-red-500"
				)}
			/>
			{#if $errors.email}
				<p class="text-sm text-red-500">{$errors.email}</p>
			{/if}
		</div>

		<div class="space-y-2">
			<Label for="phone">Phone Number</Label>
			<Input
				id="phone"
				name="phone"
				type="tel"
				bind:value={$form.phone}
				class={cn(
					$errors.phone && "border-red-500 focus-visible:ring-red-500"
				)}
			/>
			{#if $errors.phone}
				<p class="text-sm text-red-500">{$errors.phone}</p>
			{/if}
		</div>

		<div class="space-y-2">
			<Label for="role">Role</Label>
			<Select.Root
				selected={$form.role ? { value: $form.role, label: $form.role === 'admin' ? 'Admin' : $form.role === 'expert' ? 'Expert' : 'Member' } : undefined}
				onSelectedChange={(selected) => {
					if (selected) {
						$form.role = selected.value;
					}
				}}
			>
				<Select.Trigger class={cn(
					"w-full",
					$errors.role && "border-red-500 focus-visible:ring-red-500"
				)}>
					<Select.Value placeholder="Select a role" />
				</Select.Trigger>
				<Select.Content>
					<Select.Item value="admin">Admin</Select.Item>
					<Select.Item value="expert">Expert</Select.Item>
					<Select.Item value="member">Member</Select.Item>
				</Select.Content>
			</Select.Root>
			{#if $errors.role}
				<p class="text-sm text-red-500">{$errors.role}</p>
			{/if}
		</div>

		<Button type="submit" class="w-full">Save Changes</Button>
	</div>
</form> 