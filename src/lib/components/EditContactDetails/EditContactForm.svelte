<script lang="ts">
	import { superForm } from "sveltekit-superforms/client";
	import { formSchema } from "./schema";
	import { Button } from "$lib/components/ui/button";
	import { Input } from "$lib/components/ui/input";
	import { Label } from "$lib/components/ui/label";
	import { cn } from "$lib/utils";
	import type { SuperValidated } from "sveltekit-superforms";
	import type { FormSchema } from "./schema";
	import * as Select from "$lib/components/ui/select";

	export let data: SuperValidated<FormSchema>;
	export let user: {
		user_id: string;
		user_email: string | null;
		user_phone_number: string | null;
		role?: string;
	};

	console.log("EditContactForm mounted with user:", user);

	const { form, errors, enhance } = superForm(data, {
		id: "contact-form",
		taintedMessage: null,
		resetForm: false,
		onUpdate: ({ form }) => {
			console.log("Form updated:", form);
		},
		onSubmit: ({ formData }) => {
			console.log("Form submitting with data:", formData);
			return true;
		},
		onResult: ({ result }) => {
			console.log("Form submission result:", result);
		}
	});

	// Set initial values when the component mounts or user prop changes
	$: if (user) {
		console.log("Setting form values from user:", {
			userId: user.user_id,
			email: user.user_email,
			phone: user.user_phone_number,
			role: user.role || 'member'
		});
		$form = {
			userId: user.user_id,
			email: user.user_email || '',
			phone: user.user_phone_number || '',
			role: (user.role as "admin" | "expert" | "member") || 'member'
		};
	}
</script>

<form method="POST" action="?/updateContactInfo" use:enhance>
	<input type="hidden" name="userId" bind:value={$form.userId} />
	<input type="hidden" name="role" bind:value={$form.role} />
	
	<div class="space-y-4">
		<div class="space-y-2">
			<Label for="email">Email</Label>
			<Input
				id="email"
				name="email"
				type="email"
				bind:value={$form.email}
				class={cn(
					$errors.email && "border-red-500 focus-visible:ring-red-500"
				)}
			/>
			{#if $errors.email}
				<p class="text-sm text-red-500">{$errors.email}</p>
			{/if}
		</div>

		<div class="space-y-2">
			<Label for="phone">Phone Number</Label>
			<Input
				id="phone"
				name="phone"
				type="tel"
				bind:value={$form.phone}
				class={cn(
					$errors.phone && "border-red-500 focus-visible:ring-red-500"
				)}
			/>
			{#if $errors.phone}
				<p class="text-sm text-red-500">{$errors.phone}</p>
			{/if}
		</div>

		<div class="space-y-2">
			<Label for="role">Role</Label>
			<Select.Root value={$form.role} onSelectedChange={(value) => $form.role = value?.value}>
				<Select.Trigger class={cn(
					"w-full",
					$errors.role && "border-red-500 focus-visible:ring-red-500"
				)}>
					<Select.Value placeholder="Select a role">
						{$form.role === 'admin' ? 'Admin' : $form.role === 'expert' ? 'Expert' : 'Member'}
					</Select.Value>
				</Select.Trigger>
				<Select.Content>
					<Select.Item value="admin">Admin</Select.Item>
					<Select.Item value="expert">Expert</Select.Item>
					<Select.Item value="member">Member</Select.Item>
				</Select.Content>
			</Select.Root>
			{#if $errors.role}
				<p class="text-sm text-red-500">{$errors.role}</p>
			{/if}
		</div>

		<Button type="submit" class="w-full">Save Changes</Button>
	</div>
</form> 