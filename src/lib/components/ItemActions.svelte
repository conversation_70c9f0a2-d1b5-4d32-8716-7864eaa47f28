<script lang="ts">
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import {
		UploadIcon,
		FileTextIcon,
		Trash2Icon,
		EllipsisVertical,
		ExternalLinkIcon
	} from 'lucide-svelte';
	import { cn } from '$lib/utils.js';
	import PermissionsDialogue from '$lib/components/PermissionsDialogue.svelte';
	import Label from './ui/label/label.svelte';
	import Switch from './ui/switch/switch.svelte';
	import { toast } from 'svelte-sonner';
	import { enhance } from '$app/forms';
	import type { ActionResult, SubmitFunction } from '@sveltejs/kit';
	import { invalidateAll } from '$app/navigation';

	export let file;
	export let sourceId;
	export let user;
	export let isIngested: boolean;
	let className: string | undefined = undefined;
	export { className as class };

	let showPermissionsDialogue = false;
	let permissions: any[] = [];

	type FormResult = ActionResult<{
		success?: boolean;
		message?: string;
		[key: string]: unknown;
	}>;

	const handleViewPermissionDetails = async () => {
		const formData = new FormData();
		formData.append('fileId', file.id);

		try {
			const response = await fetch('?/getPermissions', {
				method: 'POST',
				body: formData
			});
			const result = await response.json();

			if (result.success) {
				permissions = result.permissions;
				showPermissionsDialogue = true;
			} else {
				throw new Error(result.message);
			}
		} catch (error) {
			console.error('Error fetching permissions:', error);
			toast.error('Failed to fetch permissions');
		}
	};

	const handleOpenInSharePoint = () => {
		if (file.webUrl) {
			window.open(file.webUrl, '_blank');
		}
	};

	const handleDelete: SubmitFunction = () => {
		const toastId = toast.loading(`Removing ${file.name} from TOTM Knowledge...`, {
			duration: Infinity
		});

		return async ({ result }: { result: FormResult }) => {
			if (result.type === 'success') {
				toast.success(result.data?.message || 'Success', {
					id: toastId,
					duration: 2000
				});
			} else if (result.type === 'failure') {
				toast.error(result.data?.message || 'An error occurred', {
					id: toastId,
					duration: 3000
				});
			} else if (result.type === 'error') {
				toast.error(result.error?.message || 'An error occurred', {
					id: toastId,
					duration: 3000
				});
			} else {
				toast.error('An unexpected error occurred', {
					id: toastId,
					duration: 3000
				});
			}
		};
	};

	const handleIngest: SubmitFunction = () => {
		const toastId = toast.loading(`Adding ${file.name} to TOTM Knowledge...`, {
			duration: Infinity
		});

		return async ({ result }: { result: FormResult }) => {
			if (result.type === 'success') {
				toast.success(result.data?.message || 'Success', {
					id: toastId,
					duration: 2000
				});
			} else if (result.type === 'failure') {
				toast.error(result.data?.message || 'An error occurred', {
					id: toastId,
					duration: 3000
				});
			} else if (result.type === 'error') {
				toast.error(result.error?.message || 'An error occurred', {
					id: toastId,
					duration: 3000
				});
			} else {
				toast.error('An unexpected error occurred', {
					id: toastId,
					duration: 3000
				});
			}
			invalidateAll();
		};
	};
</script>

<div class={cn('relative inline-block', className)}>
	<DropdownMenu.Root>
		<DropdownMenu.Trigger
			class="flex items-center justify-center w-8 h-8 transition-colors rounded-full hover:bg-secondary/80"
		>
			<EllipsisVertical class="w-4 h-4" />
		</DropdownMenu.Trigger>
		<DropdownMenu.Content align="end" class="w-auto">
			{#if !file.folder && file.webUrl}
				<DropdownMenu.Item class="w-full">
					<button
						class="flex items-center w-full gap-2 whitespace-nowrap"
						on:click={handleOpenInSharePoint}
					>
						<ExternalLinkIcon class="flex-shrink-0 w-4 h-4" />
						<span>Open in SharePoint</span>
					</button>
				</DropdownMenu.Item>
				<DropdownMenu.Separator />
			{/if}

			<form method="POST" action="?/ingest" class="contents" use:enhance={handleIngest}>
				<input type="hidden" name="fileId" value={file.id} />
				<input type="hidden" name="fileName" value={file.name} />
				<DropdownMenu.Item disabled={isIngested} class="w-full">
					<button
						class="flex items-center w-full gap-2 whitespace-nowrap"
						type="submit"
						disabled={isIngested}
					>
						<UploadIcon class="flex-shrink-0 w-4 h-4" />
						<span>Add to TOTM Knowledge</span>
					</button>
				</DropdownMenu.Item>
			</form>

			<form method="POST" action="?/delete" class="contents" use:enhance={handleDelete}>
				<input type="hidden" name="fileId" value={file.id} />
				<input type="hidden" name="fileName" value={file.name} />
				<DropdownMenu.Item disabled={!isIngested} class="w-full">
					<button
						class="flex items-center w-full gap-2 whitespace-nowrap"
						type="submit"
						disabled={!isIngested}
					>
						<Trash2Icon class="flex-shrink-0 w-4 h-4" />
						<span>Remove from TOTM Knowledge</span>
					</button>
				</DropdownMenu.Item>
			</form>

			<DropdownMenu.Item class="w-full">
				<button
					class="flex items-center w-full gap-2 whitespace-nowrap"
					on:click={handleViewPermissionDetails}
				>
					<FileTextIcon class="flex-shrink-0 w-4 h-4" />
					<span>View Permissions</span>
				</button>
			</DropdownMenu.Item>
		</DropdownMenu.Content>
	</DropdownMenu.Root>

	{#if showPermissionsDialogue}
		<PermissionsDialogue
			item={file}
			permissionsData={permissions}
			on:close={() => (showPermissionsDialogue = false)}
		/>
	{/if}
</div>
