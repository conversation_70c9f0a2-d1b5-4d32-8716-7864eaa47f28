<script lang="ts">
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { Button } from '$lib/components/ui/button';
	import {
		FileSearch,
		ExternalLink,
		Trash2,
		MoreVertical,
		UploadIcon,
		FileTextIcon,
		FlaskConicalIcon
	} from 'lucide-svelte';
	import { toast } from 'svelte-sonner';
	import { createEventDispatcher } from 'svelte';
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';

	export let datasource: {
		id: string;
		type?: string;
		name?: string;
		webUrl: string | null;
		drive_id?: string;
		site_id?: string;
		display_name?: string;
		platform_type?: string;
		company_id?: string;
	};
	export let user: any;

	let className = '';
	export { className as class };

	let companyId = datasource.company_id || $page.params.companyId;
	let supabase = $page.data?.supabase;
	const dispatch = createEventDispatcher<{
		datasourceDelete: { datasourceId: string };
	}>();

	// Ensure we have values for type and name, even if they come from different fields
	$: sourceType = datasource.type || datasource.platform_type || 'unknown';
	$: sourceName = datasource.name || datasource.display_name || 'Unnamed Source';

	const handleDelete = async () => {
		if (
			!confirm(
				`Are you sure you want to remove "${sourceName}" from your vault? This will not delete the data from its original source.`
			)
		) {
			return;
		}

		const toastId = toast.loading(`Removing ${sourceName}...`);
		try {
			// First try using Supabase if available
			if (supabase) {
				const { error } = await supabase.from('data_sources').delete().eq('id', datasource.id);
				if (error) throw new Error('Failed to delete datasource');
			} else {
				// Fallback to fetch API
				const response = await fetch(`/api/datasources/${datasource.id}`, {
					method: 'DELETE',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({ userId: user.id })
				});

				if (!response.ok) {
					const data = await response.json();
					throw new Error(data.message || 'Failed to delete data source');
				}
			}

			toast.success(`Successfully removed ${sourceName}`, { id: toastId });
			dispatch('datasourceDelete', { datasourceId: datasource.id });
		} catch (error) {
			console.error('Error deleting datasource:', error);
			toast.error(`Failed to remove ${sourceName}`, { id: toastId });
		}
	};

	const handleIngest = async () => {
		const toastId = toast.loading(`Adding ${sourceName} to TOTM Knowledge...`);
		try {
			// Call your ingest API endpoint
			const response = await fetch(`/api/datasources/${datasource.id}/ingest`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ userId: user.id })
			});

			if (!response.ok) {
				throw new Error('Failed to start ingestion process');
			}

			toast.success(`Successfully added ${sourceName} to TOTM Knowledge`, { id: toastId });
		} catch (error) {
			console.error('Error ingesting datasource:', error);
			toast.error(`Failed to add ${sourceName} to TOTM Knowledge`, { id: toastId });
		}
	};

	const removeFromKB = async () => {
		const toastId = toast.loading(`Removing ${sourceName} from TOTM Knowledge...`);
		try {
			// Call your remove API endpoint
			const response = await fetch(`/api/datasources/${datasource.id}/remove-kb`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ userId: user.id })
			});

			if (!response.ok) {
				throw new Error('Failed to remove from knowledge base');
			}

			toast.success(`Successfully removed ${sourceName} from TOTM Knowledge`, { id: toastId });
		} catch (error) {
			console.error('Error removing from knowledge base:', error);
			toast.error(`Failed to remove ${sourceName} from TOTM Knowledge`, { id: toastId });
		}
	};

	function browseDatasource() {
		goto(`/company/${companyId}/vault/${datasource.id}/root`);
	}

	function openExternalUrl() {
		if (datasource.webUrl) {
			window.open(datasource.webUrl, '_blank');
		}
	}

	function gotoRetrievalTest() {
		goto(`/company/${companyId}/vault/${datasource.id}/retrieval`);
	}
</script>

<div class={className}>
	<DropdownMenu.Root>
		<DropdownMenu.Trigger asChild let:builder>
			<Button variant="ghost" size="icon" class="h-8 w-8" builders={[builder]}>
				<MoreVertical class="h-5 w-5" />
				<span class="sr-only">Options</span>
			</Button>
		</DropdownMenu.Trigger>
		<DropdownMenu.Content align="end" class="min-w-[200px]">
			<DropdownMenu.Label>Actions</DropdownMenu.Label>
			<DropdownMenu.Separator />

			<DropdownMenu.Item class="cursor-pointer" on:click={browseDatasource}>
				<FileSearch class="w-4 h-4 mr-2" />
				Browse Files
			</DropdownMenu.Item>

			{#if datasource.webUrl}
				<DropdownMenu.Item class="cursor-pointer" on:click={openExternalUrl}>
					<ExternalLink class="w-4 h-4 mr-2" />
					Open in {sourceType}
				</DropdownMenu.Item>
			{/if}

			<DropdownMenu.Separator />

			<DropdownMenu.Item class="cursor-pointer" on:click={handleIngest}>
				<UploadIcon class="w-4 h-4 mr-2" />
				Add to TOTM Knowledge
			</DropdownMenu.Item>

			<DropdownMenu.Item class="cursor-pointer" on:click={removeFromKB}>
				<Trash2 class="w-4 h-4 mr-2 text-amber-500" />
				Remove from TOTM Knowledge
			</DropdownMenu.Item>

			<DropdownMenu.Item class="cursor-pointer" on:click={gotoRetrievalTest}>
				<FlaskConicalIcon class="w-4 h-4 mr-2" />
				Test Retrieval
			</DropdownMenu.Item>

			<DropdownMenu.Separator />

			<DropdownMenu.Item
				class="cursor-pointer text-destructive focus:text-destructive"
				on:click={handleDelete}
			>
				<Trash2 class="w-4 h-4 mr-2" />
				Remove Connection
			</DropdownMenu.Item>
		</DropdownMenu.Content>
	</DropdownMenu.Root>
</div>
