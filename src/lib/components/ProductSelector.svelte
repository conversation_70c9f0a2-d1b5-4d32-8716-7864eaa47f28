<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { Button } from '$lib/components/ui/button';
	import * as Card from '$lib/components/ui/card';
	import { Switch } from '$lib/components/ui/switch';
	import { Loader2 } from 'lucide-svelte';
	import { onMount } from 'svelte';
	import { toast } from 'svelte-sonner';
	import { Badge } from '$lib/components/ui/badge';

	// Define product types
	interface Product {
		id: string;
		name: string;
		description: string;
		is_coming_soon?: boolean;
		category?: string;
		categories?: string[];
		features?: string[];
		requires_product_id?: string;
	}

	interface CompanyProduct {
		id: string;
		company_id: string;
		product_id: string;
		is_enabled: boolean;
	}

	export let companyId: string;
	export let products: Product[] = [];
	export let companyProducts: CompanyProduct[] = [];
	export let category: string = 'Document Storage';
	export let compact: boolean = false;

	let loading: Record<string, boolean> = {};
	let error: string | null = null;

	// Create event dispatcher for product toggle events
	const dispatch = createEventDispatcher<{
		productToggled: { productId: string; enabled: boolean };
	}>();

	// Filter products by category, supporting both the old category field and the new categories array
	$: filteredProducts = category
		? products.filter((product) => {
				// Check single category field (backward compatibility)
				if (product.category === category) return true;

				// Check categories array if it exists
				if (product.categories && Array.isArray(product.categories)) {
					return product.categories.includes(category);
				}

				return false;
			})
		: products;

	// Create a map of product ID to enabled status
	$: productStatusMap = Object.fromEntries(
		companyProducts.map((cp) => [cp.product_id, cp.is_enabled])
	);

	// Local state to track UI changes before they're confirmed by the server
	let localUIState: Record<string, boolean> = {};

	// Check if a product is enabled - prioritize local UI state over derived state
	function isProductEnabled(productId: string): boolean {
		// If we have a local override, use that
		if (productId in localUIState) {
			return localUIState[productId];
		}
		// Otherwise fall back to the derived state
		return !!productStatusMap[productId];
	}

	// Check if a product's requirements are met (required product is enabled)
	function productRequirementsMet(product: Product): boolean {
		if (!product.requires_product_id) return true;
		return isProductEnabled(product.requires_product_id);
	}

	// Get product logo by name
	function getProductLogo(productName: string): string {
		// Use the name instead of ID to match logo
		const normalizedName = productName.toLowerCase().replace(/\s+/g, '');

		if (normalizedName.includes('sharepoint')) {
			return '/product-logos/sharepoint-logo.svg';
		} else if (normalizedName.includes('onedrive')) {
			return '/product-logos/onedrive-logo.svg';
		} else if (normalizedName.includes('googledrive') || normalizedName.includes('google drive')) {
			return '/product-logos/googledrive-logo.svg';
		} else if (normalizedName.includes('dropbox')) {
			return '/product-logos/dropbox-logo.svg';
		} else if (normalizedName.includes('slack')) {
			return '/product-logos/slack-logo.svg';
		} else if (normalizedName.includes('teams')) {
			return '/product-logos/teams-logo.svg';
		} else if (normalizedName.includes('whatsapp')) {
			return '/product-logos/whatsapp-logo.svg';
		} else if (normalizedName.includes('email')) {
			return '/product-logos/email-logo.svg';
		} else if (normalizedName.includes('ai')) {
			return '/product-logos/ai-logo.svg';
		} else if (normalizedName.includes('upstack')) {
			return '/product-logos/upstack-logo.svg';
		}

		return '/product-logos/default-logo.svg';
	}

	// Toggle product enabled status
	async function toggleProduct(productId: string) {
		// Get the product by ID
		const product = products.find((p) => p.id === productId);

		if (!product) {
			toast.error('Product not found.');
			return;
		}

		// Don't allow toggling if product is coming soon
		if (product.is_coming_soon) {
			toast.error('This product is coming soon and cannot be enabled yet.');
			return;
		}

		// Check if required products are enabled
		const enabling = !isProductEnabled(productId);
		if (enabling && product.requires_product_id && !isProductEnabled(product.requires_product_id)) {
			const requiredProduct = products.find((p) => p.id === product.requires_product_id);
			toast.error(`You must enable ${requiredProduct?.name || 'the required product'} first.`);
			return;
		}

		if (loading[productId]) return;

		loading = { ...loading, [productId]: true };
		const currentStatus = isProductEnabled(productId);
		const newStatus = !currentStatus;

		// Immediately update local UI state to reflect the change
		// This ensures the switch stays in the correct position
		localUIState = { ...localUIState, [productId]: newStatus };

		try {
			const response = await fetch(`/company/${companyId}/products/api/toggle-product`, {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ productId, enabled: newStatus })
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.message || 'Failed to toggle product');
			}

			// On success, keep our local UI state as is (already set to newStatus)
			// We don't need to update productStatusMap directly as it's derived from companyProducts
			// which will be updated on the next data fetch
			dispatch('productToggled', { productId, enabled: newStatus });

			// Re-enable toast
			toast.success(`Product ${newStatus ? 'enabled' : 'disabled'} successfully`);
		} catch (err) {
			console.error('Error toggling product:', err);
			error = err instanceof Error ? err.message : 'An unknown error occurred';

			// Revert the local UI state if the API call fails
			localUIState = { ...localUIState, [productId]: currentStatus };

			toast.error(error);
		} finally {
			loading = { ...loading, [productId]: false };
		}
	}
</script>

{#if filteredProducts.length === 0}
	<div class="text-center p-4 text-muted-foreground">
		<p>No products available in this category.</p>
	</div>
{:else if compact}
	<div class="flex flex-col space-y-2">
		{#each filteredProducts as product}
			{@const productEnabled = isProductEnabled(product.id)}
			{@const requirementsMet = productRequirementsMet(product)}
			<div
				class="flex items-center justify-between p-3 rounded-md border bg-card {!requirementsMet
					? 'opacity-70'
					: ''}"
			>
				<div class="flex items-center">
					{#if loading[product.id]}
						<Loader2 class="h-4 w-4 mr-2 animate-spin text-muted-foreground" />
					{:else}
						<Switch
							checked={productEnabled}
							onCheckedChange={() => toggleProduct(product.id)}
							disabled={product.is_coming_soon || loading[product.id] || !requirementsMet}
						/>
					{/if}
					<span class="ml-3 text-sm font-medium">{product.name}</span>
					{#if product.is_coming_soon}
						<Badge class="ml-2" variant="outline">Coming Soon</Badge>
					{/if}
				</div>
			</div>
		{/each}
	</div>
{:else}
	<div class="grid gap-3">
		{#each filteredProducts as product (product.id)}
			{@const productEnabled = isProductEnabled(product.id)}
			{@const requirementsMet = productRequirementsMet(product)}
			<Card.Root class={!requirementsMet ? 'opacity-70' : ''}>
				<Card.Content class="flex justify-between items-center p-4">
					<div class="space-y-1.5 flex-grow">
						<div class="flex items-center justify-between">
							<h3 class="font-semibold leading-none tracking-tight flex items-center gap-2">
								{#if product.name}
									<img
										src={getProductLogo(product.name)}
										alt={`${product.name} Logo`}
										class="h-5 w-5 object-contain"
									/>
								{/if}
								{product.name}
								{#if product.is_coming_soon}
									<Badge variant="outline" class="ml-2">Coming Soon</Badge>
								{/if}
								{#if product.requires_product_id && !requirementsMet}
									{@const requiredProduct = products.find(
										(p) => p.id === product.requires_product_id
									)}
									<Badge variant="secondary" class="ml-2"
										>Requires {requiredProduct?.name || 'another product'}</Badge
									>
								{/if}
							</h3>
						</div>
						<p class="text-sm text-muted-foreground">
							{product.description}
						</p>
						{#if product.features && product.features.length > 0}
							<div class="flex flex-wrap gap-1 mt-2">
								{#each product.features as feature}
									<Badge variant="secondary" class="text-xs">{feature}</Badge>
								{/each}
							</div>
						{/if}
					</div>
					<div class="flex items-center ml-4">
						{#if loading[product.id]}
							<Loader2 class="h-5 w-5 animate-spin text-muted-foreground mr-1" />
						{:else}
							<Switch
								checked={productEnabled}
								onCheckedChange={() => toggleProduct(product.id)}
								disabled={product.is_coming_soon || loading[product.id] || !requirementsMet}
							/>
						{/if}
					</div>
				</Card.Content>
			</Card.Root>
		{/each}
	</div>
{/if}

{#if error}
	<div class="mt-4 p-3 text-red-600 bg-red-50 rounded-md">{error}</div>
{/if}
