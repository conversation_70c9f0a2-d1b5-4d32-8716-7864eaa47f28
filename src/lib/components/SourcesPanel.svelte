<script>
	import Button from './ui/button/button.svelte';
	import * as Select from './ui/select';

	// Props
	export let sources = [];
	export let onClose = () => {};

	// Filter Options
	let timeRangeOptions = [
		'Any Time',
		'Past Week',
		'Past Month',
		'Past 3 Months',
		'Past Year',
		'Custom Range'
	];
	let selectedTimeRange = 'Any Time';

	let mediaTypeOptions = [
		{ label: 'PDFs', value: 'pdfs', checked: false },
		{ label: 'PowerPoints', value: 'powerpoints', checked: false },
		{ label: 'Video', value: 'video', checked: false },
		{ label: 'Images', value: 'images', checked: true },
		{ label: 'URLs', value: 'urls', checked: false }
	];

	let sourceOptions = [
		{ label: 'Sharepoint', value: 'sharepoint', checked: true },
		{ label: 'One Drive', value: 'one_drive', checked: false },
		{ label: 'Confluence', value: 'confluence', checked: false }
	];

	// Dropdown States
	let showTimeRangeDropdown = false;
	let showMediaTypeDropdown = false;
	let showSourceOptionsDropdown = false;

	// Filtered Sources
	$: filteredSources = sources;

	// Group sources by document title
	function groupSourcesByDocument(sources) {
		return sources.reduce((acc, source) => {
			const title = source.metadata.file_name;
			if (!acc[title]) {
				acc[title] = [];
			}
			acc[title].push(source);
			return acc;
		}, {});
	}

	// Grouped sources
	$: groupedSources = groupSourcesByDocument(sources);

	// Methods
	function toggleDropdown(dropdown) {
		if (dropdown === 'timeRange') {
			showTimeRangeDropdown = !showTimeRangeDropdown;
			showMediaTypeDropdown = false;
			showSourceOptionsDropdown = false;
		} else if (dropdown === 'mediaType') {
			showMediaTypeDropdown = !showMediaTypeDropdown;
			showTimeRangeDropdown = false;
			showSourceOptionsDropdown = false;
		} else if (dropdown === 'sourceOptions') {
			showSourceOptionsDropdown = !showSourceOptionsDropdown;
			showTimeRangeDropdown = false;
			showMediaTypeDropdown = false;
		}
	}

	function applyFilters() {
		// Implement filtering logic here
		filteredSources = sources.filter((source) => {
			return true; // Replace with actual conditions
		});

		// Close dropdowns
		showTimeRangeDropdown = false;
		showMediaTypeDropdown = false;
		showSourceOptionsDropdown = false;
	}
</script>

<div
	class="fixed inset-y-0 right-0 top-0 bottom-0 min-w-sm max-w-md bg-gradient-to-r from-[#240046] to-[#16002a] border-teal-500 border-l-4 border-solid text-white p-4"
>
	<!-- Exit Button -->
	<button class="absolute text-teal-300 top-2 right-2 hover:text-teal-200" on:click={onClose}>
		<!-- X Icon -->
		<svg
			xmlns="http://www.w3.org/2000/svg"
			class="w-6 h-6"
			fill="none"
			viewBox="0 0 24 24"
			stroke="currentColor"
		>
			<path
				stroke-linecap="round"
				stroke-linejoin="round"
				stroke-width="2"
				d="M6 18L18 6M6 6l12 12"
			/>
		</svg>
	</button>

	<!-- Filter Dropdowns -->
	<div class="mt-6 space-y-2">
		<!-- Time Range Dropdown -->
		<div class="relative">
			<button
				class="flex items-center justify-between w-full px-4 py-2 text-black bg-white rounded-full"
				on:click={() => toggleDropdown('timeRange')}
			>
				<span>{selectedTimeRange}</span>
				<!-- Dropdown Arrow Icon -->
				<svg
					xmlns="http://www.w3.org/2000/svg"
					class="w-4 h-4"
					fill="none"
					viewBox="0 0 24 24"
					stroke="currentColor"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M19 9l-7 7-7-7"
					/>
				</svg>
			</button>
			{#if showTimeRangeDropdown}
				<div class="absolute z-10 w-full mt-1 text-black bg-white rounded-md shadow-lg">
					{#each timeRangeOptions as option}
						<Select.Root>
							<Select.Trigger>
								<Select.Value bind:value={selectedTimeRange} />
							</Select.Trigger>
							<Select.Content>
								{#each timeRangeOptions as option}
									<Select.Item value={option}>{option}</Select.Item>
								{/each}
							</Select.Content>
						</Select.Root>
					{/each}
				</div>
			{/if}
		</div>

		<!-- Media Types Dropdown -->
		<div class="relative">
			<button
				class="flex items-center justify-between w-full px-4 py-2 text-black bg-white rounded-full"
				on:click={() => toggleDropdown('mediaType')}
			>
				<span>Media Types</span>
				<!-- Dropdown Arrow Icon -->
				<svg
					xmlns="http://www.w3.org/2000/svg"
					class="w-4 h-4"
					fill="none"
					viewBox="0 0 24 24"
					stroke="currentColor"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M19 9l-7 7-7-7"
					/>
				</svg>
			</button>
			{#if showMediaTypeDropdown}
				<div class="absolute z-10 w-full p-2 mt-1 text-black bg-white rounded-md shadow-lg">
					{#each mediaTypeOptions as option, index}
						<label class="flex items-center space-x-2">
							<input
								type="checkbox"
								bind:checked={mediaTypeOptions[index].checked}
								class="form-checkbox"
							/>
							<span>{option.label}</span>
						</label>
					{/each}
				</div>
			{/if}
		</div>

		<!-- Source Options Dropdown -->
		<div class="relative">
			<button
				class="flex items-center justify-between w-full px-4 py-2 text-black bg-white rounded-full"
				on:click={() => toggleDropdown('sourceOptions')}
			>
				<span>Source Options</span>
				<!-- Dropdown Arrow Icon -->
				<svg
					xmlns="http://www.w3.org/2000/svg"
					class="w-4 h-4"
					fill="none"
					viewBox="0 0 24 24"
					stroke="currentColor"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M19 9l-7 7-7-7"
					/>
				</svg>
			</button>
			{#if showSourceOptionsDropdown}
				<div class="absolute z-10 w-full p-2 mt-1 text-black bg-white rounded-md shadow-lg">
					{#each sourceOptions as option, index}
						<label class="flex items-center space-x-2">
							<input
								type="checkbox"
								bind:checked={sourceOptions[index].checked}
								class="form-checkbox"
							/>
							<span>{option.label}</span>
						</label>
					{/each}
				</div>
			{/if}
		</div>
	</div>

	<!-- Search Again Button -->
	<div class="mt-4">
		<button
			class="w-full px-4 py-2 font-bold text-white bg-teal-500 rounded-full"
			on:click={applyFilters}
		>
			Search Again
		</button>
	</div>

	<div class="mt-4 overflow-y-auto h-[calc(100vh-280px)] pr-4">
		<!-- Set height for scrolling -->
		<h2 class="text-lg font-bold">Sources</h2>
		<ul class="mt-4">
			{#each Object.entries(groupedSources) as [title, groupedSource]}
				<li class="mb-4">
					<Button variant="link" href="#" class="text-xl font-bold text-blue-400 hover:underline"
						>{title}</Button
					>
					{#each groupedSource as source, index}
						<div>
							<p class="line-clamp-3">{source.content}</p>
							{#if index < groupedSource.length - 1}
								<div class="my-2 border-b border-gray-300"></div>
							{/if}
						</div>
					{/each}
				</li>
			{/each}
		</ul>
	</div>
</div>
