<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { LayoutGridIcon, ListIcon } from 'lucide-svelte';
	import FileCard from './FileCard.svelte';
	import FileListItem from './FileListItem.svelte';
	import type { SharePointFile } from '$lib/types';
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';
	export let user;
	export let files: (SharePointFile & { isIngested?: boolean })[] = [];
	export let sourceId;
	export let onFolderClick: (file: SharePointFile) => Promise<void>;
	export let isGridView = false;
</script>

<div class="w-full max-w-full overflow-x-auto">
	<div
		class={`${isGridView ? 'grid grid-cols-2 gap-4 md:grid-cols-3 auto-cols-auto' : 'flex flex-col gap-2 max-w-screen w-full overflow-hidden'}`}
	>
		{#each files.filter((file) => file.folder) as file (file.id)}
			{#if isGridView}
				<FileCard {file} onFolderClick={() => onFolderClick(file)} />
			{:else}
				<FileListItem
					{user}
					{sourceId}
					{file}
					isIngested={file.isIngested || false}
					onFolderClick={() => onFolderClick(file)}
				/>
			{/if}
		{/each}
		{#each files.filter((file) => !file.folder) as file (file.id)}
			<FileListItem
				{user}
				{sourceId}
				{file}
				isIngested={file.isIngested || false}
				onFolderClick={() => onFolderClick(file)}
			/>
		{/each}
	</div>
</div>
