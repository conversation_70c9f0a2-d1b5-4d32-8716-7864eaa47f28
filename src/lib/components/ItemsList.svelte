<script lang="ts">
	import { z } from 'zod';
	import SuperDebug, { type SuperValidated, superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { browser } from '$app/environment';
	import * as Form from '$lib/components/ui/form/index.js';
	import { Checkbox } from '$lib/components/ui/checkbox/index.js';
	import type { User } from '$lib/types/User';
	import { toast } from 'svelte-sonner';
	import * as Select from '$lib/components/ui/select';
	import type { Database } from '../../../database.types';
	import Label from './ui/label/label.svelte';
	import { onDestroy } from 'svelte';
	import { createEventDispatcher } from 'svelte';
	const dispatch = createEventDispatcher();

	export let items: User[] = [];
	export let selectedUsers: string[] = [];
	export let userStatuses: { [key: string]: 'admin' | 'member' } = {};
	export { selectedUsers as selectedUsersForm };

	export const formSchema = z.object({
		selectedUsers: z.array(z.string()).refine((value) => value.length > 0, {
			message: 'You have to select at least one user.'
		}),
		userStatuses: z.record(z.string(), z.enum(['admin', 'member']))
	});
	export type FormSchema = typeof formSchema;

	const form = superForm(
		{ selectedUsers, userStatuses },
		{
			validators: zodClient(formSchema),
			dataType: 'json',
			onUpdated: ({ form: f }) => {
				if (f.valid) {
					toast.success(`You submitted the form successfully.`);
				} else {
					toast.error('Please fix the errors in the form.');
				}
			}
		}
	);

	const { form: formData, enhance } = form;

	function addItem(id: string) {
		const newSelectedUsers = [...selectedUsers, id];
		userStatuses[id] = 'member'; // Default status
		dispatch('updateSelected', { 
			selectedUsers: newSelectedUsers,
			userStatuses
		});
	}

	function removeItem(id: string) {
		const newSelectedUsers = selectedUsers.filter((i) => i !== id);
		delete userStatuses[id];
		dispatch('updateSelected', { 
			selectedUsers: newSelectedUsers,
			userStatuses
		});
	}

	function handleStatusChange(userId: string, status: 'admin' | 'member') {
		userStatuses[userId] = status;
	}

	// Select All Functionality
	let selectAll = false;
	let isIndeterminate = false;

	function toggleSelectAll() {
		let newSelectedUsers;
		if (selectAll) {
			newSelectedUsers = items.map((item) => item.id);
			items.forEach((item) => {
				if (!userStatuses[item.id]) {
					 userStatuses[item.id] = 'member';
				}
			});
		} else {
			newSelectedUsers = [];
			Object.keys(userStatuses).forEach((key) => {
				delete userStatuses[key];
			});
		}
		dispatch('updateSelected', { 
			selectedUsers: newSelectedUsers,
			userStatuses
		});
	}

	function updateSelectAllState() {
		if (selectedUsers.length === items.length) {
			selectAll = true;
			isIndeterminate = false;
		} else if (selectedUsers.length === 0) {
			selectAll = false;
			isIndeterminate = false;
		} else {
			selectAll = false;
			isIndeterminate = true;
		}
	}

	$: updateSelectAllState();

	// Watch for changes in selectedUsers to update selectAll and isIndeterminate
	$: {
		updateSelectAllState();
	}
</script>

<form method="POST" class="space-y-8" use:enhance>
	<Form.Fieldset {form} name="selectedUsers" class="space-y-0">
		<div class="mb-4">
			<Form.Legend class="text-base">Select Users</Form.Legend>
			<Form.Description>Select the users you want to assign roles to.</Form.Description>
		</div>
		<div class="flex items-center mb-4">
			<Checkbox id="select-all" bind:checked={selectAll} on:change={toggleSelectAll} />
			<Label for="select-all" class="ml-2 font-medium">Select All</Label>
		</div>
		<div class="space-y-2">
			{#each items as item}
				<div class="flex flex-col space-y-2">
					<div class="flex items-center space-x-2">
						<Checkbox
							id={item.id}
							checked={selectedUsers.includes(item.id)}
							on:change={(e) => {
								if (e.currentTarget.checked) {
									addItem(item.id);
								} else {
									removeItem(item.id);
								}
							}}
						/>
						<div class="grid gap-1.5 leading-none">
							<Label
								for={item.id}
								class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
							>
								{item.displayName}
							</Label>
							<p class="text-sm text-muted-foreground">
								{item.mail}
								{item.businessPhones.map((x) => x)}
							</p>
						</div>
						{#if selectedUsers.includes(item.id)}
							<Select.Root bind:selected={userStatuses[item.id]}>
								<Select.Trigger><Select.Value placeholder="Select status..." /></Select.Trigger>
								<Select.Content>
									<Select.Item value="admin" label="Knowledge Owner">Knowledge Owner</Select.Item>
									<Select.Item value="member" label="Member">Member</Select.Item>
								</Select.Content>
							</Select.Root>
						{/if}
					</div>
					{#if selectedUsers.includes(item.id) && !userStatuses[item.id]}
						<p class="text-sm text-red-500">Please select a status for this user.</p>
					{/if}
				</div>
			{/each}
			<!-- <Form.FieldErrors name="selectedUsers" /> -->
			<!-- <Form.FieldErrors name="userStatuses" /> -->
		</div>
	</Form.Fieldset>
	<Form.Button>Update display</Form.Button>
	{#if browser}
		<SuperDebug data={$formData} />
	{/if}
</form>
