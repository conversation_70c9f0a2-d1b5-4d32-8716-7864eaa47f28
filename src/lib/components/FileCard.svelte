<script lang="ts">
	import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader } from '$lib/components/ui/card';
	import { Button } from '$lib/components/ui/button';
	import { FileIcon, FolderIcon, Trash2Icon, UploadIcon, FileTextIcon } from 'lucide-svelte';
	import ItemActions from './ItemActions.svelte';
	import * as Tooltip from '$lib/components/ui/tooltip';

	export let file;
	export let onFolderClick: () => void;
	export let sourceId;
	export let user;
	export let isIngested = false;

	$: isFolder = !!file.folder;

	const handleDelete = async () => {
		// Implement delete functionality
	};

	const handleUpload = async () => {
		// Implement upload functionality
	};

	const handleViewDetails = async () => {
		// Implement view details functionality
	};

	const handleClick = () => {
		if (isFolder) {
			onFolderClick();
		} else if (file.webUrl) {
			// Open SharePoint file in a new tab
			window.open(file.webUrl, '_blank');
		}
	};

	function formatFileSize(bytes: number): string {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	}
</script>

<Card
	on:click={handleClick}
	class={`relative cursor-pointer transition-colors ${isFolder ? 'hover:bg-secondary/10' : 'hover:bg-secondary/5'}`}
>
	<ItemActions class="absolute top-0 right-0 p-2" {file} {sourceId} {user} {isIngested} />
	<CardHeader class="flex flex-row items-center justify-between pb-2 space-y-0">
		<div class="flex items-start w-full space-x-2 min-w-0">
			{#if isFolder}
				<FolderIcon class="w-5 h-5" />
			{:else}
				<FileIcon class="w-5 h-5" />
			{/if}
			<Tooltip.Root>
				<Tooltip.Trigger>
					<h4 class="truncate flex-1 overflow-hidden text-sm font-medium block min-w-0">
						{file.name}
					</h4>
				</Tooltip.Trigger>
				<Tooltip.Content>
					{file.name}
				</Tooltip.Content>
			</Tooltip.Root>
		</div>
	</CardHeader>
	<CardContent>
		<p class="text-xs text-muted-foreground">
			{new Date(file.lastModifiedDateTime).toLocaleString()}
		</p>
		<p class="mt-1 text-xs text-muted-foreground">
			{formatFileSize(file.size)}
		</p>
	</CardContent>
</Card>
