<script lang="ts" context="module">
	import { z } from 'zod';

	export let items: User[];

	export const formSchema = z.object({
		items: z.array(z.string()).refine((value) => value.length > 0, {
			message: 'You have to select at least one user.'
		}),
		userStatuses: z.record(z.string(), z.enum(['admin', 'member']))
	});
	export type FormSchema = typeof formSchema;
</script>

<script lang="ts">
	import SuperDebug, { type Infer, type SuperValidated, superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { browser } from '$app/environment';
	import { page } from '$app/stores';
	import * as Form from '$lib/components/ui/form/index.js';
	import { Checkbox } from '$lib/components/ui/checkbox/index.js';
	import type { User } from '$lib/types/User';
	import { toast } from 'svelte-sonner';
	import * as Select from '$lib/components/ui/select';
	import type { Database } from '../../../database.types';

	export let selectedUsers: string[] = [];
	export let userStatuses: { [key: string]: 'admin' | 'member' } = {};
	export { selectedUsers as form };

	const form = superForm(
		{ selectedUsers, userStatuses },
		{
			validators: zodClient(formSchema),
			dataType: 'json',
			onUpdated: ({ form: f }) => {
				if (f.valid) {
					toast.success(`You submitted the form successfully.`);
				} else {
					toast.error('Please fix the errors in the form.');
				}
			}
		}
	);

	const { form: formData, enhance } = form;

	function addItem(id: string) {
		selectedUsers = [...selectedUsers, id];
		userStatuses[id] = 'member'; // Default status
	}

	function removeItem(id: string) {
		selectedUsers = selectedUsers.filter((i) => i !== id);
		delete userStatuses[id];
	}

	function handleStatusChange(userId: string, status: 'admin' | 'member') {
		userStatuses[userId] = status;
	}
</script>

<form method="POST" class="space-y-8" use:enhance>
	<Form.Fieldset {form} name="items" class="space-y-0">
		<div class="mb-4">
			<Form.Legend class="text-base">Select Users</Form.Legend>
			<Form.Description>Select the users you want to assign roles to.</Form.Description>
		</div>
		<div class="space-y-2">
			{#each items as item}
				{@const checked = selectedUsers.includes(item.id)}
				<div class="flex flex-col space-y-2">
					<div class="flex flex-row items-center space-x-3">
						<Form.Control let:attrs>
							<Checkbox
								{...attrs}
								{checked}
								onCheckedChange={(v) => {
									if (v) {
										addItem(item.id);
									} else {
										removeItem(item.id);
									}
								}}
							/>
							<Form.Label class="gap-4 font-normal">
								{item.displayName}
								{item.mail}
							</Form.Label>
							<input hidden type="checkbox" name={attrs.name} value={item.id} {checked} />
						</Form.Control>
						{#if checked}
							<Select.Root bind:selected={userStatuses[item.id]}>
								<Select.Trigger><Select.Value placeholder="Select status..." /></Select.Trigger>
								<Select.Content>
									<Select.Item value="admin" label="Knowledge Owner">Knowledge Owner</Select.Item>
									<Select.Item value="member" label="Member">Member</Select.Item>
								</Select.Content>
							</Select.Root>
						{/if}
					</div>
					{#if checked && !userStatuses[item.id]}
						<p class="text-sm text-red-500">Please select a status for this user.</p>
					{/if}
				</div>
			{/each}
			<Form.FieldErrors name="items" />
			<Form.FieldErrors name="userStatuses" />
		</div>
	</Form.Fieldset>
	<Form.Button>Update display</Form.Button>
	{#if browser}
		<SuperDebug data={$formData} />
	{/if}
</form>
