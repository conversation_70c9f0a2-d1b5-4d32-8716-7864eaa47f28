<script>
	import { goto } from '$app/navigation';
	import Button from './ui/button/button.svelte';
	export let breadcrumbs = {};

	function navigateTo(sourceId, itemId) {
		goto(`/company/${companyId}/vault/${sourceId}/${itemId}`);
	}
</script>

<nav class="mb-4">
	<ul class="flex">
		{#each Object.entries(breadcrumbs) as [id, name], index}
			<li>
				{#if index > 0}
					<span> / </span>
				{/if}
				<Button
					variant="link"
					class="font-thin text-purple-900 hover:underline"
					on:click={() => navigateTo(id, name)}>{name}</Button
				>
			</li>
		{/each}
	</ul>
</nav>
