<script lang="ts">
  export let onVoiceInput: () => void;
  export let isLoading = false;
  
  let message = '';
  let inputElement: HTMLInputElement;

  function handleSubmit(event: Event) {
    if (!message.trim()) {
      event.preventDefault();
      return;
    }
  }

  // Reset the message without losing focus
  export function reset() {
    message = '';
    // Keep the input active after reset
    if (document.activeElement !== inputElement) {
      inputElement?.focus();
    }
  }

  export function focus() {
    inputElement?.focus();
  }
</script>

<div class="relative max-w-4xl px-0 mx-auto sm:px-2 md:px-4">
  <input
    bind:value={message}
    bind:this={inputElement}
    name="message"
    id="messageInput"
    type="text"
    placeholder="Talk to TOTM..."
    class="w-full py-3 pl-4 pr-24 text-[hsl(var(--foreground))] bg-gradient-to-r from-card to-background dark:to-[#360033]/50 dark:from-[#0b8793]/20 placeholder:text-[hsl(var(--muted-foreground))] rounded-full border border-[hsl(var(--border))] focus:outline-none focus:ring-2 focus:ring-[#0b8793] dark:focus:ring-[#0b8793]/50 transition-all duration-200"
    disabled={isLoading}
    autofocus
  />
  <div class="absolute inset-y-0 right-0 flex items-center gap-2 pr-4 sm:right-2 md:right-4">
    <button
      type="button"
      class="p-2 rounded-full text-muted-foreground hover:bg-muted/50 dark:hover:bg-[#0b8793]/20 transition-colors duration-200"
      on:click={onVoiceInput}
      disabled={isLoading}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="size-5"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M12 18.75a6 6 0 0 0 6-6v-1.5m-6 7.5a6 6 0 0 1-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 0 1-3-3V4.5a3 3 0 1 1 6 0v8.25a3 3 0 0 1-3 3Z"
        />
      </svg>
    </button>
    <button
      type="submit"
      class="p-2 rounded-full text-primary hover:text-[#0b8793] dark:text-[#0b8793] dark:hover:text-[#0b8793]/80 hover:bg-muted/50 dark:hover:bg-[#0b8793]/20 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
      disabled={isLoading || !message.trim()}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="size-5"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M6 12 3.269 3.125A59.769 59.769 0 0 1 21.485 12 59.768 59.768 0 0 1 3.27 20.875L5.999 12Zm0 0h7.5"
        />
      </svg>
    </button>
  </div>
</div> 