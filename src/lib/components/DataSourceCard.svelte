<script lang="ts">
	import * as Card from '$lib/components/ui/card';
	import { Button } from '$lib/components/ui/button';
	import { FileSearch, FileBox } from 'lucide-svelte';
	import { getProductLogo } from '$lib/utils/productLogos';
	import DataSourceActions from './DataSourceActions.svelte';
	import * as Tooltip from '$lib/components/ui/tooltip';
	import { page } from '$app/stores';

	export let source: {
		id: string;
		type?: string;
		name?: string;
		webUrl: string | null;
		created_at: string;
		updated_at: string;
		company_id?: string;
		display_name?: string;
		platform_type?: string;
		drive_id?: string;
	};
	export let user: any;

	// Get company ID from page params if not provided in source
	$: companyId = source.company_id || $page.params.companyId;

	// Ensure we have values for type and name, even if they come from different fields
	$: sourceType = source.type || source.platform_type || 'unknown';
	$: sourceName = source.name || source.display_name || 'Unnamed Source';

	// Get product logo
	$: logo = getProductLogo(sourceType);

	// Format date for better readability
	function formatDate(dateString: string): string {
		const date = new Date(dateString);
		return new Intl.DateTimeFormat('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		}).format(date);
	}
</script>

<Card.Root
	class="bg-card border rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-200 min-w-[280px] flex flex-col h-full"
>
	<!-- Card header with colored accent -->
	<div class="h-2 bg-primary/80"></div>

	<div class="p-5">
		<!-- Title and icon -->
		<div class="flex items-start justify-between mb-4 gap-2">
			<div class="flex items-start gap-3 min-w-0 flex-1">
				<div
					class="w-12 h-12 shrink-0 bg-primary/10 rounded-lg p-1.5 flex items-center justify-center text-primary"
				>
					<img src={logo.src} alt={logo.alt} class="w-full h-full object-contain" />
				</div>
				<div class="min-w-0 flex-1">
					<Tooltip.Root>
						<Tooltip.Trigger class="text-left w-full">
							<h2 class="text-xl font-semibold line-clamp-2 hover:text-primary transition-colors">
								{sourceName}
							</h2>
						</Tooltip.Trigger>
						<Tooltip.Content>
							<p class="max-w-xs break-words">{sourceName}</p>
						</Tooltip.Content>
					</Tooltip.Root>
					<p class="text-muted-foreground text-sm">{sourceType}</p>
				</div>
			</div>

			<!-- Actions Menu -->
			<DataSourceActions datasource={source} {user} />
		</div>

		<!-- Metadata -->
		<div class="text-sm text-muted-foreground mb-5">
			<p>Connected on {formatDate(source.created_at)}</p>
			{#if source.updated_at && source.updated_at !== source.created_at}
				<p class="mt-1">Last synced: {formatDate(source.updated_at)}</p>
			{/if}
		</div>

		<!-- Action button -->
		<Button
			variant="default"
			class="w-full flex items-center justify-center gap-2 mt-auto cursor-pointer transition-all hover:shadow-md hover:translate-y-[-1px]"
			href={`/company/${companyId}/vault/${source.id}/root`}
		>
			<FileSearch class="w-4 h-4" />
			Browse Files
		</Button>
	</div>
</Card.Root>
