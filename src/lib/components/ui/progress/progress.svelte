<script lang="ts">
	import { Progress as ProgressPrimitive } from "bits-ui";
	import { cn } from "$lib/utils";

	type $$Props = ProgressPrimitive.Props;

	let className: $$Props["class"] = undefined;
	export { className as class };
</script>

<ProgressPrimitive.Root
	class={cn(
		"relative h-4 w-full overflow-hidden rounded-full bg-secondary",
		className
	)}
	{...$$restProps}
>
	<div
		class="h-full w-full flex items-center gap-1"
	>
		<ProgressPrimitive.Indicator
			class="h-full bg-primary transition-all"
			style="transform: translateX(-{100 - ($$restProps.value || 0)}%)"
		/>
	</div>
</ProgressPrimitive.Root>
