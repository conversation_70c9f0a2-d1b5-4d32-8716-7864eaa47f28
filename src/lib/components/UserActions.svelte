<script lang="ts">
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Tooltip from '$lib/components/ui/tooltip';
	import {
		EllipsisVertical,
		UserPlusIcon,
		UserCogIcon,
		UserIcon,
		ChevronUpIcon,
		MoreHorizontal,
		Pencil,
		BookOpen
	} from 'lucide-svelte';
	import { cn } from '$lib/utils.js';
	import { toast } from 'svelte-sonner';
	import { createEventDispatcher } from 'svelte';
	import { page } from '$app/stores';
	import { Button } from '$lib/components/ui/button';
	import EditContactForm from './EditContactDetails/EditContactForm.svelte';
	import type { SuperValidated } from 'sveltekit-superforms';

	interface CompanyUser {
		user_name: string;
		user_email: string | null;
		user_phone_number: string | null;
		is_active: boolean;
		user_id: string;
		company_id: string;
		invite_id?: string;
		invite_status?: string;
		user_status: 'active' | 'pending' | 'inactive';
		has_file_access: boolean;
		role?: string;
	}

	export let user: CompanyUser;

	export let form: SuperValidated<{
		userId: string;
		email: string;
		phone: string;
	}>;

	let className = '';
	export { className as class };
	let showPromoteDialog = false;
	let showContactDialog = false;
	let dropdownOpen = false;
	let companyId = $page.params.companyId;
	let supabase = $page.data.supabase;
	const dispatch = createEventDispatcher();

	// Get current user from page data
	let currentUser = $page.data.session?.user;
	$: currentUserProfile = $page.data.companyUsers?.find(
		(u: CompanyUser) => u.user_email?.toLowerCase() === currentUser?.email?.toLowerCase()
	);
	// $: {
	// 	console.log('Current User:', currentUser);
	// 	console.log('Company Users:', $page.data.companyUsers);
	// 	console.log('Current User Profile:', currentUserProfile);
	// 	console.log('Current User Email:', currentUser?.email);
	// 	console.log('Matching Profile:', $page.data.companyUsers?.find(
	// 		(u: CompanyUser) => u.user_email?.toLowerCase() === currentUser?.email?.toLowerCase()
	// 	));
	// 	console.log('Is Admin:', isAdmin);
	// 	console.log('Can Edit Contact Info:', canEditContactInfo);
	// }
	$: isAdmin = currentUserProfile?.role === 'admin';

	// Check if user is a course creator
	let isCourseCreator = false;
	$: {
		if (user && companyId) {
			checkCourseCreatorStatus();
		}
	}

	async function checkCourseCreatorStatus() {
		try {
			const { data, error } = await supabase
				.from('user_tags')
				.select('*')
				.eq('user_id', user.user_id)
				.eq('company_id', companyId)
				.eq('tag_name', 'course_creator')
				.maybeSingle();

			isCourseCreator = !!data;
		} catch (err) {
			console.error('Error checking course creator status:', err);
		}
	}

	// Helper to check if user can edit contact info
	$: canEditContactInfo =
		isAdmin || currentUser?.email?.toLowerCase() === user.user_email?.toLowerCase();

	const handleActivate = async () => {
		console.log('🔄 Starting user activation process:', {
			userId: user.user_id,
			companyId: user.company_id,
			userEmail: user.user_email,
			currentUserRole: currentUserProfile?.role,
			userStatus: user.user_status,
			timestamp: new Date().toISOString()
		});

		const toastId = toast.loading(`Activating ${user.user_name}...`);

		const formData = new FormData();
		formData.append('userId', user.user_id);
		formData.append('companyId', user.company_id);
		formData.append('userEmail', user.user_email || '');

		try {
			console.log('📤 Sending activation request');
			const response = await fetch('?/activateUser', {
				method: 'POST',
				body: formData
			});

			const result = await response.json();
			console.log('📥 Activation response received:', {
				ok: response.ok,
				status: response.status,
				result,
				timestamp: new Date().toISOString()
			});

			if (response.ok) {
				if (result.type === 'success') {
					toast.success(`Successfully activated ${user.user_name}`, { id: toastId });
					dispatch('userUpdate');
				} else {
					toast.error(`Failed to activate ${user.user_name}: ${result.error || 'Unknown error'}`, {
						id: toastId
					});
				}
			} else {
				console.error('❌ Failed to activate user:', {
					result,
					status: response.status,
					timestamp: new Date().toISOString()
				});
				toast.error(`Failed to activate ${user.user_name}`, { id: toastId });
			}
		} catch (error) {
			console.error('❌ Error in handleActivate:', {
				error,
				userId: user.user_id,
				userEmail: user.user_email,
				timestamp: new Date().toISOString()
			});
			toast.error(
				`Error activating ${user.user_name}: ${error instanceof Error ? error.message : 'Unknown error'}`,
				{ id: toastId }
			);
		}
	};

	// const handleInvite = async () => {
	// 	const toastId = toast.loading(`Inviting ${user.user_name}...`);
	// 	const formData = new FormData();
	// 	formData.append('userEmail', user.user_email || '');
	// 	formData.append('companyId', user.company_id);

	// 	try {
	// 		const response = await fetch('?/inviteUser', {
	// 			method: 'POST',
	// 			body: formData
	// 		});

	// 		const result = await response.json();

	// 		if (response.ok) {
	// 			if (result.type === 'success') {
	// 				toast.success(`Successfully invited ${user.user_name}`, { id: toastId });
	// 				dispatch('userUpdate');
	// 			} else {
	// 				toast.error(`Failed to invite ${user.user_name}: ${result.error || 'Unknown error'}`, {
	// 					id: toastId
	// 				});
	// 			}
	// 		} else {
	// 			toast.error(`Failed to invite ${user.user_name}`, { id: toastId });
	// 		}
	// 	} catch (error) {
	// 		toast.error(
	// 			`Error inviting ${user.user_name}: ${error instanceof Error ? error.message : 'Unknown error'}`,
	// 			{ id: toastId }
	// 		);
	// 	}
	// };

	const handleResendInvite = async () => {
		const toastId = toast.loading(`Resending invite to ${user.user_name}...`);
		const formData = new FormData();
		formData.append('inviteId', user.invite_id || '');
		formData.append('userEmail', user.user_email || '');
		formData.append('companyId', user.company_id);

		try {
			// First try to resend using Supabase's resend method
			const { data: resendData, error: resendError } = await supabase.auth.resend({
				type: 'signup',
				email: user.user_email || '',
				options: {
					data: {
						type: 'company_invite',
						companyName: $page.data.company?.name || 'your company',
						message: `You have been invited to join ${$page.data.company?.name || 'a company'} on TOTM Search`
					},
					emailRedirectTo: `${window.location.origin}/company/${user.company_id}`
				}
			});

			console.log('Resend invite response:', {
				data: resendData,
				error: resendError,
				timestamp: new Date().toISOString()
			});

			if (resendError) {
				// If resend fails, try the server-side resend as fallback
				const response = await fetch('?/resendInvite', {
					method: 'POST',
					body: formData
				});

				const result = await response.json();
				console.log('Server resend response:', {
					result,
					timestamp: new Date().toISOString()
				});

				if (response.ok && result.type === 'success') {
					toast.success(`Successfully resent invite to ${user.user_name}`, { id: toastId });
					dispatch('userUpdate');
				} else {
					toast.error(
						`Failed to resend invite to ${user.user_name}: ${result.error || resendError.message || 'Unknown error'}`,
						{ id: toastId }
					);
				}
			} else {
				toast.success(`Successfully resent invite to ${user.user_name}`, { id: toastId });
				dispatch('userUpdate');
			}
		} catch (error) {
			console.error('Error in handleResendInvite:', error);
			toast.error(
				`Error resending invite to ${user.user_name}: ${error instanceof Error ? error.message : 'Unknown error'}`,
				{ id: toastId }
			);
		}
	};

	const handlePromote = async () => {
		console.log('Attempting to promote user:', {
			userId: user.user_id,
			companyId: user.company_id,
			currentRole: user.role,
			currentUserRole: currentUserProfile?.role,
			isAdmin
		});

		const toastId = toast.loading(`Promoting ${user.user_name} to admin...`);
		const formData = new FormData();
		formData.append('userId', user.user_id);
		formData.append('companyId', user.company_id);
		formData.append('role', 'admin');

		try {
			const response = await fetch('?/promoteUser', {
				method: 'POST',
				body: formData
			});

			const result = await response.json();
			console.log('Promote user response:', {
				ok: response.ok,
				status: response.status,
				result
			});

			if (response.ok && result.success) {
				toast.success(`Successfully promoted ${user.user_name} to admin`, { id: toastId });
				if (result.warning) {
					toast.warning(result.warning);
				}
				dispatch('userUpdate');
			} else {
				toast.error(`Failed to promote ${user.user_name}: ${result.error || 'Unknown error'}`, {
					id: toastId
				});
			}
		} catch (error) {
			console.error('Error in handlePromote:', error);
			toast.error(
				`Error promoting ${user.user_name}: ${error instanceof Error ? error.message : 'Unknown error'}`,
				{ id: toastId }
			);
		}
	};

	const handleDeactivate = async () => {
		console.log('Attempting to deactivate user:', {
			userId: user.user_id,
			companyId: user.company_id,
			userRole: user.role,
			currentUserRole: currentUserProfile?.role,
			isAdmin,
			isSelfDeactivation: currentUser?.email?.toLowerCase() === user.user_email?.toLowerCase()
		});

		const toastId = toast.loading(`Deactivating ${user.user_name}...`);
		const formData = new FormData();
		formData.append('userId', user.user_id);
		formData.append('companyId', user.company_id);

		try {
			const response = await fetch('?/deactivateUser', {
				method: 'POST',
				body: formData
			});

			const result = await response.json();
			console.log('Deactivate user response:', {
				ok: response.ok,
				status: response.status,
				result
			});

			if (response.ok && result.type === 'success') {
				toast.success(`Successfully deactivated ${user.user_name}`, { id: toastId });
				dispatch('userUpdate');
			} else {
				toast.error(`Failed to deactivate ${user.user_name}: ${result.error || 'Unknown error'}`, {
					id: toastId
				});
			}
		} catch (error) {
			console.error('Error in handleDeactivate:', error);
			toast.error(
				`Error deactivating ${user.user_name}: ${error instanceof Error ? error.message : 'Unknown error'}`,
				{ id: toastId }
			);
		}
	};

	const handleContactUpdate = async (event: SubmitEvent) => {
		dropdownOpen = false;
		event.preventDefault();
		const form = event.target as HTMLFormElement;
		const formData = new FormData(form);
		const email = formData.get('email') as string;
		const phone = formData.get('phone') as string;

		const toastId = toast.loading(`Updating contact information for ${user.user_name}...`);

		try {
			// First get the company_member_id
			const { data: memberData, error: memberError } = await supabase
				.from('company_members')
				.select('id')
				.eq('company_id', companyId)
				.eq('user_id', user.user_id)
				.single();

			if (memberError) throw memberError;
			if (!memberData?.id) throw new Error('Could not find company member record');

			const { error } = await supabase.from('user_platform_profiles').upsert([
				{
					company_member_id: memberData.id,
					platform_type: 'whatsapp',
					platform_user_id: phone,
					is_primary: true,
					metadata: { type: 'phone' }
				},
				{
					company_member_id: memberData.id,
					platform_type: 'email',
					platform_user_id: email,
					is_primary: true,
					metadata: { type: 'email' }
				}
			]);

			if (error) throw error;

			toast.success('Successfully updated contact information', {
				id: toastId,
				duration: 2000
			});
			showContactDialog = false;
			dispatch('userUpdate');
		} catch (error) {
			toast.error(
				`Failed to update contact information: ${error instanceof Error ? error.message : 'Unknown error'}`,
				{
					id: toastId,
					duration: 3000
				}
			);
		}
	};

	const handleEditContact = () => {
		dropdownOpen = false;
		dispatch('editContact', { user });
	};

	// Add new function to toggle course creator tag
	const toggleCourseCreatorTag = async () => {
		const toastId = toast.loading(
			`${isCourseCreator ? 'Removing' : 'Adding'} course creator access for ${user.user_name}...`
		);

		try {
			if (isCourseCreator) {
				// Remove the tag
				const { error } = await supabase
					.from('user_tags')
					.delete()
					.eq('user_id', user.user_id)
					.eq('company_id', companyId)
					.eq('tag_name', 'course_creator');

				if (error) throw error;

				toast.success(`Removed course creator access from ${user.user_name}`, { id: toastId });
			} else {
				// Add the tag
				const { error } = await supabase.from('user_tags').insert({
					user_id: user.user_id,
					company_id: companyId,
					tag_name: 'course_creator',
					created_by: currentUser?.id
				});

				if (error) throw error;

				toast.success(`Added course creator access for ${user.user_name}`, { id: toastId });
			}

			// Update the local state
			isCourseCreator = !isCourseCreator;

			// Notify parent component of the update
			dispatch('userUpdate');
		} catch (error) {
			console.error('Error toggling course creator status:', error);
			toast.error(
				`Error updating course creator status: ${error instanceof Error ? error.message : 'Unknown error'}`,
				{ id: toastId }
			);
		}
	};
</script>

<div class={className}>
	<DropdownMenu.Root bind:open={dropdownOpen}>
		<DropdownMenu.Trigger asChild let:builder>
			<Button builders={[builder]} variant="ghost" class="w-8 h-8 p-0">
				<span class="sr-only">Open menu</span>
				<MoreHorizontal class="w-4 h-4" />
			</Button>
		</DropdownMenu.Trigger>
		<DropdownMenu.Content align="end" class="w-[180px]">
			<DropdownMenu.Group>
				{#if user.user_status === 'active'}
					<DropdownMenu.Label>User Actions</DropdownMenu.Label>
					{#if canEditContactInfo}
						<DropdownMenu.Item on:click={() => dispatch('editContact', { user })}>
							<Pencil class="mr-2 h-4 w-4" />
							<span>Edit Contact</span>
						</DropdownMenu.Item>
					{/if}
					{#if isAdmin && user.role !== 'admin'}
						<DropdownMenu.Item on:click={() => (showPromoteDialog = true)}>
							<ChevronUpIcon class="mr-2 h-4 w-4" />
							<span>Promote to Admin</span>
						</DropdownMenu.Item>
					{/if}
					{#if isAdmin}
						<DropdownMenu.Item on:click={toggleCourseCreatorTag}>
							<BookOpen class="mr-2 h-4 w-4" />
							<span>{isCourseCreator ? 'Remove Course Creator' : 'Mark as Course Creator'}</span>
						</DropdownMenu.Item>
					{/if}
				{:else if user.user_status === 'pending'}
					<DropdownMenu.Label>Invitations</DropdownMenu.Label>
					<DropdownMenu.Item on:click={handleResendInvite}>
						<UserIcon class="mr-2 h-4 w-4" />
						<span>Resend Invite</span>
					</DropdownMenu.Item>
				{:else}
					<DropdownMenu.Label>User Actions</DropdownMenu.Label>
					<DropdownMenu.Item on:click={handleActivate}>
						<UserPlusIcon class="mr-2 h-4 w-4" />
						<span>Activate User</span>
					</DropdownMenu.Item>
					{#if user.user_email}
						<!-- <DropdownMenu.Item on:click={handleInvite}>
							<UserCogIcon class="mr-2 h-4 w-4" />
							<span>Send Invite</span>
						</DropdownMenu.Item> -->
					{/if}
				{/if}
			</DropdownMenu.Group>
		</DropdownMenu.Content>
	</DropdownMenu.Root>

	<Dialog.Root bind:open={showPromoteDialog}>
		<Dialog.Content class="sm:max-w-[425px]">
			<Dialog.Header>
				<Dialog.Title>Promote User</Dialog.Title>
				<Dialog.Description>
					Choose a new role for {user.user_name}
				</Dialog.Description>
			</Dialog.Header>
			<div class="grid gap-4 py-4">
				<div class="grid grid-cols-2 gap-4">
					<button
						class="flex flex-col items-start p-4 text-left transition-colors border rounded-lg hover:bg-muted"
						on:click={() => handlePromote()}
					>
						<h3 class="text-sm font-medium">Admin</h3>
						<p class="text-sm text-left text-muted-foreground">
							Can manage users, data sources, and company settings
						</p>
					</button>
					<button
						class="flex flex-col items-start p-4 transition-colors border rounded-lg hover:bg-muted"
						on:click={() => handlePromote()}
					>
						<h3 class="text-sm font-medium">Expert</h3>
						<p class="text-sm text-left text-muted-foreground">
							Can manage knowledge base and answer questions
						</p>
					</button>
				</div>
			</div>
		</Dialog.Content>
	</Dialog.Root>
</div>
