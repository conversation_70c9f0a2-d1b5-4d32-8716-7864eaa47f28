<script lang="ts">
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { 
		Settings2Icon, 
		UsersIcon, 
		Trash2Icon, 
		EllipsisVertical,
		BuildingIcon
	} from 'lucide-svelte';
	import { goto } from '$app/navigation';
	import { toast } from 'svelte-sonner';
	import type { Database } from '../../../database.types';
	import { page } from '$app/stores';
	import { createEventDispatcher } from 'svelte';
	import DeleteConfirmDialog from './DeleteConfirmDialog.svelte';
	
	let { supabase } = $page.data

	export let company: Database['public']['Tables']['companies']['Row'];
	let className: string | undefined = undefined;
	export { className as class };

	const dispatch = createEventDispatcher();
	let showDeleteDialog = false;

	const handleDelete = async () => {
		const toastId = toast.loading(`Deleting ${company.name}...`);
		try {
			const { error } = await supabase.from('companies').delete().eq('id', company.id);

			if (error) {
				console.error('Error deleting company:', error);

				throw new Error('Failed to delete company');
			} 

			toast.success(`Successfully deleted ${company.name}`, { id: toastId });
			dispatch('companyDelete', { companyId: company.id });
		} catch (error) {
			toast.error(`Failed to delete ${company.name}`, { id: toastId });
			console.error('Error deleting company:', error);
		}
	};

	const handleManageUsers = () => {
		goto(`/company/${company.id}/users`);
	};

	const handleSettings = () => {
		goto(`/company/${company.id}/settings`);
	};

	const handleTransfer = () => {
		goto(`/company/${company.id}/transfer`);
	};
</script>

<button class={className} on:click|stopPropagation>
	<DropdownMenu.Root>
		<DropdownMenu.Trigger class="p-1">
			<EllipsisVertical size={16} />
		</DropdownMenu.Trigger>
		<DropdownMenu.Content>
			<DropdownMenu.Item on:click={handleSettings}>
				<Settings2Icon class="w-4 h-4 mr-2" /> Settings
			</DropdownMenu.Item>

			<DropdownMenu.Item on:click={handleManageUsers}>
				<UsersIcon class="w-4 h-4 mr-2" /> Manage Users
			</DropdownMenu.Item>

			<DropdownMenu.Item on:click={handleTransfer}>
				<BuildingIcon class="w-4 h-4 mr-2" /> Transfer Ownership
			</DropdownMenu.Item>

			<DropdownMenu.Separator />
			
			<DropdownMenu.Item on:click={() => showDeleteDialog = true} class="text-destructive">
				<Trash2Icon class="w-4 h-4 mr-2" /> Delete Company
			</DropdownMenu.Item>
		</DropdownMenu.Content>
	</DropdownMenu.Root>
</button>

<DeleteConfirmDialog
	bind:open={showDeleteDialog}
	title="Delete Company"
	description="Are you sure you want to delete {company.name}? This action cannot be undone."
	on:confirm={handleDelete}
/>