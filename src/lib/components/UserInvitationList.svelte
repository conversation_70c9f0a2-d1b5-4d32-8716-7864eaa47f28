<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import type { User } from '$lib/types/User';

	export let siteUsers: Array<User> = [];

	let selectedUserIds = new Set<string>();

	const dispatch = createEventDispatcher();

	function toggleUserSelection(userId: string) {
		if (selectedUserIds.has(userId)) {
			selectedUserIds.delete(userId);
		} else {
			selectedUserIds.add(userId);
		}
	}

	function sendInvitations() {
		const selectedUsers = siteUsers.filter((user) => selectedUserIds.has(user.id));
		dispatch('sendInvitations', { selectedUsers });
	}
</script>

<div>
	<h2>Select Users to Invite</h2>
	{#if siteUsers.length > 0}
		<ul>
			{#each siteUsers as user}
				<li>
					<label>
						<input type="checkbox" on:change={() => toggleUserSelection(user.id)} />
						{user.displayName} ({user.mail})
					</label>
				</li>
			{/each}
		</ul>
		<button on:click={sendInvitations}> Send Invitations </button>
	{:else}
		<p>No users available.</p>
	{/if}
</div>
