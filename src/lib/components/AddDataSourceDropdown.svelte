<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import * as Dialog from '$lib/components/ui/dialog';
	import { Button } from '$lib/components/ui/button';
	import { Plus, ChevronDown } from 'lucide-svelte';

	interface StorageProvider {
		id: string;
		name: string;
		description: string;
		features: string[];
	}

	export let storageProviders: StorageProvider[] = [];
	export let companyId: string;
	export let showProductSelectorModal = false;

	// Create a reactive statement to handle storageProviders changes
	$: {
		console.log('AddDataSourceDropdown storageProviders updated:', storageProviders);
		// This reactive block will run whenever storageProviders changes
		// It ensures the dropdown content is updated without opening it
	}
	const dispatch = createEventDispatcher<{
		connectProvider: { provider: StorageProvider };
	}>();

	function handleStorageProviderClick(provider: StorageProvider) {
		if (!companyId) {
			console.log('No companyId');
			return;
		}

		// Pass the entire provider object to the parent
		dispatch('connectProvider', {
			provider: provider
		});
	}
</script>

<Dialog.Root bind:open={showProductSelectorModal}>
	<DropdownMenu.Root>
		<DropdownMenu.Trigger asChild let:builder>
			<Button
				variant="default"
				class="px-3 font-medium"
				builders={[builder]}
				data-testid="add-data-source-button"
			>
				<Plus class="w-4 h-4 mr-2" />
				Enabled Data Sources
				<ChevronDown class="w-4 h-4 ml-2" />
			</Button>
		</DropdownMenu.Trigger>
		<DropdownMenu.Content class="w-52">
			<DropdownMenu.Group>
				<DropdownMenu.Label>Available Providers</DropdownMenu.Label>
				<!-- Use a key directive to force re-rendering when storageProviders changes -->
				{#key JSON.stringify(storageProviders)}
					{#if storageProviders.length > 0}
						{#each storageProviders as provider}
							<DropdownMenu.Item on:click={() => handleStorageProviderClick(provider)}>
								{provider.name}
							</DropdownMenu.Item>
						{/each}
					{:else}
						<DropdownMenu.Item disabled>No providers enabled</DropdownMenu.Item>
					{/if}
				{/key}
			</DropdownMenu.Group>
			<DropdownMenu.Separator />
			<DropdownMenu.Group>
				<DropdownMenu.Label>Settings</DropdownMenu.Label>
				<DropdownMenu.Item on:click={() => (showProductSelectorModal = true)}>
					Enable Provider
				</DropdownMenu.Item>
			</DropdownMenu.Group>
		</DropdownMenu.Content>
	</DropdownMenu.Root>

	<Dialog.Content class="max-w-md">
		<Dialog.Header>
			<Dialog.Title>Add Storage Provider</Dialog.Title>
			<Dialog.Description>Enable a document storage provider for your company.</Dialog.Description>
		</Dialog.Header>

		<div class="py-4">
			<slot name="product-selector">
				<p class="text-sm text-muted-foreground">Loading available products...</p>
			</slot>
		</div>

		<Dialog.Footer>
			<Button variant="outline" on:click={() => (showProductSelectorModal = false)}>Cancel</Button>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>
