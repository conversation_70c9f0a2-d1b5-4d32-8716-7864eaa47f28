import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { cubicOut } from 'svelte/easing';
import type { TransitionConfig } from 'svelte/transition';
import type { SharePointFile } from '$lib/types';
import { env } from '$env/dynamic/public';
import { supabase } from './supabaseClient';

export const baseUrl =
	env.PUBLIC_BASE_URL ||
	(process.env.VERCEL_BRANCH_URL
		? `https://${process.env.VERCEL_BRANCH_URL}`
		: process.env.VERCEL_URL
			? `https://${process.env.VERCEL_URL}`
			: 'https://localhost:5173');

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

type FlyAndScaleParams = {
	y?: number;
	x?: number;
	start?: number;
	duration?: number;
};

export const flyAndScale = (
	node: Element,
	params: FlyAndScaleParams = { y: -8, x: 0, start: 0.95, duration: 150 }
): TransitionConfig => {
	const style = getComputedStyle(node);
	const transform = style.transform === 'none' ? '' : style.transform;

	const scaleConversion = (valueA: number, scaleA: [number, number], scaleB: [number, number]) => {
		const [minA, maxA] = scaleA;
		const [minB, maxB] = scaleB;

		const percentage = (valueA - minA) / (maxA - minA);
		const valueB = percentage * (maxB - minB) + minB;

		return valueB;
	};

	const styleToString = (style: Record<string, number | string | undefined>): string => {
		return Object.keys(style).reduce((str, key) => {
			if (style[key] === undefined) return str;
			return str + `${key}:${style[key]};`;
		}, '');
	};

	return {
		duration: params.duration ?? 200,
		delay: 0,
		css: (t) => {
			const y = scaleConversion(t, [0, 1], [params.y ?? 5, 0]);
			const x = scaleConversion(t, [0, 1], [params.x ?? 0, 0]);
			const scale = scaleConversion(t, [0, 1], [params.start ?? 0.95, 1]);

			return styleToString({
				transform: `${transform} translate3d(${x}px, ${y}px, 0) scale(${scale})`,
				opacity: t
			});
		},
		easing: cubicOut
	};
};

interface SharePointItem {
	id: string;
	name: string;
	path: string;
	lastModifiedDateTime: string;
	webUrl: string;
	size: number;
	folder?: { childCount: number };
}

interface SharePointResponse {
	value: SharePointItem[];
}

export function transformSharePointData(data: SharePointResponse): SharePointFile[] {
	return data.value.map((item: SharePointItem) => ({
		id: item.id,
		name: item.name,
		path: item.path,
		lastModifiedDateTime: item.lastModifiedDateTime,
		webUrl: item.webUrl,
		size: item.size,
		folder: item.folder ? { childCount: item.folder.childCount } : undefined
	}));
}

export async function fetchUserCompanies(userId: string) {
	try {
		// Get companies where user is a member
		const { data: memberCompanies, error: memberError } = await supabase
			.from('company_members')
			.select(
				`
				company:companies (
					id,
					name,
					status,
					transcription_time_allocated,
					transcription_time_used,
					created_at,
					updated_at,
					admin_uid,
					is_public
				)
			`
			)
			.eq('user_id', userId)
			.eq('is_active', true);

		// Get companies where user is an admin
		const { data: adminCompanies, error: adminError } = await supabase
			.from('companies')
			.select(`*`)
			.eq('admin_uid', userId);

		if (memberError) throw memberError;
		if (adminError) throw adminError;

		// Combine and deduplicate companies
		const memberCompanyList = memberCompanies?.map((mc) => mc.company) || [];
		const adminCompanyList = adminCompanies || [];
		const allCompanies = [...memberCompanyList, ...adminCompanyList];

		// Remove duplicates based on company id
		const uniqueCompanies = Array.from(new Map(allCompanies.map((c) => [c.id, c])).values());

		return { userCompanies: uniqueCompanies };
	} catch (error) {
		console.error('Failed to load companies:', error);
		return { userCompanies: [] };
	}
}

export const getN8nWebhookUrl = (
	endpoint: string,
	params: Record<string, string>,
	supabaseDb?: string
) => {
	const isProd = env.PUBLIC_BASE_URL === 'https://app.totm.space';
	const isStaging = env.PUBLIC_BASE_URL === 'https://staging.totm.space';
	const isDev = env.PUBLIC_BASE_URL === 'https://localhost:5173';
	const n8nBaseUrl = 'https://totm.app.n8n.cloud';

	// Always use the staging-{endpoint} pattern as the other endpoint is outdated
	const webhookEndpoint = `staging-${endpoint}`;

	// Add the Supabase database identifier to the parameters
	// This way n8n knows which Supabase database to connect to
	const paramsWithDb = { ...params };
	if (supabaseDb) {
		paramsWithDb.supabaseDb = supabaseDb;
	} else {
		// If no specific database is provided, default to the environment
		paramsWithDb.supabaseDb = isProd ? 'production' : 'staging';
	}

	const queryString = new URLSearchParams(paramsWithDb).toString();
	return `${n8nBaseUrl}/webhook/${webhookEndpoint}${queryString ? `?${queryString}` : ''}`;
};

export async function fetchFolderContents(userId: string, itemId: string, sourceId: string) {
	const params = {
		user_id: userId,
		source_id: sourceId,
		...(itemId && { item_id: itemId })
	};

	const url = getN8nWebhookUrl('folder', params, 'staging');

	const response = await fetch(url, {
		headers: {
			Authorization: `Bearer ${env.PUBLIC_SUPABASE_ANON_KEY}`,
			'Content-Type': 'application/json'
		}
	});
	const data = await response.json();
	const transformedData = transformSharePointData(data);
	return transformedData;
}

interface Permission {
	id: string;
	roles: string[];
	grantedTo: {
		user: {
			displayName: string;
		};
	};
	grantedToIdentities?: Array<{
		user: {
			displayName: string;
		};
	}>;
}

interface PermissionsResponse {
	value: Permission[];
}

export async function fetchItemPermissions(userId: string, itemId: string, sourceId: string) {
	const params = {
		user_id: userId,
		item_id: itemId,
		source_id: sourceId
	};

	const url = getN8nWebhookUrl('drive-item-permissions', params, 'staging');

	try {
		const response = await fetch(url, {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json'
			}
		});

		if (!response.ok) {
			throw new Error(`Error fetching permissions: ${response.statusText}`);
		}

		const data = await response.json();
		return data;
	} catch (error) {
		if (error instanceof Error) {
			console.error('Failed to fetch item permissions:', error.message);
		}
		throw error;
	}
}

export function transformPermissionsData(data: PermissionsResponse[]): Array<{
	id: string;
	role: string;
	grantedTo: string;
}> {
	if (!data || !Array.isArray(data) || data.length === 0 || !data[0].value) {
		return [];
	}

	const permissions = data[0].value;
	return permissions.map((permission) => {
		const role = permission.roles.join(', ');
		const grantedTo = permission.grantedToIdentities?.length
			? permission.grantedToIdentities[0].user.displayName
			: permission.grantedTo.user.displayName;

		return {
			id: permission.id,
			role: role,
			grantedTo: grantedTo
		};
	});
}

export function getRedirectUri(companyId?: string): string {
	if (companyId) {
		return `${baseUrl}/auth/sharepoint?company_id=${companyId}`;
	}
	return `${baseUrl}/auth/add-data-source`;
}

interface TokenData {
	[key: string]: unknown;
}

export async function sendWebhook(tokenData: TokenData, companyId: string) {
	try {
		const redirectUri = getRedirectUri();
		const url = getN8nWebhookUrl('data-source', {}, 'staging');

		const response = await fetch(url, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				tokenData,
				companyId,
				redirectUri
			})
		});

		if (!response.ok) {
			throw new Error('Webhook call failed');
		}
		const userData = await response.json();
		return userData;
	} catch (error) {
		if (error instanceof Error) {
			console.error(error.message);
		}
		throw error;
	}
}

export function generateState(payload: Record<string, unknown>): string {
	return Buffer.from(JSON.stringify(payload)).toString('base64');
}

export function generateCodeVerifier(): string {
	try {
		const array = new Uint8Array(64);
		crypto.getRandomValues(array);
		return base64UrlEncode(array);
	} catch (error) {
		console.error('Error generating code verifier:', error);
		throw new Error('Failed to generate code verifier');
	}
}

export async function generateCodeChallenge(verifier: string): Promise<string> {
	try {
		const encoder = new TextEncoder();
		const data = encoder.encode(verifier);
		const hashBuffer = await crypto.subtle.digest('SHA-256', data);
		const hashArray = new Uint8Array(hashBuffer);
		return base64UrlEncode(hashArray);
	} catch (error) {
		console.error('Error generating code challenge:', error);
		throw new Error('Failed to generate code challenge');
	}
}

export function base64UrlEncode(array: Uint8Array): string {
	return btoa(String.fromCharCode(...array))
		.replace(/\+/g, '-')
		.replace(/\//g, '_')
		.replace(/=+$/, '');
}

export async function exchangeCodeForTokens(
	code: string,
	codeVerifier: string,
	clientSecret: string,
	companyId?: string
) {
	const clientId = env.PUBLIC_AZURE_CLIENT_ID;

	try {
		const redirectUri = getRedirectUri(companyId);

		const response = await fetch('https://login.microsoftonline.com/common/oauth2/v2.0/token', {
			method: 'POST',
			headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
			body: new URLSearchParams({
				client_id: clientId,
				client_secret: clientSecret,
				code: code,
				redirect_uri: redirectUri,
				grant_type: 'authorization_code',
				code_verifier: codeVerifier
			})
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error('Failed to exchange code for tokens:', errorText);
			throw new Error(`Failed to exchange code for tokens: ${response.status} ${errorText}`);
		}

		const data = await response.json();
		return data;
	} catch (error) {
		console.error('Error in exchangeCodeForTokens:', error);
		throw error;
	}
}

export function getCompanyRoute(companyId: string, path: string = ''): string {
	const cleanPath = path.replace(/^\//, '');
	const withoutApp = cleanPath.replace(/^app\//, '');
	return `/company/${companyId}${withoutApp ? `/${withoutApp}` : ''}`;
}

export function deleteMatchingCookies(cookies: any, pattern: string) {
	// Get all cookie names
	const cookieNames = Object.keys(cookies.getAll());

	// Delete all cookies that match the pattern
	cookieNames.forEach((name) => {
		if (name.startsWith(pattern)) {
			cookies.delete(name, { path: '/' });
		}
	});
}

export function deleteAllCookies() {
	const cookies = document.cookie.split(';');

	for (let i = 0; i < cookies.length; i++) {
		const cookie = cookies[i];
		const eqPos = cookie.indexOf('=');
		const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
		document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/';
	}
}
