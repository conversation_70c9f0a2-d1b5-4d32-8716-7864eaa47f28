import OpenAI from 'openai';
import { PRIVATE_OPENAI_API_KEY } from '$env/static/private';

const openai = new OpenAI({
	apiKey: PRIVATE_OPENAI_API_KEY
});

export default openai;

/**
 * Generates an embedding vector for the given text using OpenAI's text-embedding-3-large model
 * @param text The text to generate an embedding for
 * @returns A 1024-dimensional vector representing the text
 */
export async function generateEmbedding(text: string): Promise<number[]> {
	console.log('🔧 [generateEmbedding] Start - Query:', text);
	if (!text) {
		console.error('❌ [generateEmbedding] No text provided!');
		throw new Error('Text is required for generating embeddings.');
	}

	console.log('🔎 [generateEmbedding] Requesting embedding from OpenAI...');
	const response = await fetch('https://api.openai.com/v1/embeddings', {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${PRIVATE_OPENAI_API_KEY}`
		},
		body: JSON.stringify({
			model: 'text-embedding-3-large',
			input: text,
			dimensions: 1024
		})
	});

	if (!response.ok) {
		const errorDetails = await response.text();
		console.error('❌ [generateEmbedding] Failed to generate embedding:', errorDetails);
		throw new Error(`Failed to generate embedding: ${errorDetails}`);
	}

	const data = await response.json();
	console.log('✅ [generateEmbedding] Embedding generated successfully!');
	return data.data[0].embedding;
}
