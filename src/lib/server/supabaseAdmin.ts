import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { PRIVATE_SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';
import type { Database } from '../../../database.types';
import fs from 'fs';
import path from 'path';

if (!PUBLIC_SUPABASE_URL || !PRIVATE_SUPABASE_SERVICE_ROLE_KEY) {
	throw new Error(
		`Missing required environment variables: ${!PUBLIC_SUPABASE_URL ? 'PUBLIC_SUPABASE_URL' : ''} ${
			!PRIVATE_SUPABASE_SERVICE_ROLE_KEY ? 'PRIVATE_SUPABASE_SERVICE_ROLE_KEY' : ''
		}`
	);
}

// Configure SSL options based on environment
const getConfig = () => {
	const config: any = {
		auth: {
			autoRefreshToken: true,
			persistSession: true
		}
	};

	// Only add SSL configuration in staging/production
	if (process.env.NODE_ENV !== 'development') {
		try {
			// Optional SSL cert handling - only if environment variable is provided
			const sslCert = process.env.SUPABASE_SSL_CERT;
			if (sslCert) {
				let cert: string;

				// Check if the cert is a base64-encoded string (Vercel environment variable)
				if (sslCert.includes('BEGIN CERTIFICATE')) {
					cert = sslCert;
				} else if (sslCert.match(/^[A-Za-z0-9+/=]+$/)) {
					// Decode base64 certificate
					cert = Buffer.from(sslCert, 'base64').toString('utf-8');
				} else {
					// Assume it's a file path
					const certPath = path.resolve(process.cwd(), sslCert);
					cert = fs.readFileSync(certPath, 'utf-8');
				}

				config.db = {
					ssl: {
						rejectUnauthorized: true,
						ca: cert
					}
				};
			}
		} catch (error) {
			console.warn('Failed to load SSL certificate:', error);
		}
	}

	return config;
};

// This client should only be used in server-side code
export const supabaseAdmin = createClient<Database>(
	PUBLIC_SUPABASE_URL,
	PRIVATE_SUPABASE_SERVICE_ROLE_KEY,
	getConfig()
);
