/**
 * file_access_functions.ts
 *
 * This file contains isolated functions for working with files and access permissions:
 * 1. getCompanyFiles - Returns all files belonging to a company
 * 2. getFileAccessUsers - Queries Microsoft Graph API for users with access to a file
 */

import { supabaseAdmin } from "./supabaseAdmin";
import { Client } from "@microsoft/microsoft-graph-client";
import { ClientSecretCredential } from "@azure/identity";
import type { Group, User } from "@microsoft/microsoft-graph-types";
import { env } from "process";
import { PUBLIC_AZURE_CLIENT_ID } from "$env/static/public";
import { PRIVATE_AZURE_CLIENT_SECRET } from "$env/static/private";
import { getSecret, getSecretById } from "./vault";
import { exchangeRefreshToken, sharepointScopes } from "./microsoft";

// Database Types (these would normally be imported from your database.types.ts)
export interface File {
  id: string;
  name: string;
  external_id: string | null;
  company_id: string;
  data_source_id: string | null;
  created_at: string;
  updated_at: string;
}

export interface DataSource {
  id: string;
  name: string;
  type: string;
  drive_id: string | null;
  company_id: string;
  created_at: string;
  updated_at: string;
}

export interface Company {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

export interface UserAccess {
  userId: string;
  userPrincipalName: string;
  displayName: string;
  email: string;
  accessType: "direct" | "group";
  groupName?: string;
}

/**
 * Returns all files belonging to a company
 *
 * @param companyId - The ID of the company
 * @param supabaseUrl - Supabase URL
 * @param supabaseKey - Supabase API key
 * @returns Promise with an array of File objects
 */
export async function getCompanyFiles(
  companyId: string,
): Promise<File[]> {
  // Create Supabase client

  try {
    // Query files table for all files belonging to the company
    const { data, error } = await supabaseAdmin
      .from("files")
      .select("*")
      .eq("company_id", companyId);

    if (error) {
      throw new Error(`Error fetching company files: ${error.message}`);
    }

    return data as File[];
  } catch (error) {
    console.error("Error in getCompanyFiles:", error);
    throw error;
  }
}

/**
 * Queries Microsoft Graph API to find which Microsoft accounts have access to a file,
 * including direct access and access through group membership
 *
 * @param fileExternalId - The external ID of the file in Microsoft (SharePoint/OneDrive)
 * @param driveId - The drive ID from data_sources table
 * @param dataSourceId - The ID of the data source to retrieve the ID token
 * @returns Promise with an array of UserAccess objects
 */
export async function getFileAccessUsers(
  fileExternalId: string,
  driveId: string,
  dataSourceId: string,
): Promise<UserAccess[]> {
  const clientId = PUBLIC_AZURE_CLIENT_ID;
  const clientSecret = PRIVATE_AZURE_CLIENT_SECRET;

  if (!clientId || !clientSecret) {
    throw new Error(
      "Microsoft Graph API credentials not configured. Please set AZURE_CLIENT_ID and AZURE_CLIENT_SECRET environment variables.",
    );
  }

  try {
    // Get the data source to retrieve the ID token
    const { data: dataSource, error: dsError } = await supabaseAdmin
      .from("data_sources")
      .select("id_token, secret_oauth, site_id, drive_id, platform_type")
      .eq("id", dataSourceId)
      .single();

    if (dsError || !dataSource) {
      throw new Error(
        `Error fetching data source: ${
          dsError?.message || "Data source not found"
        }`,
      );
    }

    if (!dataSource.secret_oauth) {
      throw new Error("Data source has no auth secret");
    }

    // Convert string IDs to UUID format if needed for vault operations
    const secretOAuth = dataSource.secret_oauth;
    const idToken = dataSource.id_token;

    const secret = await getSecretById(secretOAuth);
    const id_token = idToken ? await getSecretById(idToken) : null;

    if (!secret || !id_token) {
      throw new Error("Data source has no auth secret or ID token");
    }

    // Exchange refresh token for an access token
    console.log("Exchanging refresh token for access token");
    const accessToken = await exchangeRefreshToken(
      secret as string,
      sharepointScopes.join(" "),
    );

    if (!accessToken) {
      throw new Error("Failed to obtain access token from refresh token");
    }

    console.log("Successfully obtained access token");
    // Create a Graph client using the access token
    const graphClient = Client.initWithMiddleware({
      authProvider: {
        getAccessToken: async () => {
          return accessToken;
        },
      },
    });

    // Extract tenant ID from the id_token if it's a JWT
    let tenantId = "unknown";
    try {
      if (id_token && id_token.includes(".")) {
        const tokenParts = id_token.split(".");
        if (tokenParts.length === 3) {
          const payload = JSON.parse(
            Buffer.from(tokenParts[1], "base64").toString(),
          );
          tenantId = payload.tid || "unknown";
          console.log(`Operating with tenant ID: ${tenantId}`);
        }
      }
    } catch (error) {
      console.warn("Could not extract tenant ID from token:", error);
    }

    // Get permissions for the specific file
    console.log(
      `Requesting permissions for file ${fileExternalId} in drive ${driveId}`,
    );

    let permissions;
    try {
      permissions = await graphClient
        .api(`/drives/${driveId}/items/${fileExternalId}/permissions`)
        .get();
      console.log(`Retrieved ${permissions.value?.length || 0} permissions`);
    } catch (error) {
      console.error("Error retrieving file permissions:", error);
      throw new Error(
        `Failed to get file permissions: ${(error as Error).message}`,
      );
    }

    const userAccess: UserAccess[] = [];

    // Helper function to get members of a SharePoint site group
    async function getSiteGroupMembers(displayName: string): Promise<void> {
      if (!displayName || !dataSource?.site_id) {
        // If we don't have a site ID or display name, add a placeholder entry
        if (displayName) {
          userAccess.push({
            userId: `site-group-${
              displayName.replace(/\s+/g, "-").toLowerCase()
            }`,
            userPrincipalName: displayName,
            displayName: displayName,
            email: `${displayName}@sharepoint`,
            accessType: "group",
            groupName: displayName,
          });
        }
        return;
      }

      try {
        console.log(
          `Fetching information about SharePoint site group: ${displayName}`,
        );

        // Instead of using the problematic siteGroups API,
        // we'll query the User Information List directly
        // This approach has been tested and works well
        try {
          // First, get all items from the User Information List
          const userInfoList = await graphClient
            .api(
              `/sites/${dataSource.site_id}/lists/User Information List/items?expand=fields`,
            )
            .headers({
              "Prefer": "HonorNonIndexedQueriesWarningMayFailRandomly",
            })
            .get();

          console.log(
            `Retrieved ${
              userInfoList.value?.length || 0
            } items from User Information List`,
          );

          if (!userInfoList.value || userInfoList.value.length === 0) {
            throw new Error("No items found in User Information List");
          }

          // Find our group in the User Information List
          const groupItems = userInfoList.value.filter((item: any) =>
            item.fields?.ContentType === "SharePointGroup" &&
            item.fields?.Title === displayName
          );

          if (groupItems.length === 0) {
            console.log(
              `Could not find group "${displayName}" in User Information List`,
            );
            throw new Error(`Group "${displayName}" not found`);
          }

          const groupItem = groupItems[0];
          console.log(`Found group "${displayName}" in User Information List`);

          // Find all Person items that might be members of this group
          const personItems = userInfoList.value.filter((item: any) =>
            item.fields?.ContentType === "Person" &&
            item.fields?.EMail // Only include users with an email
          );

          if (personItems.length === 0) {
            console.log(`No user items found in User Information List`);

            // Add just the group as a placeholder
            userAccess.push({
              userId: `site-group-${
                displayName.replace(/\s+/g, "-").toLowerCase()
              }`,
              userPrincipalName: displayName,
              displayName: displayName,
              email: `${displayName}@sharepoint`,
              accessType: "group",
              groupName: displayName,
            });

            return;
          }

          console.log(`Found ${personItems.length} potential user items`);

          // Add all users as potential members
          // In a future improvement, we could try to determine actual membership
          // but for now, we'll include all users with a note that they're potential members
          let usersAdded = 0;
          for (const personItem of personItems) {
            // Skip system accounts
            if (
              personItem.fields?.Title?.includes("System Account") ||
              personItem.fields?.Title?.startsWith("NT ") ||
              !personItem.fields?.EMail
            ) {
              continue;
            }

            userAccess.push({
              userId: personItem.fields?.ID || personItem.id ||
                `site-user-${
                  personItem.fields?.Title?.replace(/\s+/g, "-").toLowerCase()
                }`,
              userPrincipalName: personItem.fields?.Name ||
                personItem.fields?.Title || "",
              displayName: personItem.fields?.Title || "",
              email: personItem.fields?.EMail ||
                `${personItem.fields?.Title}@sharepoint`,
              accessType: "group",
              groupName: `${displayName} (possible member)`,
            });
            usersAdded++;
          }

          console.log(
            `Added ${usersAdded} users as potential members of group "${displayName}"`,
          );

          if (usersAdded === 0) {
            // If we didn't add any users, add the group itself as a placeholder
            userAccess.push({
              userId: `site-group-${
                displayName.replace(/\s+/g, "-").toLowerCase()
              }`,
              userPrincipalName: displayName,
              displayName: displayName,
              email: `${displayName}@sharepoint`,
              accessType: "group",
              groupName: displayName,
            });
          }
        } catch (uiListError) {
          console.warn(
            `Error retrieving information from User Information List:`,
            uiListError,
          );

          // Fall back to adding just the group as a placeholder
          userAccess.push({
            userId: `site-group-${
              displayName.replace(/\s+/g, "-").toLowerCase()
            }`,
            userPrincipalName: displayName,
            displayName: displayName,
            email: `${displayName}@sharepoint`,
            accessType: "group",
            groupName: displayName,
          });
        }
      } catch (error) {
        console.warn(
          `Error retrieving members of site group ${displayName}:`,
          error,
        );

        // Fall back to adding just the group as a placeholder
        userAccess.push({
          userId: `site-group-${
            displayName.replace(/\s+/g, "-").toLowerCase()
          }`,
          userPrincipalName: displayName,
          displayName: displayName,
          email: `${displayName}@sharepoint`,
          accessType: "group",
          groupName: displayName,
        });
      }
    }

    // Process each permission
    if (!permissions.value || permissions.value.length === 0) {
      console.log("No permissions found for this file");
      return [];
    }

    for (const permission of permissions.value) {
      if (!permission.grantedTo) {
        console.log("Permission has no grantedTo property:", permission);
        continue;
      }

      // Handle user permission
      if (permission.grantedTo.user && permission.grantedTo.user.id) {
        try {
          // Get detailed user information from Graph API
          const user = await graphClient
            .api(`/users/${permission.grantedTo.user.id}`)
            .select("id,userPrincipalName,displayName,mail")
            .get() as User;

          userAccess.push({
            userId: user.id!,
            userPrincipalName: user.userPrincipalName!,
            displayName: user.displayName || "",
            email: user.mail || user.userPrincipalName!,
            accessType: "direct",
          });
        } catch (error) {
          console.warn(
            `Could not retrieve user details for ID ${permission.grantedTo.user.id}:`,
            error,
          );
        }
      } else if (permission.grantedTo.user) {
        // This is likely a SharePoint site group rather than a user
        const displayName = permission.grantedTo.user.displayName;
        console.log(
          "SharePoint site group found:",
          displayName,
        );

        // Try to get members of this SharePoint site group if we have a site ID
        await getSiteGroupMembers(displayName);
      }

      // Handle group permission
      if (permission.grantedTo.group && permission.grantedTo.group.id) {
        try {
          // Get group details
          const group = await graphClient
            .api(`/groups/${permission.grantedTo.group.id}`)
            .select("id,displayName,mail")
            .get() as Group;

          // Get all members of the group
          const members = await graphClient
            .api(`/groups/${permission.grantedTo.group.id}/members`)
            .select("id,userPrincipalName,displayName,mail")
            .get();

          // Add each group member to the access list
          for (const member of members.value) {
            userAccess.push({
              userId: member.id!,
              userPrincipalName: member.userPrincipalName!,
              displayName: member.displayName || "",
              email: member.mail || member.userPrincipalName!,
              accessType: "group",
              groupName: group.displayName || "",
            });
          }
        } catch (error) {
          console.warn(
            `Could not retrieve group details for ID ${permission.grantedTo.group.id}:`,
            error,
          );
        }
      } else if (permission.grantedTo.group) {
        // This might be a SharePoint site group without a proper ID
        const displayName = permission.grantedTo.group.displayName;
        console.log(
          "SharePoint site group found in group property:",
          displayName,
        );

        // Try to get members of this SharePoint site group if we have a site ID
        await getSiteGroupMembers(displayName);
      }
    }

    // Remove duplicates (a user might be in multiple groups)
    const uniqueUsers = userAccess.filter((user, index, self) =>
      index === self.findIndex((u) => u.userId === user.userId)
    );

    return uniqueUsers;
  } catch (error) {
    console.error("Error in getFileAccessUsers:", error);
    throw error;
  }
}

/**
 * Checks if a user with the specified email has access to each file in the fileIds list
 * and returns only the fileIds that the user can access.
 *
 * @param userEmail - The email of the user to check access for
 * @param fileIds - Array of file IDs to check access for
 * @returns Promise with an array of file IDs the user has access to
 */
export async function filterFilesByUserAccess(
  userEmail: string,
  fileIds: string[],
): Promise<string[]> {
  if (!userEmail || fileIds.length === 0) {
    return [];
  }

  try {
    console.log(
      `Checking access for user ${userEmail} to ${fileIds.length} files`,
    );

    // Get all the files from the database using external_id
    const { data: files, error } = await supabaseAdmin
      .from("files")
      .select("id, external_id, data_source_id")
      .in("external_id", fileIds);

    if (error) {
      throw new Error(`Error fetching files: ${error.message}`);
    }

    if (!files || files.length === 0) {
      console.log("No files found with the specified external IDs");
      return [];
    }

    console.log(`Retrieved ${files.length} files from database`);

    // Group files by data source to minimize API calls
    const filesByDataSource: Record<string, any[]> = {};
    for (const file of files) {
      if (!file.data_source_id || !file.external_id) continue;

      if (!filesByDataSource[file.data_source_id]) {
        filesByDataSource[file.data_source_id] = [];
      }
      filesByDataSource[file.data_source_id].push(file);
    }

    const accessibleFileIds: string[] = [];

    // Process each data source and its files
    for (
      const [dataSourceId, dataSourceFiles] of Object.entries(filesByDataSource)
    ) {
      // Get the data source to retrieve drive ID
      const { data: dataSource, error: dsError } = await supabaseAdmin
        .from("data_sources")
        .select("drive_id")
        .eq("id", dataSourceId)
        .single();

      if (dsError || !dataSource || !dataSource.drive_id) {
        console.warn(
          `Skipping data source ${dataSourceId}: ${
            dsError?.message || "No drive ID"
          }`,
        );
        continue;
      }

      // Process files in batches to avoid too many parallel requests
      const batchSize = 5;
      for (let i = 0; i < dataSourceFiles.length; i += batchSize) {
        const batch = dataSourceFiles.slice(i, i + batchSize);

        // Process files in the current batch in parallel
        const batchResults = await Promise.all(
          batch.map(async (file) => {
            try {
              // Get all users with access to this file
              const usersWithAccess = await getFileAccessUsers(
                file.external_id,
                dataSource.drive_id,
                dataSourceId,
              );

              // Check if our user is in the list (case insensitive email comparison)
              const userHasAccess = usersWithAccess.some((user) =>
                user.email.toLowerCase() === userEmail.toLowerCase()
              );

              if (userHasAccess) {
                console.log(
                  `User ${userEmail} has access to file ${file.external_id}`,
                );
                // Return the external_id since that's what the client is using
                return file.external_id;
              } else {
                console.log(
                  `User ${userEmail} does NOT have access to file ${file.external_id}`,
                );
                return null;
              }
            } catch (error) {
              console.warn(
                `Error checking access for file ${file.external_id}:`,
                error,
              );
              return null;
            }
          }),
        );

        // Add accessible files to our result
        for (const fileId of batchResults) {
          if (fileId) accessibleFileIds.push(fileId);
        }
      }
    }

    console.log(
      `User ${userEmail} has access to ${accessibleFileIds.length} out of ${fileIds.length} files`,
    );
    return accessibleFileIds;
  } catch (error) {
    console.error("Error in filterFilesByUserAccess:", error);
    throw error;
  }
}

/**
 * Example usage:
 *
 * // Get all files for a company
 * const files = await getCompanyFiles('company-123');
 *
 * // Get users with access to a specific file
 * const usersWithAccess = await getFileAccessUsers(
 *   'file-external-id',
 *   'drive-id',
 *   'data-source-id' // This retrieves the OAuth token and other credentials from the data source
 * );
 */
