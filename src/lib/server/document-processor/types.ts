import type { Database } from "../../../database.types";

export type FileRecord = Database["public"]["Tables"]["files"]["Row"];
export type DocumentRecord = Database["public"]["Tables"]["documents"]["Row"];

export interface ProcessedDocument {
  content: string;
  metadata: {
    pageCount?: number;
    wordCount?: number;
    language?: string;
    author?: string;
    createdAt?: string;
    modifiedAt?: string;
    // Additional PowerPoint metadata
    title?: string;
    subject?: string;
    keywords?: string[];
    category?: string;
    status?: string;
    company?: string;
    imageCount?: number;
    tableCount?: number;
  };
}

export interface DocumentProcessor {
  canProcess(mimeType: string): boolean;
  process(buffer: Buffer): Promise<ProcessedDocument>;
  generateSummary(
    content: string,
    metadata?: ProcessedDocument["metadata"],
  ): Promise<string>;
  splitIntoChunks(text: string): string[];
  processChunks(fileId: string, chunks: string[]): Promise<void>;
}

export type SupportedMimeTypes =
  | "application/pdf"
  | "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
  | "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  | "application/vnd.openxmlformats-officedocument.presentationml.presentation"
  | "text/plain"
  | "text/csv"
  | "application/json"
  | "text/markdown";

export interface ProcessingOptions {
  shouldGenerateSummary?: boolean;
  maxSummaryLength?: number;
  chunkSize?: number;
  overlapSize?: number;
  shouldExtractMetadata?: boolean;
  shouldDetectLanguage?: boolean;
}
