import { BaseDocumentProcessor } from "./base-processor";
import type { ProcessedDocument } from "./types";
import * as pdfjsLib from "pdfjs-dist";

export class PDFProcessor extends BaseDocumentProcessor {
  canProcess(mimeType: string): boolean {
    return mimeType === "application/pdf";
  }

  async process(buffer: Buffer): Promise<ProcessedDocument> {
    try {
      // Load the PDF document
      const data = new Uint8Array(buffer);
      const loadingTask = pdfjsLib.getDocument({ data });
      const pdfDocument = await loadingTask.promise;

      // Extract metadata
      const metadata: ProcessedDocument["metadata"] = {
        pageCount: pdfDocument.numPages,
        wordCount: 0,
      };

      // Extract text from all pages
      let fullText = "";
      for (let i = 1; i <= pdfDocument.numPages; i++) {
        const page = await pdfDocument.getPage(i);
        const content = await page.getTextContent();
        const pageText = content.items
          .map((item: any) => item.str)
          .join(" ")
          .replace(/\s+/g, " ");
        fullText += pageText + "\n\n";
      }

      // Clean up the text
      fullText = fullText
        .replace(/\n{3,}/g, "\n\n") // Remove excessive newlines
        .trim();

      // Calculate word count
      metadata.wordCount = fullText.split(/\s+/).length;

      // Detect language if enabled
      if (this.options.shouldDetectLanguage) {
        metadata.language = this.detectLanguage(fullText);
      }

      // Extract PDF-specific metadata if available
      const info = await pdfDocument.getMetadata();
      if (info?.info) {
        metadata.author = info.info.Author || undefined;
        metadata.createdAt = info.info.CreationDate
          ? this.parseAdobeDate(info.info.CreationDate)
          : undefined;
        metadata.modifiedAt = info.info.ModDate
          ? this.parseAdobeDate(info.info.ModDate)
          : undefined;
      }

      return {
        content: fullText,
        metadata,
      };
    } catch (error) {
      console.error("Error processing PDF:", error);
      throw new Error("Failed to process PDF document");
    }
  }

  private parseAdobeDate(adobeDate: string): string {
    // Adobe's PDF date format: D:YYYYMMDDHHmmSSOHH'mm'
    // Example: D:20240209121732+02'00'
    try {
      if (!adobeDate.startsWith("D:")) return new Date().toISOString();

      const year = adobeDate.substring(2, 6);
      const month = adobeDate.substring(6, 8);
      const day = adobeDate.substring(8, 10);
      const hour = adobeDate.substring(10, 12);
      const minute = adobeDate.substring(12, 14);
      const second = adobeDate.substring(14, 16);
      const tzOffset = adobeDate.substring(16, 17) +
        adobeDate.substring(17, 19) + ":" + adobeDate.substring(20, 22);

      return new Date(
        `${year}-${month}-${day}T${hour}:${minute}:${second}${tzOffset}`,
      ).toISOString();
    } catch {
      return new Date().toISOString();
    }
  }
}
