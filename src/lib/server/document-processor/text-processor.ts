import { BaseDocumentProcessor } from "./base-processor";
import type { ProcessedDocument } from "./types";

export class TextProcessor extends BaseDocumentProcessor {
  canProcess(mimeType: string): boolean {
    return [
      "text/plain",
      "text/markdown",
      "text/csv",
      "application/json",
    ].includes(mimeType);
  }

  async process(buffer: Buffer): Promise<ProcessedDocument> {
    try {
      // Convert buffer to text
      const fullText = buffer.toString("utf-8");

      // Basic text cleanup
      const cleanedText = fullText
        .replace(/\r\n/g, "\n") // Normalize line endings
        .replace(/\n{3,}/g, "\n\n") // Remove excessive newlines
        .trim();

      // Extract metadata
      const metadata: ProcessedDocument["metadata"] = {
        wordCount: cleanedText.split(/\s+/).length,
      };

      // Detect language if enabled
      if (this.options.shouldDetectLanguage) {
        metadata.language = this.detectLanguage(cleanedText);
      }

      // Try to extract creation time from file stats if available
      try {
        const lines = cleanedText.split("\n");

        // Look for common metadata patterns in the first few lines
        const firstLines = lines.slice(0, 10).join("\n").toLowerCase();

        // Try to find author
        const authorPatterns = [
          /author:\s*([^\n]+)/i,
          /by:\s*([^\n]+)/i,
          /written by:\s*([^\n]+)/i,
        ];

        for (const pattern of authorPatterns) {
          const match = firstLines.match(pattern);
          if (match) {
            metadata.author = match[1].trim();
            break;
          }
        }

        // Try to find dates
        const datePatterns = [
          /date:\s*([^\n]+)/i,
          /created:\s*([^\n]+)/i,
          /modified:\s*([^\n]+)/i,
          /updated:\s*([^\n]+)/i,
          /\d{4}-\d{2}-\d{2}/, // ISO date format
          /\d{2}\/\d{2}\/\d{4}/, // DD/MM/YYYY
          /\d{2}\.\d{2}\.\d{4}/, // DD.MM.YYYY
        ];

        for (const pattern of datePatterns) {
          const match = firstLines.match(pattern);
          if (match) {
            const dateStr = match[1] || match[0];
            const date = new Date(dateStr);
            if (!isNaN(date.getTime())) {
              metadata.createdAt = date.toISOString();
              break;
            }
          }
        }
      } catch (metadataError) {
        console.warn("Failed to extract text metadata:", metadataError);
      }

      return {
        content: cleanedText,
        metadata,
      };
    } catch (error) {
      console.error("Error processing text document:", error);
      throw new Error("Failed to process text document");
    }
  }
}
