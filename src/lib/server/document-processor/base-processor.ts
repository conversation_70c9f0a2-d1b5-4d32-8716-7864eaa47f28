import type { ProcessedDocument, ProcessingOptions } from "./types";
import { generateEmbedding } from "../openai";
import { supabaseAdmin } from "../supabaseAdmin";

export abstract class BaseDocumentProcessor {
  protected options: ProcessingOptions;

  constructor(options: ProcessingOptions = {}) {
    this.options = {
      shouldGenerateSummary: true,
      maxSummaryLength: 1000,
      chunkSize: 1000,
      overlapSize: 200,
      shouldExtractMetadata: true,
      shouldDetectLanguage: true,
      ...options,
    };
  }

  abstract canProcess(mimeType: string): boolean;
  abstract process(buffer: Buffer): Promise<ProcessedDocument>;

  protected async generateSummary(
    content: string,
    metadata?: ProcessedDocument["metadata"],
  ): Promise<string> {
    const contextPrompt = metadata
      ? `
Document Type: ${metadata.mimeType || "Unknown"}
Pages: ${metadata.pageCount || "Unknown"}
Words: ${metadata.wordCount || "Unknown"}
Language: ${metadata.language || "Unknown"}
Author: ${metadata.author || "Unknown"}
Created: ${metadata.createdAt || "Unknown"}
    `.trim()
      : "";

    const prompt =
      `Please provide a comprehensive summary of the following document. 
Focus on:
- Main topics and key points
- Important findings or conclusions
- Relevant dates, numbers, or statistics
- Key relationships or dependencies mentioned
- Action items or recommendations (if any)

Document Metadata:
${contextPrompt}

Document Content:
${content.slice(0, 3000)}

Format the summary with:
- Clear section headings
- Bullet points for key information
- Preserve important numerical data
- Maintain chronological order where relevant`;

    try {
      const response = await fetch(
        "https://api.openai.com/v1/chat/completions",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${process.env.OPENAI_API_KEY}`,
          },
          body: JSON.stringify({
            model: "gpt-4",
            messages: [
              {
                role: "system",
                content:
                  `You are a precise document summarizer with expertise in extracting key information from business documents.
Your summaries should:
- Be clear and well-structured
- Preserve important details and relationships
- Maintain professional language
- Be factual and objective
- Include relevant metadata when available`,
              },
              { role: "user", content: prompt },
            ],
            max_tokens: this.options.maxSummaryLength,
            temperature: 0.3,
          }),
        },
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(
          `OpenAI API error: ${error.error?.message || "Unknown error"}`,
        );
      }

      const data = await response.json();
      if (!data.choices?.[0]?.message?.content) {
        throw new Error("Invalid response from OpenAI API");
      }

      return data.choices[0].message.content.trim();
    } catch (error) {
      console.error("Error generating summary:", error);
      // Return a basic summary based on the first few sentences
      return content
        .split(/[.!?]/)
        .slice(0, 3)
        .join(". ")
        .trim() + "...";
    }
  }

  protected splitIntoChunks(text: string): string[] {
    const chunks: string[] = [];
    const sentences = text.split(/(?<=[.!?])\s+/);
    let currentChunk = "";

    for (const sentence of sentences) {
      if ((currentChunk + sentence).length <= this.options.chunkSize!) {
        currentChunk += (currentChunk ? " " : "") + sentence;
      } else {
        if (currentChunk) chunks.push(currentChunk);
        currentChunk = sentence;
      }
    }

    if (currentChunk) chunks.push(currentChunk);
    return chunks;
  }

  protected async processChunks(
    fileId: string,
    chunks: string[],
  ): Promise<void> {
    // First verify the file exists
    const { data: file, error: fileError } = await supabaseAdmin
      .from("files")
      .select("id, company_id")
      .eq("id", fileId)
      .single();

    if (fileError || !file) {
      throw new Error(`File ${fileId} not found or not accessible`);
    }

    // Process chunks with embeddings
    await Promise.all(chunks.map(async (chunk, index) => {
      const embedding = await generateEmbedding(chunk);

      const metadata = {
        company_id: file.company_id,
        file_id: fileId,
        chunk_index: index,
        total_chunks: chunks.length,
      };

      const { error: docError } = await supabaseAdmin.rpc("upsert_document", {
        p_id: crypto.randomUUID(),
        p_content: chunk,
        p_metadata: metadata,
        p_file_id: fileId,
        p_embedding: embedding,
      });

      if (docError) {
        throw new Error(`Failed to upsert document chunk: ${docError.message}`);
      }
    }));
  }

  protected detectLanguage(text: string): string {
    // Simple language detection - can be enhanced with a proper language detection library
    // This is a placeholder implementation
    const sample = text.slice(0, 1000).toLowerCase();

    // Common English words
    const englishWords = [
      "the",
      "be",
      "to",
      "of",
      "and",
      "a",
      "in",
      "that",
      "have",
      "i",
    ];
    const englishCount = englishWords.reduce(
      (count, word) =>
        count + (sample.match(new RegExp(`\\b${word}\\b`, "g"))?.length || 0),
      0,
    );

    // Add more language detection logic as needed
    return englishCount > 5 ? "en" : "unknown";
  }
}
