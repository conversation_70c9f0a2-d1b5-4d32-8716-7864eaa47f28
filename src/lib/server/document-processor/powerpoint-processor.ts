import { BaseDocumentProcessor } from "./base-processor";
import type { ProcessedDocument } from "./types";
import { Composer } from "nodejs-pptx";

export class PowerPointProcessor extends BaseDocumentProcessor {
  canProcess(mimeType: string): boolean {
    return mimeType ===
      "application/vnd.openxmlformats-officedocument.presentationml.presentation";
  }

  async process(buffer: Buffer): Promise<ProcessedDocument> {
    try {
      const pptx = new Composer();

      // Load the PowerPoint from buffer
      await pptx.load(buffer);

      // Process each slide
      const contentParts: string[] = [];
      const slides = await pptx.getSlides();
      let totalImages = 0;
      let totalTables = 0;

      for (let i = 0; i < slides.length; i++) {
        const slide = slides[i];
        contentParts.push(`\n## Slide ${i + 1}\n`);

        // Get slide layout and title if available
        const layout = await slide.getLayout();
        if (layout?.name) {
          contentParts.push(`Layout: ${layout.name}\n`);
        }

        // Extract text content from shapes with formatting
        const shapes = await slide.getShapes();
        for (const shape of shapes) {
          if (shape.text) {
            let shapeText = shape.text;

            // Add formatting context if available
            if (shape.type) {
              shapeText = `[${shape.type}] ${shapeText}`;
            }
            if (shape.style?.fontFamily || shape.style?.fontSize) {
              const styleInfo = `[Style: ${shape.style.fontFamily || ""} ${
                shape.style.fontSize || ""
              }]`;
              shapeText = `${styleInfo} ${shapeText}`;
            }

            contentParts.push(shapeText);
          }
        }

        // Extract text from text boxes with formatting
        const textBoxes = await slide.getTextboxes();
        for (const textBox of textBoxes) {
          if (textBox.text) {
            let textBoxContent = textBox.text;

            // Add formatting and position context
            if (textBox.style) {
              const styleInfo = `[TextBox Style: ${
                textBox.style.fontFamily || ""
              } ${textBox.style.fontSize || ""}]`;
              textBoxContent = `${styleInfo} ${textBoxContent}`;
            }

            contentParts.push(textBoxContent);
          }
        }

        // Extract tables if available
        try {
          const tables = await slide.getTables();
          for (const table of tables) {
            contentParts.push("\n[Table]");

            // Process table headers
            if (table.headers) {
              contentParts.push("Headers: " + table.headers.join(" | "));
            }

            // Process table rows
            if (table.rows) {
              table.rows.forEach((row: string[]) => {
                contentParts.push(row.join(" | "));
              });
            }

            totalTables++;
          }
        } catch (tableError) {
          console.warn(
            `Failed to extract tables from slide ${i + 1}:`,
            tableError,
          );
        }

        // Track images
        try {
          const images = await slide.getImages();
          if (images && images.length > 0) {
            contentParts.push(
              `\n[Images: ${images.length} found in this slide]`,
            );
            images.forEach((image) => {
              if (image.alt) {
                contentParts.push(`Image Alt Text: ${image.alt}`);
              }
              if (image.title) {
                contentParts.push(`Image Title: ${image.title}`);
              }
            });
            totalImages += images.length;
          }
        } catch (imageError) {
          console.warn(
            `Failed to extract images from slide ${i + 1}:`,
            imageError,
          );
        }

        // Extract notes if available
        const notes = await slide.getNotes();
        if (notes) {
          contentParts.push(`\nPresenter Notes:\n${notes}`);
        }

        // Extract footer if available
        try {
          const footer = await slide.getFooter();
          if (footer) {
            contentParts.push(`\nFooter: ${footer}`);
          }
        } catch (footerError) {
          console.warn(
            `Failed to extract footer from slide ${i + 1}:`,
            footerError,
          );
        }
      }

      // Join all content with proper spacing
      const fullText = contentParts.join("\n").trim();

      // Extract metadata
      const metadata: ProcessedDocument["metadata"] = {
        wordCount: fullText.split(/\s+/).length,
        pageCount: slides.length,
        ...await this.extractExtendedMetadata(pptx, totalImages, totalTables),
      };

      // Detect language if enabled
      if (this.options.shouldDetectLanguage) {
        metadata.language = this.detectLanguage(fullText);
      }

      return {
        content: fullText,
        metadata,
      };
    } catch (error) {
      console.error("Error processing PowerPoint document:", error);
      throw new Error("Failed to process PowerPoint document");
    }
  }

  private async extractExtendedMetadata(
    pptx: Composer,
    totalImages: number,
    totalTables: number,
  ) {
    try {
      const properties = await pptx.getProperties();
      const metadata: ProcessedDocument["metadata"] = {};

      if (properties) {
        metadata.author = properties.creator || undefined;
        metadata.createdAt = properties.created
          ? new Date(properties.created).toISOString()
          : undefined;
        metadata.modifiedAt = properties.modified
          ? new Date(properties.modified).toISOString()
          : undefined;

        // Add extended metadata
        metadata.title = properties.title;
        metadata.subject = properties.subject;
        metadata.keywords = properties.keywords;
        metadata.category = properties.category;
        metadata.status = properties.status;
        metadata.company = properties.company;
        metadata.imageCount = totalImages;
        metadata.tableCount = totalTables;
      }

      return metadata;
    } catch (metadataError) {
      console.warn("Failed to extract PowerPoint metadata:", metadataError);
      return {};
    }
  }

  protected splitIntoChunks(text: string): string[] {
    // Override default chunk splitting to maintain slide structure
    const chunks: string[] = [];
    const slides = text.split(/(?=\n## Slide \d+\n)/);

    slides.forEach((slide) => {
      if (!slide.trim()) return;

      // If slide content is smaller than chunk size, keep it as one chunk
      if (slide.length <= this.options.chunkSize!) {
        chunks.push(slide);
        return;
      }

      // Split larger slides while trying to preserve logical sections
      const sections = slide.split(
        /\n(?=\[|Layout:|Headers:|Presenter Notes:|Footer:)/,
      );
      let currentChunk = sections[0]; // Start with slide header

      for (let i = 1; i < sections.length; i++) {
        const nextSection = sections[i];
        if (
          (currentChunk + "\n" + nextSection).length <= this.options.chunkSize!
        ) {
          currentChunk += "\n" + nextSection;
        } else {
          chunks.push(currentChunk);
          currentChunk = nextSection;
        }
      }

      if (currentChunk) {
        chunks.push(currentChunk);
      }
    });

    return chunks;
  }
}
