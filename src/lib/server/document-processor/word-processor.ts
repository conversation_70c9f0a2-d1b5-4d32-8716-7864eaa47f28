import { BaseDocumentProcessor } from "./base-processor";
import type { ProcessedDocument } from "./types";
import mammoth from "mammoth";

export class WordProcessor extends BaseDocumentProcessor {
  canProcess(mimeType: string): boolean {
    return mimeType ===
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
  }

  async process(buffer: Buffer): Promise<ProcessedDocument> {
    try {
      // Extract text and preserve some formatting
      const result = await mammoth.extractRawText({
        buffer: buffer,
        options: {
          includeDefaultStyleMap: true,
          preserveEmptyParagraphs: false,
        },
      });

      // Clean up the text
      let fullText = result.value
        .replace(/\n{3,}/g, "\n\n") // Remove excessive newlines
        .trim();

      // Extract metadata
      const metadata: ProcessedDocument["metadata"] = {
        wordCount: fullText.split(/\s+/).length,
      };

      // Detect language if enabled
      if (this.options.shouldDetectLanguage) {
        metadata.language = this.detectLanguage(fullText);
      }

      // Try to extract Word-specific metadata
      try {
        const metadataResult = await mammoth.extractRawText({
          buffer: buffer,
          options: { includeEmbeddedXml: true },
        });

        const xmlString = metadataResult.value;
        // Extract creation date
        const createdMatch = xmlString.match(/created="([^"]+)"/);
        if (createdMatch) {
          metadata.createdAt = new Date(createdMatch[1]).toISOString();
        }

        // Extract modified date
        const modifiedMatch = xmlString.match(/modified="([^"]+)"/);
        if (modifiedMatch) {
          metadata.modifiedAt = new Date(modifiedMatch[1]).toISOString();
        }

        // Extract author
        const authorMatch = xmlString.match(/author="([^"]+)"/);
        if (authorMatch) {
          metadata.author = authorMatch[1];
        }
      } catch (metadataError) {
        console.warn("Failed to extract Word metadata:", metadataError);
      }

      return {
        content: fullText,
        metadata,
      };
    } catch (error) {
      console.error("Error processing Word document:", error);
      throw new Error("Failed to process Word document");
    }
  }
}
