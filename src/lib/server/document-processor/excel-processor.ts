import { BaseDocumentProcessor } from "./base-processor";
import type { ProcessedDocument } from "./types";
import * as XLSX from "xlsx";

export class ExcelProcessor extends BaseDocumentProcessor {
  canProcess(mimeType: string): boolean {
    return mimeType ===
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
  }

  async process(buffer: Buffer): Promise<ProcessedDocument> {
    try {
      // Load workbook from buffer
      const workbook = XLSX.read(buffer, { type: "buffer" });

      // Initialize content array to store all sheet data
      const contentParts: string[] = [];

      // Process each sheet
      workbook.SheetNames.forEach((sheetName) => {
        const sheet = workbook.Sheets[sheetName];

        // Convert sheet to JSON for easier processing
        const jsonData = XLSX.utils.sheet_to_json(sheet, { header: 1 });

        // Add sheet name as a section header
        contentParts.push(`\n## Sheet: ${sheetName}\n`);

        // Process rows and columns
        jsonData.forEach((row: any[], rowIndex: number) => {
          if (row.length > 0) {
            // Convert row to a readable format
            const rowContent = row
              .map((cell) => cell?.toString() || "")
              .filter(Boolean)
              .join(" | ");

            if (rowContent.trim()) {
              contentParts.push(
                rowIndex === 0 ? `Headers: ${rowContent}` : rowContent,
              );
            }
          }
        });
      });

      // Join all content with proper spacing
      const fullText = contentParts.join("\n").trim();

      // Extract metadata
      const metadata: ProcessedDocument["metadata"] = {
        wordCount: fullText.split(/\s+/).length,
        pageCount: workbook.SheetNames.length, // Use sheet count as page count
      };

      // Try to extract Excel-specific metadata
      try {
        const props = workbook.Props;
        if (props) {
          metadata.author = props.Author || undefined;
          metadata.createdAt = props.CreatedDate
            ? new Date(props.CreatedDate).toISOString()
            : undefined;
          metadata.modifiedAt = props.ModifiedDate
            ? new Date(props.ModifiedDate).toISOString()
            : undefined;
        }
      } catch (metadataError) {
        console.warn("Failed to extract Excel metadata:", metadataError);
      }

      // Detect language if enabled
      if (this.options.shouldDetectLanguage) {
        metadata.language = this.detectLanguage(fullText);
      }

      return {
        content: fullText,
        metadata,
      };
    } catch (error) {
      console.error("Error processing Excel document:", error);
      throw new Error("Failed to process Excel document");
    }
  }

  protected splitIntoChunks(text: string): string[] {
    // Override default chunk splitting to maintain sheet structure
    const chunks: string[] = [];
    const sheets = text.split(/(?=\n## Sheet:)/);

    sheets.forEach((sheet) => {
      if (!sheet.trim()) return;

      // Split large sheets into smaller chunks while preserving row integrity
      const rows = sheet.split("\n");
      let currentChunk = rows[0]; // Start with sheet header

      for (let i = 1; i < rows.length; i++) {
        const nextRow = rows[i];
        if ((currentChunk + "\n" + nextRow).length <= this.options.chunkSize!) {
          currentChunk += "\n" + nextRow;
        } else {
          chunks.push(currentChunk);
          currentChunk = nextRow;
        }
      }

      if (currentChunk) {
        chunks.push(currentChunk);
      }
    });

    return chunks;
  }
}
