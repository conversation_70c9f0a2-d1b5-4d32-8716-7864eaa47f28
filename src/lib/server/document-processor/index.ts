import type {
  DocumentProcessor,
  ProcessedDocument,
  ProcessingOptions,
} from "./types";
import { PDFProcessor } from "./pdf-processor";
import { WordProcessor } from "./word-processor";
import { TextProcessor } from "./text-processor";
import { ExcelProcessor } from "./excel-processor";
import { PowerPointProcessor } from "./powerpoint-processor";
import { supabaseAdmin } from "../supabaseAdmin";

export class DocumentProcessorService {
  private processors: Map<string, DocumentProcessor>;

  constructor() {
    this.processors = new Map();
    this.registerProcessors([
      new PDFProcessor(),
      new WordProcessor(),
      new TextProcessor(),
      new ExcelProcessor(),
      new PowerPointProcessor(),
    ]);
  }

  private registerProcessors(processors: DocumentProcessor[]) {
    const mimeTypes = [
      "application/pdf",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "application/vnd.openxmlformats-officedocument.presentationml.presentation",
      "text/plain",
      "text/markdown",
      "text/csv",
      "application/json",
    ];

    for (const mimeType of mimeTypes) {
      const processor = processors.find((p) => p.canProcess(mimeType));
      if (processor) {
        this.processors.set(mimeType, processor);
      }
    }
  }

  async processDocument(
    fileId: string,
    buffer: Buffer,
    mimeType: string,
    options: ProcessingOptions = {},
  ): Promise<void> {
    try {
      // 1. Verify file exists in files table
      const { data: file, error: fileError } = await supabaseAdmin
        .from("files")
        .select("id, company_id, name, path")
        .eq("id", fileId)
        .single();

      if (fileError || !file) {
        throw new Error(`File ${fileId} not found or not accessible`);
      }

      // 2. Get appropriate processor
      const processor = this.processors.get(mimeType);
      if (!processor) {
        throw new Error(`No processor available for MIME type: ${mimeType}`);
      }

      // 3. Process the document
      const processed: ProcessedDocument = await processor.process(buffer);

      // 4. Generate summary if enabled
      let summary = "";
      if (options.shouldGenerateSummary) {
        summary = await processor.generateSummary(
          processed.content,
          processed.metadata,
        );
      }

      // 5. Update file record with metadata and summary
      const { error: updateError } = await supabaseAdmin
        .from("files")
        .update({
          summary,
          metadata: processed.metadata,
          is_ingested: true,
          updated_at: new Date().toISOString(),
        })
        .eq("id", fileId);

      if (updateError) {
        throw new Error(
          `Failed to update file metadata: ${updateError.message}`,
        );
      }

      // 6. Process content chunks
      const chunks = processor.splitIntoChunks(processed.content);
      await processor.processChunks(fileId, chunks);

      console.log(`Successfully processed ${mimeType} file:`, {
        fileId,
        wordCount: processed.metadata.wordCount,
        chunks: chunks.length,
        summaryLength: summary.length,
      });
    } catch (error) {
      console.error("Error in document processing:", error);
      // Update file status to indicate processing error
      await supabaseAdmin
        .from("files")
        .update({
          is_ingested: false,
          processing_error: error instanceof Error
            ? error.message
            : "Unknown error",
          updated_at: new Date().toISOString(),
        })
        .eq("id", fileId);

      throw error;
    }
  }
}
