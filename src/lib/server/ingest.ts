import { supabaseAdmin } from "$lib/server/supabaseAdmin";
import { generateEmbedding } from "$lib/server/openai";
import type { Database } from "../../../database.types";

type FileMetadata = Database["public"]["Tables"]["files"]["Row"];
type DocumentMetadata = Database["public"]["Tables"]["documents"]["Row"];

interface ChunkMetadata {
  company_id: string;
  file_id: string;
  path: string;
}

export async function ingestFile(
  fileId: string,
  content: string,
  metadata: Partial<FileMetadata>,
  chunkSize: number = 1000,
): Promise<void> {
  try {
    // 1. Generate file-level metadata text for embedding
    const metadataText = [
      metadata.name,
      metadata.mime_type,
      metadata.summary,
      metadata.storage_provider,
    ].filter(Boolean).join(" ");

    // 2. Generate file-level embedding
    const fileEmbedding = await generateEmbedding(metadataText);

    // 3. Update file with embedding and metadata
    const { error: fileError } = await supabaseAdmin
      .from("files")
      .update({
        file_embedding: fileEmbedding,
        is_ingested: true,
        ...metadata,
      })
      .eq("id", fileId);

    if (fileError) {
      throw new Error(`Failed to update file: ${fileError.message}`);
    }

    // 4. Split content into chunks
    const chunks = splitIntoChunks(content, chunkSize);

    // 5. Process each chunk
    const chunkMetadata: ChunkMetadata = {
      company_id: metadata.company_id!,
      file_id: fileId,
      path: metadata.path!,
    };

    await Promise.all(
      chunks.map(async (chunk) => {
        // Generate embedding for chunk
        const embedding = await generateEmbedding(chunk);

        // Upsert document with chunk and embedding
        const { error: docError } = await supabaseAdmin.rpc("upsert_document", {
          p_id: crypto.randomUUID(),
          p_content: chunk,
          p_metadata: chunkMetadata,
          p_file_id: fileId,
          p_embedding: embedding,
        });

        if (docError) {
          throw new Error(`Failed to upsert document: ${docError.message}`);
        }
      }),
    );
  } catch (error) {
    console.error("Error in ingestFile:", error);
    throw error;
  }
}

function splitIntoChunks(text: string, chunkSize: number): string[] {
  const chunks: string[] = [];
  let currentChunk = "";
  const sentences = text.split(/(?<=[.!?])\s+/);

  for (const sentence of sentences) {
    if ((currentChunk + sentence).length <= chunkSize) {
      currentChunk += (currentChunk ? " " : "") + sentence;
    } else {
      if (currentChunk) chunks.push(currentChunk);
      currentChunk = sentence;
    }
  }

  if (currentChunk) chunks.push(currentChunk);
  return chunks;
}
