import {
  PUBL<PERSON>_SUPABASE_ANON_KEY,
  PUBLIC_SUPABASE_URL,
} from "$env/static/public";
import { SUPABASE_SERVICE_ROLE_KEY } from "$env/static/private";

export const env = {
  // Public variables (available on client and server)
  public: {
    supabaseUrl: PUBLIC_SUPABASE_URL,
    supabaseAnonKey: PUBLIC_SUPABASE_ANON_KEY,
  },
  // Private variables (server-side only)
  private: {
    supabaseServiceRole: SUPABASE_SERVICE_ROLE_KEY,
  },
} as const;
