import { writable } from 'svelte/store';

/**
 * Retrieves the initial companyId from localStorage or cookies.
 * This runs only on the client side.
 */
function getInitialCompanyId(): string | null {
	if (typeof window !== 'undefined') {
		// Check localStorage first
		const companyId = localStorage.getItem('companyId');
		if (companyId) {
			return companyId;
		}

		// Fallback to cookies
		const match = document.cookie.match(new RegExp('(?:^|; )companyId=([^;]*)'));
		return match ? decodeURIComponent(match[1]) : null;
	}

	return null;
}

export const companyStore = writable<string | null>(getInitialCompanyId());

/**
 * Retrieves the current companyId from the store.
 */
export function getCompanyId(): string | null {
	let currentCompanyId: string | null = null;
	companyStore.subscribe((value) => {
		currentCompanyId = value;
	})();
	return currentCompanyId;
}

/**
 * Sets the companyId in both localStorage and cookies, then updates the store.
 */
export function setCompanyId(id: string | null): void {
	if (typeof window !== 'undefined') {
		if (id === null) {
			localStorage.removeItem('companyId');
			document.cookie = 'companyId=; Max-Age=0; path=/;';
		} else {
			localStorage.setItem('companyId', id);
			document.cookie = `companyId=${id}; path=/;`;
		}
		companyStore.set(id);
	}
}

export function updateCompanyIdFromRoute(newCompanyId: string | null): void {
	setCompanyId(newCompanyId);
}
