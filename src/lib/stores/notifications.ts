import { writable } from "svelte/store";

export interface Notification {
  id: string;
  message: string;
  type: "info" | "warning" | "error" | "success";
  link?: string;
  linkText?: string;
  read: boolean;
  createdAt: Date;
}

function createNotificationsStore() {
  const { subscribe, set, update } = writable<Notification[]>([]);

  return {
    subscribe,
    add: (notification: Omit<Notification, "id" | "createdAt" | "read">) => {
      update((notifications) => [
        {
          ...notification,
          id: crypto.randomUUID(),
          createdAt: new Date(),
          read: false,
        },
        ...notifications,
      ]);
    },
    markAsRead: (id: string) => {
      update((notifications) =>
        notifications.map((n) => n.id === id ? { ...n, read: true } : n)
      );
    },
    markAllAsRead: () => {
      update((notifications) =>
        notifications.map((n) => ({ ...n, read: true }))
      );
    },
    remove: (id: string) => {
      update((notifications) => notifications.filter((n) => n.id !== id));
    },
    clear: () => set([]),
  };
}

export const notifications = createNotificationsStore();
