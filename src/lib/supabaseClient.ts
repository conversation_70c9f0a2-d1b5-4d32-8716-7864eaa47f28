import { createClient } from "@supabase/supabase-js";
import { env } from "$env/dynamic/public";

if (!env.PUBLIC_SUPABASE_URL || !env.PUBLIC_SUPABASE_ANON_KEY) {
  throw new Error("Missing Supabase environment variables");
}

// Configure client options
const getConfig = () => ({
  auth: {
    autoRefreshToken: true,
    persistSession: true,
  },
});

export const supabase = createClient(
  env.PUBLIC_SUPABASE_URL,
  env.PUBLIC_SUPABASE_ANON_KEY,
  getConfig(),
);
