declare module "nodejs-pptx" {
  export class Composer {
    constructor();
    load(buffer: Buffer): Promise<void>;
    getSlides(): Promise<Slide[]>;
    getProperties(): Promise<PresentationProperties>;
  }

  interface Slide {
    getLayout(): Promise<{ name?: string }>;
    getShapes(): Promise<Shape[]>;
    getTextboxes(): Promise<TextBox[]>;
    getTables(): Promise<Table[]>;
    getImages(): Promise<Image[]>;
    getNotes(): Promise<string | null>;
    getFooter(): Promise<string | null>;
  }

  interface Shape {
    text?: string;
    type?: string;
    style?: {
      fontFamily?: string;
      fontSize?: string | number;
    };
  }

  interface TextBox {
    text?: string;
    style?: {
      fontFamily?: string;
      fontSize?: string | number;
    };
  }

  interface Table {
    headers?: string[];
    rows?: string[][];
  }

  interface Image {
    alt?: string;
    title?: string;
  }

  interface PresentationProperties {
    creator?: string;
    created?: string | Date;
    modified?: string | Date;
    title?: string;
    subject?: string;
    keywords?: string[];
    category?: string;
    status?: string;
    company?: string;
  }
}
