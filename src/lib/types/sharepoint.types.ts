export interface SharePointSite {
	id: string;
	displayName: string;
	webUrl: string;
	siteCollection?: {
		hostname: string;
		dataLocationCode?: string;
		root?: {
			serverRelativeUrl: string;
		};
	};
	createdDateTime?: string;
	lastModifiedDateTime?: string;
	name?: string;
	description?: string;
}

export interface SitesResponse {
	value: SharePointSite[];
	'@odata.context'?: string;
	'@odata.count'?: number;
	'@odata.nextLink'?: string;
}

export interface DriveInfo {
	id: string;
	driveType: string;
	name: string;
	webUrl: string;
	createdDateTime: string;
	description: string;
	lastModifiedDateTime: string;
	quota?: {
		total: number;
		used: number;
		remaining: number;
		deleted: number;
		state: string;
	};
}
