export interface UpstackEntity {
	id: string;
	name: string;
	description: string;
	status: 'active' | 'inactive' | 'pending';
	metadata: Record<string, unknown>;
	created_at: string;
	updated_at: string;
}

export interface UpstackFile {
	id: string;
	name: string;
	path: string;
	size: number;
	contentType: string;
	url: string;
	createdAt: string;
	updatedAt: string;
	entityId: string;
	metadata?: Record<string, unknown>;
}

export interface UpstackTokenResponse {
	access_token: string;
	refresh_token: string;
	token_type: string;
	expires_in: number;
}

export interface UpstackErrorResponse {
	error: string;
	error_description?: string;
	message?: string;
	status: number;
	timestamp: string;
}

export interface UpstackIntegrationConfig {
	client_id: string;
	client_secret: string;
	redirect_uri: string;
	scope: string[];
}

export interface UpstackEntityCreationResponse {
	id: string;
	name: string;
	description: string;
	status: 'success' | 'error' | 'pending';
	message?: string;
}

export interface UpstackDocumentResponse {
	id: string;
	entityId: string;
	filename: string;
	contentType: string;
	size: number;
	url: string;
	createdAt: string;
	status: string;
	metadata?: Record<string, unknown>;
}

export interface UpstackUserCreationRequest {
	typeaction: 'org_create_user';
	sub_action: 'create_sub';
	email: string;
	token: string;
	user_list: Array<{
		firstName: string;
		user: string;
		package: 'content_creator';
	}>;
}

export interface UpstackUserDeletionRequest {
	typeaction: 'delete_user_org';
	email: string;
	token: string;
	email_to_delete: string;
}

export interface UpstackUserResponse {
	success: boolean;
	message?: string;
	error?: string;
}
