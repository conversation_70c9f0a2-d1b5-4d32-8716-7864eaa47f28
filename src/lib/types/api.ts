export interface APIErrorResponse {
  error: string;
  details?: Record<string, unknown>;
}

export interface APISuccessResponse<T> {
  success: true;
  data: T;
}

export type APIResponse<T> = APISuccessResponse<T> | APIErrorResponse;

export interface WhatsAppTokenExchangeResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
}

export interface WhatsAppBusinessDetails {
  phoneNumberId: string;
  wabaId: string;
}
