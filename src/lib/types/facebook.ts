export interface FacebookSDK {
  init(options: {
    appId: string;
    autoLogAppEvents?: boolean;
    xfbml?: boolean;
    version: string;
    status?: boolean;
  }): void;

  login(
    callback: (response: FacebookLoginResponse) => void,
    options: FacebookLoginOptions,
  ): void;
}

export interface FacebookLoginResponse {
  authResponse: {
    code?: string;
    accessToken?: string;
    userID?: string;
    expiresIn?: number;
    signedRequest?: string;
  } | null;
  status?: "connected" | "not_authorized" | "unknown";
}

export interface FacebookLoginOptions {
  config_id: string;
  response_type: "code" | "token";
  override_default_response_type: boolean;
  extras?: {
    setup?: Record<string, unknown>;
    feature_type?: string;
    session_info_version?: string;
  };
}
