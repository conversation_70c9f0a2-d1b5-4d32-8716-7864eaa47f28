// src/hooks.server.ts

import { createServerClient } from "@supabase/ssr";
import { type Handle, redirect } from "@sveltejs/kit";
import { sequence } from "@sveltejs/kit/hooks";
import {
	PUBLIC_SUPABASE_ANON_KEY,
	PUBLIC_SUPABASE_URL,
} from "$env/static/public";
import { updateCompanyIdFromRoute } from "$lib/stores/companyStore";
import { supabaseAdmin } from "$lib/server/supabaseAdmin";
import type { Database } from "../database.types";

// Define company type based on the database schema
type Company = Database["public"]["Tables"]["companies"]["Row"];

// Define company member type for proper typing
type CompanyMember = Database["public"]["Tables"]["company_members"]["Row"] & {
	companies: Company;
};

// Define company user view type for proper typing
type CompanyUser = Database["public"]["Views"]["company_user_profiles"]["Row"];

const clearAuthCookies = (cookies: any) => {
	// Clear Supabase auth cookies
	cookies.delete("sb-access-token", { path: "/" });
	cookies.delete("sb-refresh-token", { path: "/" });
};

const supabase: Handle = async ({ event, resolve }) => {
	const supabase = createServerClient(
		PUBLIC_SUPABASE_URL,
		PUBLIC_SUPABASE_ANON_KEY,
		{
			cookies: {
				getAll: () => event.cookies.getAll(),
				setAll: (cookiesToSet) => {
					cookiesToSet.forEach(({ name, value, options }) => {
						event.cookies.set(name, value, { ...options, path: "/" });
					});
				},
			},
		},
	);

	// Attach both Supabase clients to event.locals
	event.locals.supabase = supabase;
	event.locals.supabaseAdmin = supabaseAdmin;

	// Function to safely get session, user, and userCompanies
	event.locals.safeGetSession = async () => {
		const {
			data: { session },
			error: sessionError,
		} = await supabase.auth.getSession();

		// If there's a session error, return null values but don't redirect
		if (sessionError) {
			console.error("Error getting session:", sessionError);
			return { session: null, user: null, userCompanies: [] };
		}

		// If no session, return null values but don't redirect
		if (!session) {
			return { session: null, user: null, userCompanies: [] };
		}

		const {
			data: { user },
			error: userError,
		} = await supabase.auth.getUser();

		if (userError || !user) {
			console.error("Error getting user:", userError);
			return { session: null, user: null, userCompanies: [] };
		}

		// Fetch all companies where user is a member using a join query
		// This ensures we only get companies the user has access to
		const { data: companyMembers, error: memberError } = await supabase
			.from("company_members")
			.select(`
				companies:company_id (*)
			`)
			.eq("user_id", user.id);

		if (memberError) {
			console.error("Error fetching company memberships:", memberError);
			return { session, user, userCompanies: [] };
		}

		// Extract companies from the join result
		const userCompanies = companyMembers
			? companyMembers.map((member) => member.companies).filter(Boolean)
			: [];

		return { session, user, userCompanies };
	};

	event.locals.session = null;
	event.locals.user = null;
	event.locals.userCompanies = [];

	return resolve(event, {
		filterSerializedResponseHeaders(name) {
			return name === "content-range" || name === "x-supabase-api-version";
		},
	});
};

const authGuard: Handle = async ({ event, resolve }) => {
	// List of paths that should be accessible without authentication
	const publicPaths = [
		"/auth",
		"/session-expired",
		"/api/whatsapp",
		"/api/data-sources",
		"/", // Add root path as public
	];
	const isPublicPath = publicPaths.some((path) =>
		event.url.pathname.startsWith(path)
	);

	const { session, user, userCompanies } = await event.locals.safeGetSession();
	event.locals.session = session;
	event.locals.user = user;
	event.locals.userCompanies = userCompanies;

	// Check if the user is authenticated and the route contains a companyId
	const companyIdFromRoute = event.url.searchParams.get("companyId");
	if (session && companyIdFromRoute) {
		// Verify that user has access to this company by checking userCompanies
		const hasAccess = userCompanies.some((company) =>
			company.id === companyIdFromRoute
		);

		// Only update the active company if the user has access
		if (hasAccess) {
			updateCompanyIdFromRoute(companyIdFromRoute);
		} else {
			// If no access, redirect to companies page
			if (!isPublicPath && !event.url.pathname.startsWith("/company")) {
				return redirect(303, "/company");
			}
		}
	}

	// Only redirect to auth if not on a public path and no session
	if (!session && !isPublicPath) {
		const redirectTo = event.url.pathname + event.url.search;
		return redirect(303, `/auth?redirectTo=${encodeURIComponent(redirectTo)}`);
	}

	// Only redirect to company if on auth page with valid session
	if (session && event.url.pathname === "/auth") {
		// Check if there's a redirectTo param
		const redirectTo = event.url.searchParams.get("redirectTo");

		if (redirectTo) {
			return redirect(303, redirectTo);
		}

		// If no redirectTo, redirect to company with ID if available
		if (userCompanies.length > 0) {
			const companyId = userCompanies[0].id;
			return redirect(303, `/company/${companyId}`);
		}

		// Fallback to general company route if no specific company available
		return redirect(303, "/company");
	}

	return resolve(event);
};

export const handle: Handle = sequence(supabase, authGuard);
