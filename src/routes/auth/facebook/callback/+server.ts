import { json, redirect } from "@sveltejs/kit";
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./$types";
import { PRIVATE_FB_APP_SECRET } from "$env/static/private";
import { PUBLIC_FB_APP_ID } from "$env/static/public";
import { baseUrl } from "$lib/utils";

export const GET: RequestHandler = async (
  { url, cookies, locals: { supabase } },
) => {
  // Log all URL parameters for debugging
  console.log("[Facebook Callback] Raw URL:", url.toString());
  console.log(
    "[Facebook Callback] All URL parameters:",
    Object.fromEntries(url.searchParams.entries()),
  );

  const companyId = cookies.get("fb_redirect_company_id");
  const code = url.searchParams.get("code");
  const state = url.searchParams.get("state");
  const error = url.searchParams.get("error");
  const errorReason = url.searchParams.get("error_reason");
  const errorDescription = url.searchParams.get("error_description");
  const csrfToken = cookies.get("fb_csrf_token");

  // Log initial request details
  console.log("[Facebook Callback] Request details:", {
    hasCompanyId: !!companyId,
    companyId,
    hasCode: !!code,
    code: code?.substring(0, 10) + "...", // Log first 10 chars of code
    hasState: !!state,
    state,
    hasCsrfToken: !!csrfToken,
    csrfToken,
    error,
    errorReason,
    errorDescription,
    allCookies: cookies.getAll(),
    timestamp: new Date().toISOString(),
  });

  // Handle OAuth errors
  if (error) {
    console.error("[Facebook Callback] OAuth error:", {
      error,
      errorReason,
      timestamp: new Date().toISOString(),
    });
    throw redirect(
      303,
      `/company/${companyId}/manage-company?error=${encodeURIComponent(error)}`,
    );
  }

  if (!code || !companyId || !state || state !== csrfToken) {
    console.error("[Facebook Callback] Validation failed:", {
      hasCode: !!code,
      hasCompanyId: !!companyId,
      hasState: !!state,
      hasCsrfToken: !!csrfToken,
      stateMatch: state === csrfToken,
      timestamp: new Date().toISOString(),
    });
    throw redirect(
      303,
      `/company/${companyId}/manage-company?error=invalid_request`,
    );
  }

  try {
    console.log("[Facebook Callback] Initiating token exchange...");

    // Exchange code for token using Facebook Graph API
    const redirectUri = new URL("/auth/facebook/callback", baseUrl)
      .toString();
    console.log("[Facebook Callback] Using redirect URI:", redirectUri);

    const fbUrl = `https://graph.facebook.com/v21.0/oauth/access_token`;
    const params = new URLSearchParams({
      client_id: PUBLIC_FB_APP_ID,
      client_secret: PRIVATE_FB_APP_SECRET,
      redirect_uri: redirectUri,
      code,
    });

    console.log("[Facebook Callback] Making request to Facebook API...");
    console.log("[Facebook Callback] Request URL:", `${fbUrl}?${params}`);

    const response = await fetch(`${fbUrl}?${params}`, {
      method: "GET",
      headers: {
        "Accept": "application/json",
      },
    });

    const responseText = await response.text();
    console.log("[Facebook Callback] Raw response:", responseText);

    if (!response.ok) {
      let error;
      try {
        error = JSON.parse(responseText);
      } catch {
        error = { message: responseText };
      }

      console.error("[Facebook Callback] Token exchange failed:", {
        status: response.status,
        statusText: response.statusText,
        error,
        timestamp: new Date().toISOString(),
      });
      throw new Error(error.error?.message || "Failed to exchange token");
    }

    const tokenData = JSON.parse(responseText);
    console.log("[Facebook Callback] Token exchange successful:", {
      expiresIn: tokenData.expires_in,
      hasAccessToken: !!tokenData.access_token,
      timestamp: new Date().toISOString(),
    });

    // Store token in database
    console.log("[Facebook Callback] Updating Supabase record...");
    const { error: updateError } = await supabase
      .from("communication_channels")
      .update({
        access_token: tokenData.access_token,
        token_expires_at: new Date(Date.now() + tokenData.expires_in * 1000)
          .toISOString(),
      })
      .eq("company_id", companyId)
      .eq("type", "whatsapp");

    if (updateError) {
      console.error("[Facebook Callback] Supabase update failed:", updateError);
      throw updateError;
    }

    console.log("[Facebook Callback] Successfully stored token");

    // Clear cookies
    cookies.delete("fb_redirect_company_id", { path: "/" });
    cookies.delete("fb_csrf_token", { path: "/" });

    // Redirect with success
    console.log("[Facebook Callback] Flow completed successfully");
    throw redirect(303, `/company/${companyId}/manage-company?success=true`);
  } catch (error) {
    console.error("[Facebook Callback] Error:", {
      error,
      message: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
    });
    throw redirect(
      303,
      `/company/${companyId}/manage-company?error=${
        encodeURIComponent(
          error instanceof Error ? error.message : "Failed to exchange token",
        )
      }`,
    );
  }
};
