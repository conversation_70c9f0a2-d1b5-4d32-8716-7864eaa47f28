import type { EmailOtpType } from "@supabase/supabase-js";
import { redirect } from "@sveltejs/kit";

import type { RequestHandler } from "./$types";

export const GET: RequestHandler = async ({ url, locals: { supabase } }) => {
	console.log("[Auth Confirm] Starting confirmation handler", {
		url: url.toString(),
		searchParams: Object.fromEntries(url.searchParams.entries()),
		timestamp: new Date().toISOString(),
	});

	const token_hash = url.searchParams.get("token_hash");
	const type = url.searchParams.get("type") as EmailOtpType | null;
	const next = url.searchParams.get("next") ?? "/";

	console.log("[Auth Confirm] Extracted parameters", {
		hasTokenHash: !!token_hash,
		tokenHashLength: token_hash?.length,
		type,
		next,
		timestamp: new Date().toISOString(),
	});

	/**
	 * Clean up the redirect URL by deleting the Auth flow parameters.
	 *
	 * `next` is preserved for now, because it's needed in the error case.
	 */
	const redirectTo = new URL(url);
	redirectTo.pathname = next;

	console.log("[Auth Confirm] Redirect URL prepared", {
		originalUrl: url.toString(),
		redirectUrl: redirectTo.toString(),
		timestamp: new Date().toISOString(),
	});

	if (token_hash && type) {
		console.log("[Auth Confirm] Verifying OTP", {
			tokenHash: token_hash.substring(0, 8) + "...",
			type,
			timestamp: new Date().toISOString(),
		});

		const { data, error } = await supabase.auth.verifyOtp({ token_hash, type });

		if (error) {
			console.error("[Auth Confirm] OTP verification failed", {
				error,
				errorMessage: error.message,
				errorCode: error.code,
				type,
				timestamp: new Date().toISOString(),
			});
		} else {
			console.log("[Auth Confirm] OTP verification successful", {
				userId: data.user?.id,
				email: data.user?.email,
				timestamp: new Date().toISOString(),
			});
			throw redirect(303, redirectTo.toString());
		}
	} else {
		console.error("[Auth Confirm] Missing required parameters", {
			hasTokenHash: !!token_hash,
			hasType: !!type,
			type,
			searchParams: Object.fromEntries(url.searchParams.entries()),
			timestamp: new Date().toISOString(),
		});
	}

	redirectTo.pathname = "/auth/error";
	console.log("[Auth Confirm] Redirecting to error page", {
		redirectUrl: redirectTo.toString(),
		timestamp: new Date().toISOString(),
	});
	throw redirect(303, redirectTo.toString());
};
