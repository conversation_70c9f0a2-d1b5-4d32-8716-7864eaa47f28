<script lang="ts">
    import { page } from "$app/stores";
    import { Card } from "$lib/components/ui/card";
    import { Button } from "$lib/components/ui/button";
    import { goto } from "$app/navigation";
    import { Mail } from "lucide-svelte";

    // Get the email from the URL query params
    $: email = $page.url.searchParams.get("email") || "";
</script>

<div class="container max-w-md mx-auto mt-16 px-4">
    <Card.Root class="p-6">
        <Card.Header>
            <div class="flex flex-col items-center space-y-2">
                <div class="p-3 bg-primary/10 rounded-full">
                    <Mail class="w-8 h-8 text-primary" />
                </div>
                <Card.Title class="text-2xl text-center">Check your email</Card.Title>
                <Card.Description class="text-center">
                    We've sent a magic link to <span class="font-medium">{email}</span>
                </Card.Description>
            </div>
        </Card.Header>
        <Card.Content class="space-y-4 mt-4">
            <div class="text-sm text-muted-foreground space-y-2">
                <p>Click the link in the email to sign in. The link will expire in 24 hours.</p>
                <p>If you don't see the email, check your spam folder.</p>
            </div>
        </Card.Content>
        <Card.Footer class="flex flex-col space-y-2">
            <Button variant="outline" class="w-full" on:click={() => goto("/auth/login")}>
                Back to login
            </Button>
            <div class="text-center text-sm text-muted-foreground">
                <p>Need help? Contact <a href="mailto:<EMAIL>" class="text-primary hover:underline"><EMAIL></a></p>
            </div>
        </Card.Footer>
    </Card.Root>
</div> 