import { redirect } from "@sveltejs/kit";
import type { PageServerLoad } from "./$types";

export const load: PageServerLoad = async ({ url, locals: { getSession } }) => {
  const session = await getSession();

  // If user is already logged in, redirect to profile
  if (session) {
    throw redirect(303, "/profile");
  }

  // Ensure we have an email in the URL
  const email = url.searchParams.get("email");
  if (!email) {
    throw redirect(303, "/auth/login");
  }

  return {
    email,
  };
};
