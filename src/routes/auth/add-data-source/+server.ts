// src/routes/auth/add-data-source/+server.ts
import { error, redirect } from '@sveltejs/kit';
import type { RequestHandler } from '@sveltejs/kit';
import { PRIVATE_AZURE_CLIENT_SECRET } from '$env/static/private';
import { PUBLIC_AZURE_CLIENT_ID } from '$env/static/public';
import { exchangeCodeForTokens } from '$lib/utils';
import { exchangeRefreshToken } from '$lib/server/microsoft';
import { upsertSecret } from '$lib/server/vault';
import crypto from 'crypto';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { PRIVATE_SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

// PKCE helper functions
function base64URLEncode(str: Buffer): string {
	return str.toString('base64').replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
}

function generatePKCEValues() {
	// Generate code verifier
	const codeVerifier = base64URLEncode(crypto.randomBytes(32));

	// Generate code challenge
	const codeChallenge = base64URLEncode(crypto.createHash('sha256').update(codeVerifier).digest());

	return { codeVerifier, codeChallenge };
}

// Function to decode JWT and extract claims
function decodeJWT(token: string) {
	const base64Url = token.split('.')[1];
	const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
	const jsonPayload = Buffer.from(base64, 'base64').toString('utf8');
	return JSON.parse(jsonPayload);
}

export const GET: RequestHandler = async ({ url, cookies, locals: { supabase } }) => {
	const startTime = new Date().toISOString();
	const code = url.searchParams.get('code');
	const state = url.searchParams.get('state');
	const storedState = cookies.get('state');
	const codeVerifier = cookies.get('code_verifier');
	const existingRefreshToken = cookies.get('ms_refresh_token');

	console.log('[Add Data Source] Request received:', {
		hasCode: !!code,
		state,
		storedState,
		hasCodeVerifier: !!codeVerifier,
		hasRefreshToken: !!existingRefreshToken,
		timestamp: startTime,
		url: url.toString()
	});

	// Handle initial OAuth request (no code parameter)
	if (!code) {
		console.log('[Add Data Source] Initiating OAuth flow...', {
			timestamp: new Date().toISOString()
		});

		if (!state) {
			console.error('[Add Data Source] Missing state parameter');
			throw error(400, 'State parameter is required');
		}

		// Generate PKCE values
		const { codeVerifier, codeChallenge } = generatePKCEValues();

		console.log('[Add Data Source] Generated PKCE values', {
			hasCodeVerifier: !!codeVerifier,
			hasCodeChallenge: !!codeChallenge,
			timestamp: new Date().toISOString()
		});

		// Store state and code verifier in cookies
		cookies.set('state', state, {
			path: '/',
			httpOnly: true,
			secure: true,
			sameSite: 'lax',
			maxAge: 60 * 10 // 10 minutes
		});

		cookies.set('code_verifier', codeVerifier, {
			path: '/',
			httpOnly: true,
			secure: true,
			sameSite: 'lax',
			maxAge: 60 * 10 // 10 minutes
		});

		console.log('[Add Data Source] Set cookies:', {
			state: 'REDACTED',
			codeVerifier: 'REDACTED',
			timestamp: new Date().toISOString()
		});

		// Construct Microsoft OAuth URL
		const authUrl = new URL('https://login.microsoftonline.com/common/oauth2/v2.0/authorize');
		authUrl.searchParams.set('client_id', PUBLIC_AZURE_CLIENT_ID);
		authUrl.searchParams.set('response_type', 'code');
		authUrl.searchParams.set('redirect_uri', `${url.origin}/auth/add-data-source`);
		authUrl.searchParams.set('response_mode', 'query');
		authUrl.searchParams.set(
			'scope',
			'https://graph.microsoft.com/.default offline_access openid profile'
		);
		authUrl.searchParams.set('state', state);
		authUrl.searchParams.set('code_challenge', codeChallenge);
		authUrl.searchParams.set('code_challenge_method', 'S256');

		console.log('[Add Data Source] Redirecting to Microsoft login...', {
			redirectUrl: authUrl.toString(),
			timestamp: new Date().toISOString()
		});

		return redirect(303, authUrl.toString());
	}

	// If we have a refresh token but no state/code verifier, we can skip the code exchange
	if (existingRefreshToken && (!storedState || !codeVerifier)) {
		console.log('[Add Data Source] Using existing refresh token...', {
			hasStoredState: !!storedState,
			hasCodeVerifier: !!codeVerifier,
			timestamp: new Date().toISOString()
		});

		try {
			const newAccessToken = await exchangeRefreshToken(
				existingRefreshToken,
				'https://graph.microsoft.com/.default'
			);

			console.log('[Add Data Source] Successfully refreshed access token', {
				timestamp: new Date().toISOString()
			});

			// Set new access token in cookies
			cookies.set('ms_access_token', newAccessToken, {
				path: '/',
				httpOnly: true,
				secure: true,
				sameSite: 'lax',
				maxAge: 3600
			});

			// Try to get returnUrl from state parameter if available
			if (state) {
				try {
					const decodedState = Buffer.from(state, 'base64').toString();
					const stateData = JSON.parse(decodedState);
					console.log('[Add Data Source] Parsed state data:', {
						stateData,
						timestamp: new Date().toISOString()
					});
					if (stateData.returnUrl) {
						console.log('[Add Data Source] Redirecting to return URL:', stateData.returnUrl);
						return redirect(303, stateData.returnUrl);
					}
				} catch (stateError) {
					console.error('[Add Data Source] Error parsing state:', {
						error: stateError,
						state,
						timestamp: new Date().toISOString()
					});
				}
			}
			return redirect(303, '/');
		} catch (refreshError) {
			console.error('[Add Data Source] Failed to refresh token:', {
				error: refreshError,
				timestamp: new Date().toISOString()
			});
			throw error(401, 'Authentication failed - please try again');
		}
	}

	// Validate state and required parameters for code exchange flow
	if (!code || !state || !storedState || state !== storedState) {
		console.error('[Add Data Source] State validation failed:', {
			hasCode: !!code,
			state,
			storedState,
			stateMatch: state === storedState,
			timestamp: new Date().toISOString()
		});
		throw error(400, 'Invalid request parameters');
	}

	if (!codeVerifier) {
		console.error('[Add Data Source] Missing code verifier');
		throw error(400, 'Code verifier not found');
	}

	// Parse state data here so it's available throughout the function
	let stateData;
	try {
		const decodedState = Buffer.from(storedState, 'base64').toString();
		stateData = JSON.parse(decodedState);
		console.log('[Add Data Source] Successfully parsed state data:', {
			stateData,
			timestamp: new Date().toISOString()
		});
	} catch (stateError) {
		console.error('[Add Data Source] Error parsing state data:', {
			error: stateError,
			storedState,
			timestamp: new Date().toISOString()
		});
		throw error(400, 'Invalid state data');
	}

	// Exchange code for tokens
	let tokens;
	try {
		console.log('[Add Data Source] Exchanging code for tokens...', {
			timestamp: new Date().toISOString()
		});
		tokens = await exchangeCodeForTokens(code, codeVerifier, PRIVATE_AZURE_CLIENT_SECRET);
		console.log('[Add Data Source] Successfully obtained tokens', {
			hasAccessToken: !!tokens.access_token,
			hasRefreshToken: !!tokens.refresh_token,
			hasIdToken: !!tokens.id_token,
			timestamp: new Date().toISOString()
		});

		if (stateData.companyId && stateData.companyMemberId) {
			console.log('[Add Data Source] Processing tokens with company data...', {
				companyId: stateData.companyId,
				companyMemberId: stateData.companyMemberId,
				timestamp: new Date().toISOString()
			});

			try {
				// Extract user email from ID token
				if (!tokens.id_token) {
					console.error('[Add Data Source] No ID token received');
					throw error(400, 'No ID token received from Microsoft');
				}

				const claims = decodeJWT(tokens.id_token);
				const userEmail = claims.preferred_username || claims.email || claims.upn;

				if (!userEmail) {
					console.error('[Add Data Source] No email found in token claims:', claims);
					throw error(400, 'Could not determine user email from Microsoft login');
				}

				console.log('[Add Data Source] Extracted user email:', {
					email: userEmail,
					timestamp: new Date().toISOString()
				});

				// Store refresh token in vault
				console.log('[Add Data Source] Storing refresh token in vault...');
				const secretId = await upsertSecret(null, tokens.refresh_token);
				console.log('[Add Data Source] Successfully stored refresh token', {
					secretId,
					timestamp: new Date().toISOString()
				});

				// Create or update user platform profile
				console.log('[Add Data Source] Creating/updating platform profile...');
				const { error: profileError } = await supabase.from('user_platform_profiles').upsert(
					{
						company_member_id: stateData.companyMemberId,
						platform_type: 'sharepoint',
						platform_user_id: userEmail,
						is_primary: true,
						encrypted_refresh_token: secretId,
						metadata: {
							last_token_refresh: new Date().toISOString(),
							scope: 'https://graph.microsoft.com/.default',
							claims: {
								name: claims.name,
								email: userEmail,
								oid: claims.oid // Object ID in Azure AD
							}
						}
					},
					{
						onConflict: 'company_member_id,platform_type'
					}
				);

				if (profileError) {
					console.error('[Add Data Source] Failed to update user platform profile:', {
						error: profileError,
						timestamp: new Date().toISOString()
					});
					throw error(500, 'Failed to store platform profile');
				}

				console.log('[Add Data Source] Successfully stored platform profile', {
					email: userEmail,
					timestamp: new Date().toISOString()
				});
			} catch (storageError) {
				console.error('[Add Data Source] Failed to store tokens:', {
					error: storageError,
					timestamp: new Date().toISOString()
				});
				throw error(500, 'Failed to store authentication data');
			}
		} else {
			console.error('[Add Data Source] Missing required state data:', {
				stateData,
				timestamp: new Date().toISOString()
			});
			throw error(400, 'Missing required company or member information');
		}
	} catch (exchangeError) {
		console.error('[Add Data Source] Code exchange failed:', {
			error: exchangeError,
			timestamp: new Date().toISOString()
		});

		if (!existingRefreshToken) {
			throw error(401, 'Authentication failed - please try again');
		}

		try {
			console.log('[Add Data Source] Attempting to use existing refresh token...');
			const newAccessToken = await exchangeRefreshToken(
				existingRefreshToken,
				'https://graph.microsoft.com/.default'
			);

			tokens = {
				access_token: newAccessToken,
				refresh_token: existingRefreshToken,
				expires_in: 3600
			};

			console.log('[Add Data Source] Successfully used existing refresh token', {
				timestamp: new Date().toISOString()
			});
		} catch (refreshError) {
			console.error('[Add Data Source] Refresh token exchange also failed:', {
				error: refreshError,
				timestamp: new Date().toISOString()
			});
			throw error(401, 'Authentication failed - please try again');
		}
	}

	console.log('[Add Data Source] Setting tokens in cookies...');
	// Set tokens in cookies with minimal paths and optimized storage
	cookies.set('ms_access_token', tokens.access_token, {
		path: '/',
		httpOnly: true,
		secure: true,
		sameSite: 'lax',
		maxAge: tokens.expires_in
	});

	cookies.set('ms_refresh_token', tokens.refresh_token, {
		path: '/',
		httpOnly: true,
		secure: true,
		sameSite: 'lax',
		maxAge: 60 * 60 * 24 * 30 // 30 days
	});

	// Only store id_token if absolutely necessary
	if (tokens.id_token) {
		cookies.set('ms_id_token', tokens.id_token, {
			path: '/',
			httpOnly: true,
			secure: true,
			sameSite: 'lax',
			maxAge: tokens.expires_in
		});
	}

	console.log('[Add Data Source] Cleaning up temporary cookies...');
	// Clean up temporary cookies
	cookies.delete('state', { path: '/' });
	cookies.delete('code_verifier', { path: '/' });

	// Use the parsed stateData for redirection
	if (stateData.returnUrl) {
		console.log('[Add Data Source] Redirecting to return URL:', stateData.returnUrl);
		return redirect(303, stateData.returnUrl);
	}

	// Redirect to site selection page
	console.log('[Add Data Source] Redirecting to new data source page');
	return redirect(303, `/company/${stateData.companyId}/vault/new-data-source`);
};
