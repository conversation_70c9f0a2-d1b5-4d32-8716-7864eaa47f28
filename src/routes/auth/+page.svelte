<script lang="ts">
	import { <PERSON><PERSON> } from "$lib/components/ui/button";
	import * as Card from "$lib/components/ui/card";
	import { Label } from "$lib/components/ui/label";
	import { Input } from "$lib/components/ui/input";
	import MicrosoftLogo from "$lib/components/icons/MicrosoftLogo.svelte";
	import Google<PERSON>ogo from "$lib/components/icons/GoogleLogo.svelte";
	import { formSchema, type FormSchema } from './schema';
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';

	export let data;

	const { form, errors, enhance, delayed } = superForm(data.form, {
		validators: zodClient(formSchema),
		onError: ({ result }) => {
			errorMessage = result.error.message || 'An error occurred while sending the magic link.';
		}
	});

	let errorMessage = '';
	$: if ($errors.email) {
		errorMessage = $errors.email[0];
	}
</script>

<div class="container flex flex-col items-center justify-center flex-1 h-full">
	<Card.Root class="w-full max-w-md px-4 py-8 mx-auto border shadow-md bg-background dark:bg-muted">
		<Card.Header class="space-y-2">
			<Card.Title class="text-2xl font-semibold tracking-tight">Welcome back</Card.Title>
			<Card.Description class="text-muted-foreground">
				Sign in to your account
			</Card.Description>
		</Card.Header>
		<Card.Content class="space-y-4">
			<div class="grid gap-4">
				<form method="POST" action="?/microsoft">
					<Button 
						type="submit"
						variant="outline" 
						class="w-full hover:bg-primary/50"
					>
						<MicrosoftLogo class_="w-4 h-4 mr-2" />
						Sign in with Azure
					</Button>
				</form>
				<form method="POST" action="?/google">
					<Button 
						type="submit"
						variant="outline" 
						class="w-full hover:bg-primary/50"
					>
						<GoogleLogo class_="w-4 h-4 mr-2" />
						Sign in with Google
					</Button>
				</form>
			</div>
			
			<div class="relative flex items-center py-4">
				<span class="flex-grow border-t dark:border-primary" />
				<div class="relative flex justify-center text-xs uppercase">
					<span class="px-2 bg-background dark:bg-muted text-muted-foreground">Or continue with email</span>
				</div>
				<span class="flex-grow border-t dark:border-primary" />
			</div>
			
			<form method="POST" action="?/email" use:enhance class="space-y-4">
				<div class="space-y-2">
					<Label for="email">Email address</Label>
					<Input 
						id="email"
						name="email"
						type="email"
						placeholder="<EMAIL>"
						bind:value={$form.email}
						required
						class="w-full"
						disabled={$delayed}
					/>
					{#if errorMessage}
						<p class="text-sm text-destructive">{errorMessage}</p>
					{/if}
				</div>
				<Button type="submit" class="w-full hover:bg-primary/90" disabled={$delayed}>
					{#if $delayed}
						Sending...
					{:else}
						Send Magic Link
					{/if}
				</Button>
			</form>
		</Card.Content>
		<Card.Footer>
			<p class="text-sm text-center text-muted-foreground">
				{#if $delayed}
					Sending magic link...
				{:else}
					We'll send you a magic link for a password-free sign in
				{/if}
			</p>
		</Card.Footer>
	</Card.Root>
</div>
