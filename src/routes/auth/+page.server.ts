// src/routes/auth/+page.server.ts

import { redirect } from "@sveltejs/kit";
import type { Actions, PageServerLoad } from "./$types.js";
import { fail } from "@sveltejs/kit";
import { superValidate } from "sveltekit-superforms";
import { formSchema } from "./schema";
import { zod } from "sveltekit-superforms/adapters";
import { baseUrl } from "$lib/utils.js";

export const load: PageServerLoad = async ({ locals: { session } }) => {
	// Redirect if user is already authenticated
	if (session) {
		return redirect(303, "/company");
	}

	const form = await superValidate(zod(formSchema));
	return { form };
};

export const actions: Actions = {
	email: async ({ request, locals: { supabase } }) => {
		const formData = await request.formData();
		const email = formData.get("email") as string;

		if (!email) {
			console.error("[Auth] Email is missing from form data");
			return fail(400, {
				form: {
					email,
					errorMessage: "Email is required.",
				},
			});
		}

		console.log("[Auth] Initiating email sign-in", {
			email,
			timestamp: new Date().toISOString(),
		});

		const { error } = await supabase.auth.signInWithOtp({
			email,
			options: {
				emailRedirectTo: `${baseUrl}/auth/callback`,
				shouldCreateUser: true,
			},
		});

		if (error) {
			console.error("[Auth] Error during email sign-in", {
				error,
				errorMessage: error.message,
				errorCode: error.code,
				timestamp: new Date().toISOString(),
			});
			return fail(400, {
				form: {
					email,
					errorMessage: error.message,
				},
			});
		}

		console.log("[Auth] Magic link sent successfully", {
			email,
			timestamp: new Date().toISOString(),
		});

		return redirect(303, "/auth/check-email");
	},

	microsoft: async ({ locals: { supabase }, url }) => {
		console.log("[Auth] Initiating Microsoft sign-in", {
			timestamp: new Date().toISOString(),
		});

		const { data, error } = await supabase.auth.signInWithOAuth({
			provider: "azure",
			options: {
				redirectTo: `${baseUrl}/auth/callback`,
				scopes: "email profile offline_access",
			},
		});

		if (error) {
			console.error("[Auth] Error initiating Microsoft sign-in", {
				error,
				errorMessage: error.message,
				errorCode: error.code,
				timestamp: new Date().toISOString(),
			});
			return redirect(303, "/auth/error");
		}

		if (!data.url) {
			console.error("[Auth] No OAuth URL returned from Supabase");
			return redirect(303, "/auth/error");
		}

		console.log("[Auth] Redirecting to Microsoft OAuth", {
			url: data.url,
			timestamp: new Date().toISOString(),
		});

		return redirect(303, data.url);
	},

	google: async ({ locals: { supabase }, url }) => {
		console.log("[Auth] Initiating Google sign-in", {
			timestamp: new Date().toISOString(),
		});

		const { data, error } = await supabase.auth.signInWithOAuth({
			provider: "google",
			options: {
				redirectTo: `${baseUrl}/auth/callback`,
				queryParams: {
					access_type: "offline",
					prompt: "consent",
				},
			},
		});

		if (error) {
			console.error("[Auth] Error initiating Google sign-in", {
				error,
				errorMessage: error.message,
				errorCode: error.code,
				timestamp: new Date().toISOString(),
			});
			return redirect(303, "/auth/error");
		}

		if (!data.url) {
			console.error("[Auth] No OAuth URL returned from Supabase");
			return redirect(303, "/auth/error");
		}

		console.log("[Auth] Redirecting to Google OAuth", {
			url: data.url,
			timestamp: new Date().toISOString(),
		});

		return redirect(303, data.url);
	},
};
