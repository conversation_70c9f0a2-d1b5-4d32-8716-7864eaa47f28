import { redirect } from "@sveltejs/kit";
import type { Request<PERSON>and<PERSON> } from "./$types";

export const GET: RequestHandler = async ({ url, locals: { supabase } }) => {
	console.log("[Auth Callback] Starting callback handler", {
		url: url.toString(),
		searchParams: Object.fromEntries(url.searchParams.entries()),
		timestamp: new Date().toISOString(),
	});

	const code = url.searchParams.get("code");
	const next = url.searchParams.get("next") ?? "/company";
	const error = url.searchParams.get("error");
	const error_description = url.searchParams.get("error_description");

	// Handle error cases first
	if (error || error_description) {
		console.error("[Auth Callback] Auth error from provider", {
			error,
			error_description,
			timestamp: new Date().toISOString(),
		});
		return redirect(303, "/auth/error");
	}

	// Handle PKCE flow
	if (code) {
		try {
			console.log("[Auth Callback] Processing PKCE flow", {
				codePrefix: code.substring(0, 8) + "...",
				timestamp: new Date().toISOString(),
			});

			const { error: exchangeError } = await supabase.auth
				.exchangeCodeForSession(code);

			if (exchangeError) {
				console.error("[Auth Callback] Error exchanging code for session", {
					error: exchangeError,
					errorMessage: exchangeError.message,
					errorCode: exchangeError.code,
					timestamp: new Date().toISOString(),
				});
				return redirect(303, "/auth/error");
			}
		} catch (error) {
			console.error("[Auth Callback] Error in PKCE flow", {
				error,
				timestamp: new Date().toISOString(),
			});
			return redirect(303, "/auth/error");
		}
	}

	let session;

	// Verify session for both PKCE and OAuth flows
	try {
		const { data, error: sessionError } = await supabase.auth
			.getSession();

		if (sessionError) {
			console.error("[Auth Callback] Error getting session", {
				error: sessionError,
				timestamp: new Date().toISOString(),
			});
			return redirect(303, "/auth/error");
		}

		if (!data.session) {
			console.error("[Auth Callback] No session found after authentication", {
				timestamp: new Date().toISOString(),
			});
			return redirect(303, "/auth/error");
		}

		session = data.session;
	} catch (error) {
		console.error("[Auth Callback] Error verifying session", {
			error,
			timestamp: new Date().toISOString(),
		});
		return redirect(303, "/auth/error");
	}

	// Log success and redirect (outside of try-catch)
	console.log("[Auth Callback] Authentication successful", {
		userId: session.user.id,
		provider: session.user.app_metadata.provider,
		next,
		timestamp: new Date().toISOString(),
	});

	return redirect(303, next);
};
