import { error, redirect } from '@sveltejs/kit';
import type { RequestHandler } from '@sveltejs/kit';
import { PRIVATE_AZURE_CLIENT_SECRET } from '$env/static/private';
import { exchangeCodeForTokens } from '$lib/utils';
import { exchangeRefreshToken, sharepointScopes } from '$lib/server/microsoft';
import { upsertSecret } from '$lib/server/vault';

export const GET: RequestHandler = async ({
	url,
	cookies,
	locals: { supabase, safeGetSession }
}) => {
	const code = url.searchParams.get('code');
	const state = url.searchParams.get('state');
	const storedState = cookies.get('state');
	const codeVerifier = cookies.get('code_verifier');

	console.log('[SharePoint Callback] OAuth callback received:', {
		hasCode: !!code,
		state,
		storedState,
		hasCodeVerifier: !!codeVerifier,
		timestamp: new Date().toISOString()
	});

	if (!code || !codeVerifier) {
		throw error(400, 'Missing required OAuth parameters');
	}

	try {
		// Parse state data
		if (!state || !storedState || state !== storedState) {
			console.error('[SharePoint Callback] State validation failed:', {
				state,
				storedState,
				match: state === storedState,
				timestamp: new Date().toISOString()
			});
			throw error(400, 'Invalid state parameter');
		}

		const decodedState = Buffer.from(state, 'base64').toString();
		const stateData = JSON.parse(decodedState);
		const { companyId, companyMemberId, returnUrl } = stateData;

		console.log('[SharePoint Callback] Parsed state data:', {
			companyId,
			companyMemberId,
			returnUrl,
			timestamp: new Date().toISOString()
		});

		if (!companyMemberId) {
			throw error(400, 'Missing company member ID in state');
		}

		// Exchange code for tokens
		console.log('[SharePoint Callback] Exchanging code for tokens...');
		const tokens = await exchangeCodeForTokens(
			code,
			codeVerifier,
			PRIVATE_AZURE_CLIENT_SECRET,
			companyId
		);

		console.log('[SharePoint Callback] Successfully exchanged code for tokens', {
			hasAccessToken: !!tokens.access_token,
			hasRefreshToken: !!tokens.refresh_token,
			hasIdToken: !!tokens.id_token,
			timestamp: new Date().toISOString()
		});

		// Get current user
		const { session } = await safeGetSession();
		if (!session) {
			throw error(401, 'User not authenticated');
		}

		// Store tokens in vault
		console.log('[SharePoint Callback] Storing tokens in vault...');
		const [refreshTokenId, idTokenId] = await Promise.all([
			upsertSecret(null, tokens.refresh_token),
			upsertSecret(null, tokens.id_token)
		]);

		// If we have a company ID, this is a company-wide connection
		if (companyId) {
			console.log('[SharePoint Callback] Creating/updating data source...');
			// Create or update data source
			const { data: dataSource, error: dataSourceError } = await supabase
				.from('data_sources')
				.upsert(
					{
						company_id: companyId,
						platform_type: 'sharepoint',
						tenant_id: tokens.tid,
						secret_oauth: refreshTokenId,
						id_token: idTokenId,
						scope: tokens.scope,
						updated_at: new Date().toISOString()
					},
					{
						onConflict: 'company_id,platform_type'
					}
				)
				.select()
				.single();

			if (dataSourceError) {
				console.error('[SharePoint Callback] Failed to create data source:', dataSourceError);
				throw error(500, `Failed to create data source: ${dataSourceError.message}`);
			}

			console.log('[SharePoint Callback] Data source created/updated successfully');
		}

		// Create user platform profile
		console.log('[SharePoint Callback] Creating/updating user platform profile...');
		const { error: profileError } = await supabase.from('user_platform_profiles').upsert(
			{
				company_member_id: companyMemberId,
				platform_type: 'sharepoint',
				platform_user_id: tokens.oid,
				is_primary: true,
				metadata: {
					tenant_id: tokens.tid,
					scope: tokens.scope
				}
			},
			{
				onConflict: 'company_member_id,platform_type'
			}
		);

		if (profileError) {
			console.error('[SharePoint Callback] Failed to create platform profile:', profileError);
			throw error(500, `Failed to create platform profile: ${profileError.message}`);
		}

		console.log('[SharePoint Callback] Platform profile created/updated successfully');

		// Clean up cookies
		cookies.delete('state', { path: '/' });
		cookies.delete('code_verifier', { path: '/' });

		// Redirect back to profile page or specified return URL
		const redirectUrl = returnUrl || '/profile';
		console.log('[SharePoint Callback] Redirecting to:', {
			url: redirectUrl,
			timestamp: new Date().toISOString()
		});
		return redirect(303, redirectUrl);
	} catch (err) {
		console.error('[SharePoint Callback] Error:', {
			error: err instanceof Error ? err.message : 'Unknown error',
			stack: err instanceof Error ? err.stack : undefined,
			timestamp: new Date().toISOString()
		});
		throw error(
			err instanceof Error ? 500 : (err as any).status || 500,
			err instanceof Error ? err.message : 'Internal server error'
		);
	}
};
