import { redirect } from "@sveltejs/kit";
import type { RequestHand<PERSON> } from "./$types";
import { generateCodeChallenge, generateCodeVerifier } from "$lib/utils";
import { baseUrl } from "$lib/utils";
export const GET: RequestHandler = async (
	{ url, cookies, locals: { supabase } },
) => {
	//console.log('Received OAuth GET request with URL:', url);

	const provider = url.searchParams.get("provider");
	//console.log('OAuth provider requested:', provider);

	if (!provider) {
		console.error("No provider specified in the OAuth request.");
		return new Response("No provider specified", { status: 400 });
	}

	let scopes: string;
	if (provider === "google") {
		scopes =
			"https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile";
	} else if (provider === "azure") {
		scopes = ["offline_access", "profile", "openid", "email", "User.Read.All"]
			.join(" ");
	} else {
		console.error(`Unsupported OAuth provider: ${provider}`);
		return new Response("Unsupported provider", { status: 400 });
	}

	const codeVerifier = generateCodeVerifier();
	const codeChallenge = await generateCodeChallenge(codeVerifier);

	//console.log('Generated code verifier:', codeVerifier);
	//console.log('Generated code challenge:', codeChallenge);

	cookies.set("code_verifier", codeVerifier, {
		httpOnly: true,
		path: "/",
		maxAge: 60 * 15,
	});
	//console.log('Set code_verifier cookie:', codeVerifier);

	const { data, error } = await supabase.auth.signInWithOAuth({
		provider: provider as "google" | "azure",
		options: {
			scopes: scopes,
			redirectTo: `${baseUrl}/auth/callback`,
			queryParams: {
				code_challenge: codeChallenge,
				code_challenge_method: "S256",
				access_type: "offline",
			},
		},
	});

	if (error) {
		console.error("OAuth initialization error:", error);
		return new Response(error.message, { status: 400 });
	}

	if (data?.url) {
		//console.log('Redirecting to OAuth provider URL:', data.url);
		throw redirect(303, data.url);
	} else {
		console.error("No URL returned from supabase.auth.signInWithOAuth");
		return new Response("No URL returned from OAuth initialization", {
			status: 400,
		});
	}
};
