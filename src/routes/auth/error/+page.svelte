<script lang="ts">
	import { <PERSON><PERSON> } from "$lib/components/ui/button";
	import { goto } from "$app/navigation";
</script>

<div class="flex min-h-[calc(100vh-4rem)] items-center justify-center">
	<div class="mx-auto max-w-md text-center">
		<h1 class="mb-4 text-2xl font-bold">Authentication Error</h1>
		<p class="mb-6 text-muted-foreground">
			We encountered an error while trying to authenticate you. This could be because the link has expired or is invalid.
		</p>
		<div class="space-y-4">
			<Button
				variant="default"
				class="w-full"
				on:click={() => goto("/auth")}
			>
				Try Again
			</Button>
			<Button
				variant="outline"
				class="w-full"
				on:click={() => goto("/")}
			>
				Return Home
			</Button>
		</div>
	</div>
</div>
