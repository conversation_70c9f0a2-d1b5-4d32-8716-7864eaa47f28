<script lang="ts">
	import { goto } from '$app/navigation';
	import CompanyCard from '$lib/components/CompanyCard.svelte';
	import Button from '$lib/components/ui/button/button.svelte';
	export let data;
	let { user } = data;
	
	let userCompanies = data.userCompanies;

	$: ({ userCompanies } = data);

	$: sortedCompanies = userCompanies.sort((a, b) => 
		new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
	);

	function handleCompanyDelete(event: CustomEvent<{ companyId: string }>) {
		const { companyId } = event.detail;
		userCompanies = userCompanies.filter(company => company.id !== companyId);
	}
</script>

<div class="container flex flex-col mx-auto">
	<div class="flex items-center justify-between mb-6">
		<h1 class="text-2xl font-bold">Your Companies</h1>
		<Button on:click={() => goto('/company/setup-company')}>
			Add Company
		</Button>
	</div>
	<div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
		{#each sortedCompanies as company}
			<CompanyCard 
				{company}
				on:companyDelete={handleCompanyDelete}
			/>
		{/each}
	</div>

</div>