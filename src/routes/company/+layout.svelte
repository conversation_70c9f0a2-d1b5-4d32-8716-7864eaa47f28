<script lang="ts">
	import '../../app.css';
	import { invalidate } from '$app/navigation';
	import { onMount } from 'svelte';
	import { companyStore, setCompanyId } from '$lib/stores/companyStore';
	import { page } from '$app/stores';
	
	export let data;
	
	let {companyId, user, createCompany, userCompanies } = data;
	$: ({ session, supabase, user, userCompanies, profile } = $page.data);
	
	onMount(() => {
		const { data: { subscription } } = data.supabase.auth.onAuthStateChange((event, newSession) => {
			if (newSession?.expires_at !== data.session?.expires_at) {
				// Use setTimeout to avoid potential deadlocks with other Supabase calls
				setTimeout(() => {
					invalidate('supabase:auth');
				}, 0);
			}
		});

		return () => {
			subscription.unsubscribe();
		};
	});

	$: isMainCompanyPage = $page.url.pathname === `/company/${$page.params.companyId}`;
</script>

<div class="flex flex-col flex-1 h-full">
	<main class="container flex flex-col flex-1 h-full max-w-4xl p-8 mx-auto {isMainCompanyPage ? 'items-center justify-center' : 'justify-start'}">
		<div class="h-full">
			<slot />				
		</div>
	</main>
</div>

