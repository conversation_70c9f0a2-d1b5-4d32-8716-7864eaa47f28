import { deleteAllCookies } from "$lib/utils.js";
import { redirect } from "@sveltejs/kit";
import { <PERSON><PERSON> } from "lucide-svelte";

export const load = async (
	{ locals: { supabase, user, userCompanies }, depends, cookies },
) => {
	if (!user) {
		cookies.getAll().forEach((cookie) => {
			cookies.delete(cookie.name, { path: "/" });
		});
		throw redirect(303, "auth");
	}
	// Fetch companies where the user is either an admin or a member
	depends("supabase:db:companies");
	//console.log('Fetched companies:', userCompanies.length);
	if (!userCompanies || userCompanies.length === 0) {
		throw redirect(303, "company/setup-company"); // Redirect if no companies found
	}

	return {
		user,
		userCompanies,
	};
};
