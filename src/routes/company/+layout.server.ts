// src/routes/private/+layout.server.ts
import { error, redirect } from "@sveltejs/kit";

export const load = async ({ locals }) => {
	const { user, supabase, userCompanies } = locals;
	let createCompany = true;

	if (!user) {
		throw redirect(303, "/auth");
	}

	//console.log("userCompanies:", userCompanies);

	return {
		user,
		createCompany: !userCompanies || userCompanies.length === 0,
		userCompanies: userCompanies || [],
	};
};
