import {
	create<PERSON>rowser<PERSON><PERSON>,
	createServer<PERSON><PERSON>,
	isBrowser,
} from "@supabase/ssr";
import {
	PUBLIC_SUPABASE_ANON_KEY,
	PUBLIC_SUPABASE_URL,
} from "$env/static/public";

export const load = async ({ url, data, depends, fetch }) => {
	/**
	 * Declare a dependency so the layout can be invalidated, for example, on
	 * session refresh.
	 */
	depends("supabase:auth");
	depends("supabase:db:companies");

	const supabase = isBrowser()
		? createBrowserClient(PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY, {
			global: {
				fetch,
			},
		})
		: createServerClient(PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY, {
			global: {
				fetch,
			},
			cookies: {
				getAll() {
					return data.cookies;
				},
			},
		});

	/**
	 * It's fine to use `getSession` here, because on the client, `getSession` is
	 * safe, and on the server, it reads `session` from the `LayoutData`, which
	 * safely checked the session using `safeGetSession`.
	 */
	const {
		data: { session },
	} = await supabase.auth.getSession();

	const {
		data: { user },
	} = await supabase.auth.getUser();

	// Retrieve companyId from cookies
	const companyId = url.searchParams.get("company") ||
		(typeof document !== "undefined"
			? document.cookie.match(new RegExp("(?:^|; )companyId=([^;]*)"))?.[1]
			: null);

	const { data: profile } = await supabase.from("base_users").select("*").eq(
		"id",
		user?.id,
	).single();

	// Pass through the server data
	return {
		companyId: companyId ? decodeURIComponent(companyId) : null,
		session,
		supabase,
		user,
		profile,
		// Pass through the server-loaded data
		userCompanies: data.userCompanies || [],
		createCompany: data.createCompany,
	};
};
