<!-- src/routes/company/setup-company/+page.svelte -->
<script lang="ts">
	import { enhance } from '$app/forms';
	import Button from '$lib/components/ui/button/button.svelte';
	import Input from '$lib/components/ui/input/input.svelte';
	import Label from '$lib/components/ui/label/label.svelte';
	import { Loader2 } from 'lucide-svelte';
	import type { ActionData } from './$types';

	export let form: ActionData;

	let companyName = '';
	let errorMessage = '';
	let isSubmitting = false;

	// Update messages based on form results
	$: if (form?.error) {
		errorMessage = form.error;
	}
</script>

<form
	method="post"
	class="space-y-4"
	use:enhance={() => {
		// Before form submission
		isSubmitting = true;
		errorMessage = '';

		return async ({ result, update }) => {
			// After form submission
			if (result.type === 'failure') {
				// Handle the error - cast to string to fix type issue
				errorMessage = String(result.data?.error || 'An error occurred');
			}

			// Always update the form
			await update();

			// Reset submission state
			isSubmitting = false;
		};
	}}
>
	<div>
		<Label for="companyName" class="block text-sm font-medium">Company Name</Label>
		<Input
			type="text"
			id="companyName"
			name="companyName"
			bind:value={companyName}
			class="block w-full p-2 mt-1 border border-gray-300 rounded-md"
			placeholder="Enter company name"
			required
		/>
	</div>

	{#if errorMessage}
		<p class="text-red-500">{errorMessage}</p>
	{/if}

	<Button type="submit" disabled={isSubmitting}>
		{#if isSubmitting}
			<Loader2 class="w-4 h-4 mr-2 animate-spin" />
			Creating...
		{:else}
			Create Company
		{/if}
	</Button>
</form>
