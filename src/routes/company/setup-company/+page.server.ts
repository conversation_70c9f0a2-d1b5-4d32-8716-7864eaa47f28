// src/routes/company/setup-company/+page.server.ts
import type { Actions, PageServerLoad } from "./$types";
import { redirect } from "@sveltejs/kit";

export const load: PageServerLoad = async ({ locals }) => {
	return {
		user: locals.user,
	};
};

export const actions: Actions = {
	default: async ({ request, locals, cookies }) => {
		const { supabase, user } = locals;
		const formData = await request.formData();
		const companyName = formData.get("companyName") as string;
		if (!companyName) {
			return { error: "Company name is required." };
		}

		// Create the company - the database trigger will automatically add the user as admin
		const { data, error } = await supabase
			.from("companies")
			.insert([{ name: companyName, created_by: user.id }])
			.select("id")
			.single();

		if (error) {
			console.error("Error creating company:", error);
			return { error: error.message };
		}

		// Set the companyId in cookies
		cookies.set("companyId", data.id, { path: "/" });

		// Redirect to the new company's page
		return redirect(303, `/company/${data.id}/vault`);
	},
};
