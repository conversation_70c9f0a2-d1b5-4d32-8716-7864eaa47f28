<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import { toast } from 'svelte-sonner';
	import type { PageData } from './$types';
	import { onMount } from 'svelte';
	import {
		Accordion,
		AccordionContent,
		AccordionItem,
		AccordionTrigger
	} from '$lib/components/ui/accordion';
	import CompanyProducts from '$lib/components/CompanyProducts.svelte';

	export let data: PageData;
	let { companyId, products, companyProducts, isAdmin } = data;

	// If user is not an admin, show a message
	$: if (!isAdmin) {
		toast.error('You must be a company admin to manage products');
	}
</script>

<div class="container p-4 mx-auto space-y-6">
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold">Available Integrations</h1>
			<p class="text-muted-foreground mt-1">
				Enable or disable integrations for your company. Toggle the switches to activate the
				products you need.
			</p>
		</div>
		<Button href="/company/{companyId}/manage-company" variant="outline">Back to Management</Button>
	</div>

	{#if !products || products.length === 0}
		<Card>
			<CardContent class="flex flex-col items-center justify-center p-6 space-y-4">
				<div class="p-4 rounded-full bg-muted">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						width="24"
						height="24"
						viewBox="0 0 24 24"
						fill="none"
						stroke="currentColor"
						stroke-width="2"
						stroke-linecap="round"
						stroke-linejoin="round"
						class="text-muted-foreground"
					>
						<path d="M9.88 9.88a3 3 0 1 0 4.24 4.24" />
						<path
							d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68"
						/>
						<path d="M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61" />
						<line x1="2" x2="22" y1="2" y2="22" />
					</svg>
				</div>
				<h3 class="text-lg font-medium">No Products Available</h3>
				<p class="text-sm text-center text-muted-foreground">
					There are no product options available at this time. Please contact support for
					assistance.
				</p>
			</CardContent>
		</Card>
	{:else}
		<CompanyProducts
			companyId={companyId || ''}
			products={products || []}
			companyProducts={companyProducts || []}
			useAccordion={true}
		/>
	{/if}
</div>
