import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { verifyUserRoleForUrl } from '$lib/utils/roleBasedAccess';

export const POST: RequestHandler = async ({ request, locals, params }) => {
	const { supabase, user } = locals;
	const { companyId } = params;

	// Verify user has access
	const hasAccess = await verifyUserRoleForUrl(user, companyId, 'manage-company', supabase);

	if (!hasAccess) {
		return json(
			{
				success: false,
				message: 'You do not have permission to manage company products'
			},
			{ status: 403 }
		);
	}

	try {
		const { productId, enabled } = await request.json();

		if (!productId) {
			return json(
				{
					success: false,
					message: 'Product ID is required'
				},
				{ status: 400 }
			);
		}

		// Check if product is marked as "coming soon" - if so, prevent enabling
		if (enabled) {
			const { data: product, error: productError } = await supabase
				.from('products')
				.select('is_coming_soon')
				.eq('id', productId)
				.single();

			if (productError) {
				console.error('Error fetching product info:', productError);
				return json(
					{
						success: false,
						message: 'Error checking product status'
					},
					{ status: 500 }
				);
			}

			if (product.is_coming_soon) {
				return json(
					{
						success: false,
						message: 'This product is coming soon and cannot be enabled yet'
					},
					{ status: 400 }
				);
			}
		}

		// First check if the record already exists
		const { data: existingRecord, error: fetchError } = await supabase
			.from('company_products')
			.select('id, is_enabled')
			.eq('company_id', companyId)
			.eq('product_id', productId)
			.single();

		if (fetchError && fetchError.code !== 'PGRST116') {
			// Not found is ok
			console.error('Error fetching company product:', fetchError);
			return json(
				{
					success: false,
					message: 'Error checking product status'
				},
				{ status: 500 }
			);
		}

		let result;

		if (existingRecord) {
			// Record exists, update it
			const { data, error } = await supabase
				.from('company_products')
				.update({ is_enabled: enabled })
				.eq('id', existingRecord.id)
				.select()
				.single();

			result = { data, error };
		} else {
			// Record doesn't exist, insert new one
			const { data, error } = await supabase
				.from('company_products')
				.insert({
					company_id: companyId,
					product_id: productId,
					is_enabled: enabled
				})
				.select()
				.single();

			result = { data, error };
		}

		if (result.error) {
			console.error('Error updating company product:', result.error);
			return json(
				{
					success: false,
					message: 'Failed to update product status'
				},
				{ status: 500 }
			);
		}

		return json({
			success: true,
			data: result.data,
			message: `Product ${enabled ? 'enabled' : 'disabled'} successfully`
		});
	} catch (error) {
		console.error('Error processing request:', error);
		return json(
			{
				success: false,
				message: 'Internal server error'
			},
			{ status: 500 }
		);
	}
};
