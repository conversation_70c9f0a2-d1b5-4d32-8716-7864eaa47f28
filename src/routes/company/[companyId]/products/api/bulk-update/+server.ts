import { json, error } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async ({ request, locals: { supabase }, params }) => {
	try {
		const { productIds, enabled } = await request.json();
		const { companyId } = params;

		if (!Array.isArray(productIds) || typeof enabled !== 'boolean') {
			throw error(400, { message: 'Invalid request data' });
		}

		// Get user session and verify admin status
		const {
			data: { session }
		} = await supabase.auth.getSession();
		if (!session) {
			throw error(401, { message: 'You must be logged in' });
		}

		// Verify admin status
		const { data: memberData } = await supabase
			.from('company_members')
			.select('role')
			.eq('company_id', companyId)
			.eq('user_id', session.user.id)
			.single();

		if (!memberData || memberData.role !== 'admin') {
			throw error(403, { message: 'You must be a company admin to manage products' });
		}

		let updateError;

		if (enabled) {
			// When enabling products, we need to find existing records and insert new ones

			// First, get existing product records
			const { data: existingProducts } = await supabase
				.from('company_products')
				.select('product_id')
				.eq('company_id', companyId)
				.in('product_id', productIds);

			const existingProductIds = new Set(existingProducts?.map((p) => p.product_id) || []);

			// Update existing products
			if (existingProductIds.size > 0) {
				const { error: updateErr } = await supabase
					.from('company_products')
					.update({
						is_enabled: true,
						updated_at: new Date().toISOString()
					})
					.eq('company_id', companyId)
					.in('product_id', [...existingProductIds]);

				if (updateErr) updateError = updateErr;
			}

			// Insert new products that don't exist yet
			const newProductIds = productIds.filter((id) => !existingProductIds.has(id));

			if (newProductIds.length > 0) {
				const insertData = newProductIds.map((productId) => ({
					company_id: companyId,
					product_id: productId,
					is_enabled: true
				}));

				const { error: insertErr } = await supabase.from('company_products').insert(insertData);

				if (insertErr) updateError = insertErr;
			}
		} else {
			// When disabling products, we just update existing records
			const { error: updateErr } = await supabase
				.from('company_products')
				.update({
					is_enabled: false,
					updated_at: new Date().toISOString()
				})
				.eq('company_id', companyId)
				.in('product_id', productIds);

			updateError = updateErr;
		}

		if (updateError) {
			console.error('Error bulk updating products:', updateError);
			throw error(500, { message: updateError.message });
		}

		return json({ success: true });
	} catch (err) {
		console.error('Error in bulk-update API:', err);
		const message = err instanceof Error ? err.message : 'Unknown error occurred';
		throw error(500, { message });
	}
};
