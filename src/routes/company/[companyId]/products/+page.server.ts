import { error, redirect, fail, json } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';

export const load: PageServerLoad = async ({ params, locals: { supabase, safeGetSession } }) => {
	const session = await safeGetSession();
	if (!session || !session.user) {
		throw redirect(303, '/auth/login');
	}

	const { companyId } = params;

	// Check if the user has access to this company
	const { data: company, error: companyError } = await supabase
		.from('companies')
		.select('id, name')
		.eq('id', companyId)
		.single();

	if (companyError || !company) {
		console.error('Error fetching company:', companyError);
		throw error(404, 'Company not found');
	}

	// Check if user is a member of this company
	const { data: companyMember, error: memberError } = await supabase
		.from('company_members')
		.select('*')
		.eq('company_id', companyId)
		.eq('user_id', session.user.id)
		.single();

	if (memberError || !companyMember) {
		console.error('User is not a member of this company:', memberError);
		throw error(403, 'You do not have access to this company');
	}

	// Check if user is an admin (required for product management)
	if (companyMember.role !== 'admin') {
		throw error(403, 'You must be a company admin to manage products');
	}

	// Fetch all products
	const { data: products, error: productsError } = await supabase
		.from('products')
		.select('*')
		.order('name');

	if (productsError) {
		console.error('Error fetching products:', productsError);
		throw error(500, 'Failed to load products');
	}

	// Fetch company products
	const { data: companyProducts, error: companyProductsError } = await supabase
		.from('company_products')
		.select('*')
		.eq('company_id', companyId);

	if (companyProductsError) {
		console.error('Error fetching company products:', companyProductsError);
		throw error(500, 'Failed to load company products');
	}

	// If a product is marked as coming soon, make sure it's not enabled in company_products
	// This ensures consistency in case database state is inconsistent
	const cleanedCompanyProducts =
		companyProducts?.map((cp) => {
			const product = products?.find((p) => p.id === cp.product_id);
			if (product?.is_coming_soon && cp.is_enabled) {
				// Return a copy with is_enabled set to false
				return { ...cp, is_enabled: false };
			}
			return cp;
		}) || [];

	return {
		companyId,
		companyName: company.name,
		isAdmin: companyMember.role === 'admin',
		products: products || [],
		companyProducts: cleanedCompanyProducts || []
	};
};

export const actions: Actions = {
	toggleProduct: async ({ request, locals: { supabase }, params }) => {
		try {
			const { productId, enabled } = await request.json();
			const { companyId } = params;

			if (!productId || typeof enabled !== 'boolean') {
				return fail(400, { success: false, error: 'Invalid request data' });
			}

			// Get user session and verify admin status
			const {
				data: { session }
			} = await supabase.auth.getSession();
			if (!session) {
				return fail(401, { success: false, error: 'You must be logged in' });
			}

			// Verify admin status
			const { data: memberData } = await supabase
				.from('company_members')
				.select('role')
				.eq('company_id', companyId)
				.eq('user_id', session.user.id)
				.single();

			if (!memberData || memberData.role !== 'admin') {
				return fail(403, {
					success: false,
					error: 'You must be a company admin to manage products'
				});
			}

			// Check if product is marked as "coming soon" - if so, prevent enabling
			if (enabled) {
				const { data: product, error: productError } = await supabase
					.from('products')
					.select('is_coming_soon')
					.eq('id', productId)
					.single();

				if (productError) {
					console.error('Error fetching product info:', productError);
					return fail(500, { success: false, error: 'Error checking product status' });
				}

				if (product.is_coming_soon) {
					return fail(400, {
						success: false,
						error: 'This product is coming soon and cannot be enabled yet'
					});
				}
			}

			// Update product status
			const { error: updateError } = await supabase.from('company_products').upsert({
				company_id: companyId,
				product_id: productId,
				is_enabled: enabled
			});

			if (updateError) {
				console.error('Error updating product status:', updateError);
				return fail(500, { success: false, error: updateError.message });
			}

			return json({ success: true });
		} catch (error) {
			console.error('Error in toggleProduct action:', error);
			return fail(500, {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error occurred'
			});
		}
	},

	bulkUpdateProducts: async ({ request, locals: { supabase }, params }) => {
		try {
			const data = await request.json();
			const { productIds, enabled } = data;
			const { companyId } = params;

			if (!Array.isArray(productIds) || typeof enabled !== 'boolean') {
				return fail(400, { success: false, error: 'Invalid request data' });
			}

			// Get user session and verify admin status
			const {
				data: { session }
			} = await supabase.auth.getSession();
			if (!session) {
				return fail(401, { success: false, error: 'You must be logged in' });
			}

			// Verify admin status
			const { data: memberData } = await supabase
				.from('company_members')
				.select('role')
				.eq('company_id', companyId)
				.eq('user_id', session.user.id)
				.single();

			if (!memberData || memberData.role !== 'admin') {
				return fail(403, {
					success: false,
					error: 'You must be a company admin to manage products'
				});
			}

			// If enabling products, check none of them are marked as "coming soon"
			if (enabled) {
				const { data: products, error: productsError } = await supabase
					.from('products')
					.select('id, is_coming_soon')
					.in('id', productIds);

				if (productsError) {
					console.error('Error fetching products info:', productsError);
					return fail(500, { success: false, error: 'Error checking product status' });
				}

				// Filter out any "coming soon" products
				const validProductIds = products
					.filter((product) => !product.is_coming_soon)
					.map((product) => product.id);

				// If any products were filtered out, return an error
				if (validProductIds.length !== productIds.length) {
					return fail(400, {
						success: false,
						error: 'Some selected products are coming soon and cannot be enabled yet'
					});
				}

				// Update the productIds array to only include valid products
				productIds.length = 0;
				productIds.push(...validProductIds);
			}

			// Prepare upsert data for all products
			const upsertData = productIds.map((productId) => ({
				company_id: companyId,
				product_id: productId,
				is_enabled: enabled
			}));

			// Bulk update product statuses
			const { error: updateError } = await supabase.from('company_products').upsert(upsertData);

			if (updateError) {
				console.error('Error bulk updating products:', updateError);
				return fail(500, { success: false, error: updateError.message });
			}

			return json({ success: true });
		} catch (error) {
			console.error('Error in bulkUpdateProducts action:', error);
			return fail(500, {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error occurred'
			});
		}
	}
};
