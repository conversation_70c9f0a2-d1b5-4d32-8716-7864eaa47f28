<script>
	// import Database from "database.types"
	export let data;

	let { user, companyData, sourcesData, errorMessage } = data;

	// Bindings for form fields to make them editable
	let companyName = companyData ? companyData.name : '';
	let companyStatus = companyData ? companyData.status : '';
	let transcriptionTimeAllocated = companyData ? companyData.transcription_time_allocated : 0;
	let transcriptionTimeUsed = companyData ? companyData.transcription_time_used : 0;

	// Bindings for user platforms
	let updatedSourcesData = [...sourcesData];
</script>

<main class="container">
	<h1 class="mb-4 text-2xl font-bold">Company Profile</h1>
	{#if errorMessage}
		<p class="text-red-500">{errorMessage}</p>
	{/if}

	{#if companyData}
		<h2 class="text-xl font-semibold">Companies you administrate</h2>
		<form method="post" action="?/updateCompany" class="space-y-4">
			<input type="hidden" name="companyId" value={companyData.id} />
			<div>
				<label for="companyName" class="font-bold">Name:</label>
				<input
					type="text"
					id="companyName"
					name="name"
					bind:value={companyName}
					class="w-full p-2 border rounded"
				/>
			</div>
			<div>
				<label for="companyStatus" class="font-bold">Status:</label>
				<select
					id="companyStatus"
					name="status"
					bind:value={companyStatus}
					class="w-full p-2 border rounded"
				>
					<option value="trial">Trial</option>
					<option value="active">Active</option>
					<option value="inactive">Inactive</option>
				</select>
			</div>
			<div>
				<label for="transcriptionTimeAllocated" class="font-bold"
					>Transcription Time Allocated (minutes):</label
				>
				<input
					type="number"
					id="transcriptionTimeAllocated"
					name="transcription_time_allocated"
					bind:value={transcriptionTimeAllocated}
					class="w-full p-2 border rounded"
				/>
			</div>
			<div>
				<label for="transcriptionTimeUsed" class="font-bold"
					>Transcription Time Used (minutes):</label
				>
				<input
					type="number"
					id="transcriptionTimeUsed"
					name="transcription_time_used"
					bind:value={transcriptionTimeUsed}
					class="w-full p-2 border rounded"
				/>
			</div>
			<button type="submit" class="px-4 py-2 mt-2 text-white bg-blue-500 rounded"
				>Save Company Details</button
			>
		</form>
	{/if}
</main>
