import { supabase } from "$lib/supabaseClient";
import { fail } from "@sveltejs/kit";

export const load = async ({ locals, cookies }) => {
	let errorMessage = "";
	let { user } = locals;

	if (!user) {
		return { redirect: "/auth" }; // Redirect if user is not logged in
	}

	// Fetch user profile information
	const { data: companyData, error: companiesError } = await supabase
		.from("companies")
		.select("*")
		.eq("admin_uid", user.id);

	if (companiesError) {
		console.error("Error fetching user profile:", companiesError);
		errorMessage = "Failed to load user profile.";
		return { errorMessage };
	}

	// Get companyId from cookies
	const companyId = cookies.get("companyId");

	if (!companyId) {
		console.error("No company ID found in cookies.");
		errorMessage = "No company ID found.";
		return { errorMessage, user, companyData };
	}

	// Fetch user platforms information using the companyId
	const { data: sourcesData, error: sourcesError } = await supabase
		.from("data_sources")
		.select("*")
		.eq("company_id", companyId);

	if (sourcesError) {
		console.error("Error fetching user platforms:", sourcesError);
		errorMessage = "Failed to load user platforms.";
		return { errorMessage };
	}

	//console.log(`sourcesData: `, sourcesData);
	//console.log(`companyData: `, companyData);

	return { user, sourcesData, companyData, errorMessage };
};

export const actions = {
	updateCompany: async ({ request }) => {
		const formData = await request.formData();
		const companyId = formData.get("companyId");
		const name = formData.get("name");
		const status = formData.get("status");
		const transcriptionTimeAllocated = formData.get(
			"transcription_time_allocated",
		);
		const transcriptionTimeUsed = formData.get("transcription_time_used");

		const { error } = await supabase
			.from("companies")
			.update({
				name,
				status,
				transcription_time_allocated: transcriptionTimeAllocated,
				transcription_time_used: transcriptionTimeUsed,
			})
			.eq("id", companyId);

		if (error) {
			console.error("Error updating company details:", error);
			return fail(400, { error: "Failed to update company details." });
		}

		return { success: true };
	},
	updateSource: async ({ request }) => {
		const formData = await request.formData();
		const sourceId = formData.get("sourceId");
		const displayName = formData.get("name");
		const type = formData.get("type");
		const email = formData.get("email");

		const { error } = await supabase
			.from("data_sources")
			.update({
				name: displayName,
				type,
				email,
			})
			.eq("id", sourceId);

		if (error) {
			console.error("Error updating source details:", error);
			return fail(400, { error: "Failed to update source details." });
		}

		return { success: true };
	},
};
