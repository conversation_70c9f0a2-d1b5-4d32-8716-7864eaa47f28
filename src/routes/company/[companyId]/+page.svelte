<!-- src/routes/company/[companyId]/+page.svelte -->
<script lang="ts">
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';
	import type { PageData } from './$types';
	import ChatInput from '$lib/components/ChatInput.svelte';
	import { onMount } from 'svelte';

	export let data: PageData;
	let { companyId } = $page.params;
	$: ({ companies = [], user, companyId, personalization } = data);
	
	let logoUrl = '/TOTM_brandname.png';

	// Update logo URL when personalization changes
	$: {
		if (personalization?.branding_logo) {
			fetch(personalization.branding_logo)
				.then(response => {
					if (response.ok) {
						logoUrl = personalization.branding_logo || '/TOTM_brandname.png';
					} else {
						logoUrl = '/TOTM_brandname.png';
					}
				})
				.catch(() => {
					logoUrl = '/TOTM_brandname.png';
				});
		} else {
			logoUrl = '/TOTM_brandname.png';
		}
	}
	
	let isLoading = false;

	function onVoiceInput() {
		// Implement voice input functionality
	}

	async function handleMessageSubmit(message: string) {
		if (message.trim() && user?.id) {
			isLoading = true;
			try {
				await goto(`/company/${companyId}/chat/${user.id}?message=${encodeURIComponent(message)}`);
			} finally {
				isLoading = false;
			}
		}
	}
</script>

<div class="flex flex-col items-center justify-center space-y-8">
	<div class="flex justify-center w-full max-w-2xl max-h-60">
		<img src={logoUrl} alt="Company Logo" class="object-contain w-full h-auto" />
	</div>
	<h2 class="text-xl text-foreground">Have you asked TOTM?</h2>
	
	<div class="w-full max-w-lg">
		<form
			method="GET"
			action={`/company/${companyId}/chat/${user?.id}`}
			class="w-full"
		>
			<ChatInput 
				{onVoiceInput}
				{isLoading}
			/>
		</form>
	</div>
</div>
