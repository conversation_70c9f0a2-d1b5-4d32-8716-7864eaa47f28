<script lang="ts">
    import { page } from '$app/stores';
    import { <PERSON><PERSON> } from "$lib/components/ui/button";
    import { Input } from "$lib/components/ui/input";
    import { Textarea } from "$lib/components/ui/textarea";
    import { Card } from "$lib/components/ui/card";
    import * as Alert from "$lib/components/ui/alert";
    import { Loader2, ArrowLeft, Clock, Info, Shield, Database, CloudOff, Cloud, ChevronsUpDown } from "lucide-svelte";
    import { ScrollArea } from "$lib/components/ui/scroll-area";
    import { goto } from "$app/navigation";
    import * as Tabs from "$lib/components/ui/tabs";
    import * as Tooltip from "$lib/components/ui/tooltip";
    import * as Select from "$lib/components/ui/select";

    export let data;

    let query = '';
    let selectedUserId = '';
    let userEmail = '';
    let results: any = null;
    let isLoading = false;
    let error: string | null = null;
    let activeTab = "edge"; // Remove the string type to fix TypeScript error
    let showDebug = false;
    let useSharePoint = true;

    // Define interfaces for the option types to improve type safety
    interface SelectOption {
        value: string;
        label: string;
        type: string;
    }

    // Determine if we use SharePoint profiles or company members
    $: hasSharePointProfiles = data.sharePointProfiles && data.sharePointProfiles.length > 0;
    $: hasCompanyMembers = data.companyMembers && data.companyMembers.length > 0;
    
    // Create formatted options for the SharePoint profiles dropdown with better typing
    $: sharePointOptions = (data.sharePointProfiles?.map((profile: any) => {
        if (!profile.platform_user_id) return null;
        
        return {
            value: profile.platform_user_id,
            label: profile.platform_user_id,
            type: 'sharepoint'
        } as SelectOption;
    }).filter((option): option is SelectOption => option !== null) || []) as SelectOption[];
    
    // Create formatted options for the company members dropdown with better typing
    $: companyMemberOptions = (data.companyMembers?.map((member: any) => {
        if (!member.users) return null;
        
        const userName = member.users.first_name && member.users.last_name 
            ? `${member.users.first_name} ${member.users.last_name}`
            : member.users.email || 'Unknown User';
            
        return {
            value: member.users.email || '',
            label: `${userName} (${member.role || 'Member'})`,
            type: 'company_member'
        } as SelectOption;
    }).filter((option): option is SelectOption => option !== null) || []) as SelectOption[];
    
    // Combine options based on what's available
    $: userOptions = hasSharePointProfiles 
        ? sharePointOptions 
        : hasCompanyMembers 
            ? companyMemberOptions 
            : [];

    // Update userEmail when selectedUserId changes
    $: if (selectedUserId) {
        userEmail = selectedUserId; // Since we're now using email directly as the value
    } else {
        userEmail = '';
    }

    const handleSubmit = async () => {
        isLoading = true;
        error = null;
        results = null;

        try {
            console.log('Submitting retrieval test with query:', query);
            console.log('Using user:', useSharePoint ? 'SharePoint user' : 'Company member');
            console.log('Selected user ID:', selectedUserId);

            const response = await fetch('/api/test-retrieval', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    query,
                    userEmail: selectedUserId,
                    companyId: data.companyId,
                    sourceId: data.sourceId
                })
            });

            // Check if response is OK first
            if (!response.ok) {
                // Try to parse JSON error if available
                let errorData;
                try {
                    errorData = await response.json();
                    console.error('Retrieval test error:', errorData);
                    error = errorData.error || `Error ${response.status}: ${response.statusText}`;
                    
                    // Check if we have detailed Edge Function error info
                    if (errorData.edgeFunctionError) {
                        console.warn('Edge Function error details:', errorData.edgeFunctionError);
                        results = {
                            edgeFunctionError: errorData.edgeFunctionError
                        };
                    }
                } catch (e) {
                    // Not JSON, get text instead
                    const text = await response.text();
                    console.error('Non-JSON error response:', text);
                    error = `Error ${response.status}: ${text.substring(0, 100)}`;
                }
                return;
            }

            // Response is OK, parse the JSON
            const responseData = await response.json();
            console.log('Retrieval test results:', responseData);
            
            // Still check for edge function errors that might be included in a successful response
            if (responseData.edgeFunctionError) {
                console.warn('Edge Function error included in response:', responseData.edgeFunctionError);
            }
            
            results = responseData;
        } catch (e) {
            console.error('Retrieval test failed:', e);
            error = e instanceof Error ? e.message : 'Failed to run retrieval test';
        } finally {
            isLoading = false;
        }
    };

    function handleBack() {
        goto(`/company/${data.companyId}/vault`);
    }
    
    // Helper function to format time in milliseconds
    function formatTime(ms: number | undefined): string {
        if (!ms) return "N/A";
        if (ms < 1000) return `${ms.toFixed(0)}ms`;
        return `${(ms / 1000).toFixed(2)}s`;
    }

    function toggleDebug() {
        showDebug = !showDebug;
    }
    
    function toggleUserType() {
        useSharePoint = !useSharePoint;
        // Reset selection when switching types
        selectedUserId = '';
    }
</script>

<div class="container max-w-4xl p-4 mx-auto space-y-8">
    <div class="flex items-center justify-between">
        <div class="space-y-1">
            <div class="flex items-center space-x-2">
                <Button variant="ghost" size="icon" on:click={handleBack}>
                    <ArrowLeft class="w-4 h-4" />
                </Button>
                <h1 class="text-2xl font-bold">Test Semantic Search</h1>
            </div>
            <p class="text-muted-foreground">
                Test the semantic search functionality for your knowledge base documents using different retrieval methods.
            </p>
        </div>
        <Button variant="outline" size="sm" on:click={toggleDebug}>
            {showDebug ? 'Hide Debug' : 'Show Debug'}
        </Button>
    </div>

    <Card class="p-6">
        <form on:submit|preventDefault={handleSubmit} class="space-y-4">
            <div class="space-y-2">
                <label for="query" class="text-sm font-medium">Search Query</label>
                <Input
                    id="query"
                    bind:value={query}
                    placeholder="Enter your search query here..."
                    class="w-full resize-none"
                />
            </div>
            
            {#if data.isAdmin && (hasSharePointProfiles || hasCompanyMembers)}
                <div class="space-y-2">
                    <div class="flex items-center justify-between">
                        <label for="userSelect" class="text-sm font-medium">
                            Test as {useSharePoint ? 'SharePoint User' : 'Company Member'} (Optional)
                        </label>
                        
                        {#if hasSharePointProfiles && hasCompanyMembers}
                            <Button 
                                type="button" 
                                variant="outline" 
                                size="sm" 
                                on:click={toggleUserType}
                            >
                                Switch to {useSharePoint ? 'Company Members' : 'SharePoint Users'}
                            </Button>
                        {/if}
                    </div>
                    
                    <Select.Root 
                        onSelectedChange={(selected) => {
                            if (selected) {
                                selectedUserId = selected.value;
                                userEmail = selected.value;
                            } else {
                                selectedUserId = "";
                                userEmail = "";
                            }
                        }}
                        selected={selectedUserId ? { value: selectedUserId, label: userEmail } : undefined}
                    >
                        <Select.Trigger id="userSelect" class="w-full">
                            <Select.Value placeholder={useSharePoint ? "Select a SharePoint user to test as..." : "Select a company member to test as..."} />
                        </Select.Trigger>
                        <Select.Content>
                            <Select.Item value="">
                                <span>Test as yourself</span>
                            </Select.Item>
                            
                            {#if useSharePoint && sharePointOptions.length > 0}
                                <Select.Group>
                                    <Select.Label>SharePoint Users</Select.Label>
                                    {#each sharePointOptions as option}
                                        <Select.Item value={option.value}>
                                            <span>{option.label}</span>
                                        </Select.Item>
                                    {/each}
                                </Select.Group>
                            {:else if !useSharePoint && companyMemberOptions.length > 0}
                                <Select.Group>
                                    <Select.Label>Company Members</Select.Label>
                                    {#each companyMemberOptions as option}
                                        <Select.Item value={option.value}>
                                            <span>{option.label}</span>
                                        </Select.Item>
                                    {/each}
                                </Select.Group>
                            {/if}
                        </Select.Content>
                    </Select.Root>
                </div>
            {/if}

            <Button 
                type="submit" 
                disabled={isLoading || !query.trim()}
                class="w-full"
            >
                {#if isLoading}
                    <Loader2 class="w-4 h-4 mr-2 animate-spin" />
                    Searching...
                {:else}
                    Search Documents
                {/if}
            </Button>
        </form>
    </Card>

    {#if showDebug}
        <div class="mb-8 p-4 bg-gray-100 dark:bg-gray-800 rounded-md">
            <h3 class="text-lg font-bold mb-2">Debug Information</h3>
            
            <!-- Error display section -->
            {#if error || (results && results.edgeFunctionError)}
                <div class="mb-4 p-3 bg-red-100 dark:bg-red-900/30 rounded border border-red-300 dark:border-red-800">
                    <h4 class="font-bold text-red-800 dark:text-red-300">Errors</h4>
                    
                    {#if error}
                        <div class="mt-2">
                            <p class="text-red-700 dark:text-red-300">API Error: {error}</p>
                        </div>
                    {/if}
                    
                    {#if results && results.edgeFunctionError}
                        <div class="mt-2">
                            <p class="font-semibold text-red-700 dark:text-red-300">
                                Edge Function Error: {results.edgeFunctionError.message}
                            </p>
                            {#if results.edgeFunctionError.statusCode}
                                <p class="text-red-700 dark:text-red-300">Status: {results.edgeFunctionError.statusCode}</p>
                            {/if}
                            {#if results.edgeFunctionError.cause}
                                <p class="text-red-700 dark:text-red-300">Cause: {results.edgeFunctionError.cause}</p>
                            {/if}
                            {#if results.edgeFunctionError.details && typeof results.edgeFunctionError.details === 'object'}
                                <div class="mt-2">
                                    <p class="font-semibold text-red-700 dark:text-red-300">Additional Details:</p>
                                    <pre class="mt-1 text-sm bg-red-50 dark:bg-red-950/50 p-2 rounded overflow-auto max-h-60">
                                        {JSON.stringify(results.edgeFunctionError.details, null, 2)}
                                    </pre>
                                </div>
                            {/if}
                        </div>
                    {/if}
                </div>
            {/if}
            
            <!-- User details -->
            <div class="mb-4">
                <h4 class="font-bold">User</h4>
                <p>Current User ID: {data.user?.id || 'Unknown'}</p>
                <p>Is Admin: {data.isAdmin ? 'Yes' : 'No'}</p>
                <p>Selected User ID: {selectedUserId || '(none)'}</p>
                <p>Has SharePoint Profiles: {hasSharePointProfiles ? 'Yes' : 'No'} ({(data.sharePointProfiles || []).length})</p>
                <p>Has Company Members: {hasCompanyMembers ? 'Yes' : 'No'} ({(data.companyMembers || []).length})</p>
            </div>
            
            <!-- SharePoint Profiles Table -->
            {#if data.sharePointProfiles?.length > 0}
                <div class="mb-4">
                    <h4 class="font-bold">SharePoint Profiles</h4>
                    <div class="p-2 overflow-x-auto bg-white border rounded mt-2">
                        <table class="w-full text-xs text-left">
                            <thead>
                                <tr class="border-b">
                                    <th class="p-1">ID</th>
                                    <th class="p-1">User ID</th>
                                    <th class="p-1">Company Member ID</th>
                                </tr>
                            </thead>
                            <tbody>
                                {#each data.sharePointProfiles as profile}
                                    <tr class="border-b">
                                        <td class="p-1">{profile.id}</td>
                                        <td class="p-1">{profile.platform_user_id}</td>
                                        <td class="p-1">{profile.company_member_id}</td>
                                    </tr>
                                {/each}
                            </tbody>
                        </table>
                    </div>
                </div>
            {/if}
            
            <!-- Company Members Table -->
            {#if data.companyMembers?.length > 0}
                <div class="mb-4">
                    <h4 class="font-bold">Company Members</h4>
                    <div class="p-2 overflow-x-auto bg-white border rounded mt-2">
                        <table class="w-full text-xs text-left">
                            <thead>
                                <tr class="border-b">
                                    <th class="p-1">ID</th>
                                    <th class="p-1">Email</th>
                                    <th class="p-1">Name</th>
                                    <th class="p-1">Role</th>
                                </tr>
                            </thead>
                            <tbody>
                                {#each data.companyMembers as member}
                                    <tr class="border-b">
                                        <td class="p-1">{member.id}</td>
                                        <td class="p-1">{member.users?.email || 'N/A'}</td>
                                        <td class="p-1">{(member.users?.first_name || '') + ' ' + (member.users?.last_name || '')}</td>
                                        <td class="p-1">{member.role || 'N/A'}</td>
                                    </tr>
                                {/each}
                            </tbody>
                        </table>
                    </div>
                </div>
            {/if}

            <!-- Debug logs if available -->
            {#if results?.edgeFunctionDebug && results.edgeFunctionDebug.length > 0}
                <div class="p-3 mt-4 rounded-lg bg-gray-50">
                    <h4 class="font-bold mb-2">Debug Logs</h4>
                    <div class="p-3 overflow-x-auto font-mono text-xs text-green-400 bg-black rounded">
                        {#each results.edgeFunctionDebug as log}
                            <div class="mb-1">{log}</div>
                        {/each}
                    </div>
                </div>
            {/if}
        </div>
    {/if}

    {#if error}
        <Alert.Root variant="destructive">
            <Alert.Title>Error</Alert.Title>
            <Alert.Description>{error}</Alert.Description>
        </Alert.Root>
    {/if}

    {#if results}
        <div class="space-y-4">
            <div class="p-4 rounded-lg bg-muted">
                <div class="space-y-1">
                    <p class="text-sm font-medium">Query: {results.query}</p>
                    <p class="text-sm text-muted-foreground">
                        Embedding Length: {results.embedding_length}
                    </p>
                    {#if userEmail}
                        <p class="text-sm text-muted-foreground">
                            Testing as user: {userEmail}
                        </p>
                    {/if}
                    <div class="flex flex-wrap gap-4 text-sm text-muted-foreground">
                        <p>Edge Function Matches: {results.edgeFunctionMatches?.length ?? 0}</p>
                        <p>Permission Matches: {results.permissionMatches?.length ?? 0}</p>
                        <p>Public Matches: {results.publicMatches?.length ?? 0}</p>
                        {#if results.userPermissionsUsed !== undefined}
                            <p class="text-primary">
                                Using: {results.userPermissionsUsed ? 'New Permission System' : 'Legacy Permission System'}
                            </p>
                        {/if}
                    </div>
                </div>
            </div>
            
            <Tabs.Root 
                value={activeTab}
                onValueChange={(value) => activeTab = value}
                class="w-full"
            >
                <Tabs.List class="grid w-full grid-cols-3">
                    <Tabs.Trigger value="edge" class="flex items-center justify-center gap-2">
                        <span class="font-semibold text-green-600">Edge Function</span>
                        {#if results.edgeFunctionTiming}
                            <span class="flex items-center text-xs px-2 py-0.5 rounded-full bg-green-100 text-green-800">
                                <Clock class="w-3 h-3 mr-1" />
                                {formatTime(results.edgeFunctionTiming.total_ms)}
                            </span>
                        {/if}
                    </Tabs.Trigger>
                    <Tabs.Trigger value="permission" class="flex items-center justify-center gap-2">
                        <span class="font-semibold text-blue-600">DB Permissions</span>
                    </Tabs.Trigger>
                    <Tabs.Trigger value="public" class="flex items-center justify-center gap-2">
                        <span class="font-semibold text-amber-600">Public</span>
                    </Tabs.Trigger>
                </Tabs.List>
                
                <div class="mt-4">
                    <Tabs.Content value="edge">
                        <div class="p-3 mb-4 rounded-md bg-green-50">
                            <div class="flex items-start gap-2">
                                <Info class="w-5 h-5 text-green-700 mt-0.5 flex-shrink-0" />
                                <div class="text-sm text-green-800">
                                    <p class="font-medium">Edge Function Flow:</p>
                                    <ol class="pl-5 mt-1 space-y-1 list-decimal">
                                        <li>Generates embedding from query</li>
                                        <li>Calls <code>search_documents_by_company</code> database function for initial results</li>
                                        <li>Checks document permissions via Microsoft Graph API using OAuth tokens from data source</li>
                                        <li>Returns documents with content for those you can access; marks others as "Redacted"</li>
                                    </ol>
                                    <p class="mt-2 italic"><Shield class="inline w-4 h-4" /> The MS Graph permissions check uses the OAuth refresh token stored in <code>data_sources.secret_oauth</code> vault reference.</p>
                                </div>
                            </div>
                        </div>
                        
                        <ScrollArea class="h-[500px] rounded-lg border">
                            <div class="p-4 space-y-8">
                                {#if results.edgeFunctionError}
                                    <Alert.Root variant="destructive">
                                        <Alert.Title>Edge Function Error</Alert.Title>
                                        <Alert.Description>
                                            {results.edgeFunctionError.message || results.edgeFunctionError.details || JSON.stringify(results.edgeFunctionError)}
                                        </Alert.Description>
                                    </Alert.Root>
                                {:else if !results.edgeFunctionMatches || results.edgeFunctionMatches.length === 0}
                                    <div class="p-4 text-center text-muted-foreground">
                                        No matching documents found from Edge Function
                                    </div>
                                {:else}
                                    <!-- Performance stats -->
                                    {#if results.edgeFunctionTiming}
                                        <div class="p-3 rounded-lg bg-green-50">
                                            <h3 class="mb-2 text-sm font-medium text-green-800">Performance Metrics</h3>
                                            <div class="grid grid-cols-2 gap-2 text-xs">
                                                <div class="flex items-center justify-between p-2 bg-white rounded">
                                                    <span>Total Time:</span>
                                                    <span class="font-semibold">{formatTime(results.edgeFunctionTiming.total_ms)}</span>
                                                </div>
                                                <div class="flex items-center justify-between p-2 bg-white rounded">
                                                    <span>Database Query:</span>
                                                    <span class="font-semibold">{formatTime(results.edgeFunctionTiming.db_query_ms)}</span>
                                                </div>
                                                <div class="flex items-center justify-between p-2 bg-white rounded">
                                                    <span>Permission Check:</span>
                                                    <span class="font-semibold">{formatTime(results.edgeFunctionTiming.permission_check_ms)}</span>
                                                </div>
                                                <div class="flex items-center justify-between p-2 bg-white rounded">
                                                    <span>Document Processing:</span>
                                                    <span class="font-semibold">{formatTime(results.edgeFunctionTiming.document_processing_ms)}</span>
                                                </div>
                                            </div>
                                            {#if results.edgeFunctionCounts}
                                                <div class="flex items-center justify-between p-2 mt-2 text-xs bg-white rounded">
                                                    <span>Document Access:</span>
                                                    <div class="flex gap-2">
                                                        <span class="px-2 py-0.5 bg-green-100 text-green-800 rounded-full">
                                                            {results.edgeFunctionCounts.accessible} Accessible
                                                        </span>
                                                        <span class="px-2 py-0.5 bg-red-100 text-red-800 rounded-full">
                                                            {results.edgeFunctionCounts.redacted} Redacted
                                                        </span>
                                                    </div>
                                                </div>
                                            {/if}
                                        </div>
                                    {/if}
                                    
                                    <div class="space-y-4">
                                        <div class="flex items-center gap-2">
                                            <h3 class="font-semibold">Edge Function Matches</h3>
                                            <span class="px-2 py-1 text-xs text-green-800 bg-green-100 rounded-full">
                                                MS Graph Permissions
                                            </span>
                                        </div>
                                        {#each results.edgeFunctionMatches as doc}
                                            <Card class="p-4 border-l-4 {doc.has_access ? 'border-l-green-500' : 'border-l-red-500'}">
                                                <div class="space-y-2">
                                                    <div class="flex items-center justify-between">
                                                        <div class="flex items-center gap-2">
                                                            <h3 class="font-medium">
                                                                {doc.metadata?.file_name || 'Unnamed Document'}
                                                            </h3>
                                                            {#if doc.has_access}
                                                                <span class="px-2 py-0.5 text-xs rounded-full bg-green-100 text-green-800">
                                                                    Accessible
                                                                </span>
                                                            {:else}
                                                                <span class="px-2 py-0.5 text-xs rounded-full bg-red-100 text-red-800">
                                                                    Redacted
                                                                </span>
                                                            {/if}
                                                        </div>
                                                        <span class="text-sm text-muted-foreground">
                                                            {(doc.similarity * 100).toFixed(2)}% match
                                                        </span>
                                                    </div>
                                                    <p class="text-sm whitespace-pre-wrap">{doc.content}</p>
                                                    <div class="pt-2 text-xs border-t text-muted-foreground">
                                                        <p>Path: {doc.metadata?.file_path}</p>
                                                        <p>Type: {doc.metadata?.file_type}</p>
                                                    </div>
                                                </div>
                                            </Card>
                                        {/each}
                                    </div>
                                {/if}
                            </div>
                        </ScrollArea>
                    </Tabs.Content>
                    
                    <Tabs.Content value="permission">
                        <div class="p-3 mb-4 rounded-md bg-blue-50">
                            <div class="flex items-start gap-2">
                                <Info class="w-5 h-5 text-blue-700 mt-0.5 flex-shrink-0" />
                                <div class="text-sm text-blue-800">
                                    <p class="font-medium">Database Permissions Flow:</p>
                                    <ol class="pl-5 mt-1 space-y-1 list-decimal">
                                        <li>Generates embedding from query</li>
                                        <li>Calls <code>match_documents_with_user_permissions</code> or <code>match_documents_with_permissions</code> function</li>
                                        <li>PostgreSQL handles permission checking directly in the database</li>
                                        <li>Returns only documents the user has permission to view</li>
                                    </ol>
                                    <p class="mt-2 italic"><Database class="inline w-4 h-4" /> Permissions are determined by database-level RLS policies and user permission tables.</p>
                                </div>
                            </div>
                        </div>
                        
                        <ScrollArea class="h-[500px] rounded-lg border">
                            <div class="p-4 space-y-8">
                                {#if results.permissionMatches?.length === 0}
                                    <div class="p-4 text-center text-muted-foreground">
                                        No matching documents found using database permissions
                                    </div>
                                {:else}
                                    <div class="space-y-4">
                                        <div class="flex items-center gap-2">
                                            <h3 class="font-semibold">Permission-Based Matches</h3>
                                            <span class="px-2 py-1 text-xs rounded-full bg-primary/10 text-primary">
                                                Full Access
                                            </span>
                                            {#if results.userPermissionsUsed !== undefined}
                                                <span class="px-2 py-1 text-xs rounded-full {results.userPermissionsUsed ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}">
                                                    {results.userPermissionsUsed ? 'File Permission Cache' : 'Standard Permissions'}
                                                </span>
                                            {/if}
                                        </div>
                                        {#each results.permissionMatches as doc}
                                            <Card class="p-4 border-l-4 border-l-blue-500">
                                                <div class="space-y-2">
                                                    <div class="flex items-center justify-between">
                                                        <h3 class="font-medium">
                                                            {doc.metadata?.file_name || 'Unnamed Document'}
                                                        </h3>
                                                        <span class="text-sm text-muted-foreground">
                                                            {(doc.similarity * 100).toFixed(2)}% match
                                                        </span>
                                                    </div>
                                                    <p class="text-sm whitespace-pre-wrap">{doc.content}</p>
                                                    <div class="pt-2 text-xs border-t text-muted-foreground">
                                                        <p>Path: {doc.metadata?.file_path || 'N/A'}</p>
                                                        <p>Type: {doc.metadata?.file_type || 'N/A'}</p>
                                                    </div>
                                                </div>
                                            </Card>
                                        {/each}
                                    </div>
                                {/if}
                            </div>
                        </ScrollArea>
                    </Tabs.Content>
                    
                    <Tabs.Content value="public">
                        <div class="p-3 mb-4 rounded-md bg-amber-50">
                            <div class="flex items-start gap-2">
                                <Info class="w-5 h-5 text-amber-700 mt-0.5 flex-shrink-0" />
                                <div class="text-sm text-amber-800">
                                    <p class="font-medium">Public Access Flow:</p>
                                    <ol class="pl-5 mt-1 space-y-1 list-decimal">
                                        <li>Generates embedding from query</li>
                                        <li>Calls <code>match_documents_public</code> database function</li>
                                        <li>Returns only documents with public access regardless of user permissions</li>
                                        <li>Limited to specific metadata and content based on public access rules</li>
                                    </ol>
                                    <p class="mt-2 italic"><CloudOff class="inline w-4 h-4" /> Public access is more limited and does not use MS Graph or detailed permissions.</p>
                                </div>
                            </div>
                        </div>
                        
                        <ScrollArea class="h-[500px] rounded-lg border">
                            <div class="p-4 space-y-8">
                                {#if results.publicMatches?.length === 0}
                                    <div class="p-4 text-center text-muted-foreground">
                                        No matching documents found with public access
                                    </div>
                                {:else}
                                    <div class="space-y-4">
                                        <div class="flex items-center gap-2">
                                            <h3 class="font-semibold">Public Matches</h3>
                                            <span class="px-2 py-1 text-xs rounded-full bg-amber-100 text-amber-800">
                                                Limited Access
                                            </span>
                                        </div>
                                        {#each results.publicMatches as doc}
                                            <Card class="p-4 border-l-4 border-l-amber-500">
                                                <div class="space-y-2">
                                                    <div class="flex items-center justify-between">
                                                        <h3 class="font-medium">
                                                            {doc.metadata?.file_name || 'Unnamed Document'}
                                                        </h3>
                                                        <span class="text-sm text-muted-foreground">
                                                            {(doc.similarity * 100).toFixed(2)}% match
                                                        </span>
                                                    </div>
                                                    <p class="text-sm whitespace-pre-wrap">{doc.content}</p>
                                                    <div class="pt-2 text-xs border-t text-muted-foreground">
                                                        <p>Path: {doc.metadata?.file_path || 'N/A'}</p>
                                                        <p>Type: {doc.metadata?.file_type || 'N/A'}</p>
                                                    </div>
                                                </div>
                                            </Card>
                                        {/each}
                                    </div>
                                {/if}
                            </div>
                        </ScrollArea>
                    </Tabs.Content>
                </div>
            </Tabs.Root>
        </div>
    {/if}
</div>