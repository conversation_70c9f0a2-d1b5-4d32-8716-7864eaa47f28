import { error } from "@sveltejs/kit";
import type { PageServerLoad } from "./$types";

export const load: PageServerLoad = async (
    { params, locals: { supabase, user } },
) => {
    if (!user?.id) {
        throw error(401, "Unauthorized");
    }

    // Get the data source details to verify access
    const { data: dataSource, error: dataSourceError } = await supabase
        .from("data_sources")
        .select("id, company_id")
        .eq("id", params.sourceId)
        .eq("company_id", params.companyId)
        .single();

    if (dataSourceError || !dataSource) {
        console.error("Data source error:", {
            error: dataSourceError,
            sourceId: params.sourceId,
            companyId: params.companyId,
        });
        throw error(404, "Data source not found");
    }

    // Verify user has access to this company
    const { data: companyMember, error: memberError } = await supabase
        .from("company_members")
        .select("id, role")
        .eq("company_id", params.companyId)
        .eq("user_id", user.id)
        .eq("is_active", true)
        .single();

    if (memberError || !companyMember) {
        throw error(403, "Access denied");
    }

    // Fetch SharePoint profiles for company members
    const { data: sharePointProfiles, error: profilesError } = await supabase
        .from("user_platform_profiles")
        .select(`
            id,
            platform_user_id,
            company_member_id,
            company_members!inner(
                id,
                company_id
            )
        `)
        .eq("platform_type", "sharepoint")
        .eq("company_members.company_id", params.companyId);

    if (profilesError) {
        console.error("Error fetching SharePoint profiles:", profilesError);
    }

    // Fetch all company members with their user details as a fallback
    const { data: companyMembers, error: companyMembersError } = await supabase
        .from("company_members")
        .select(`
            id,
            role,
            users!inner(
                id,
                email,
                first_name,
                last_name
            )
        `)
        .eq("company_id", params.companyId)
        .eq("is_active", true)
        .order("role", { ascending: false }); // Sort by role, admins first

    if (companyMembersError) {
        console.error("Error fetching company members:", companyMembersError);
    }

    return {
        user,
        companyId: params.companyId,
        sourceId: params.sourceId,
        sharePointProfiles: sharePointProfiles || [],
        companyMembers: companyMembers || [],
        isAdmin: companyMember.role === "admin",
    };
};
