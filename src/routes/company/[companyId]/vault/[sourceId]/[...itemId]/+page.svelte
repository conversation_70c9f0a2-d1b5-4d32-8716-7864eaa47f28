<script lang="ts">
	import { page } from '$app/stores';
	import FileExplorer from '$lib/components/FileExplorer.svelte';
	import { onMount } from 'svelte';
	import { goto, invalidate } from '$app/navigation';
	import { Skeleton } from "$lib/components/ui/skeleton";
	import * as Breadcrumb from "$lib/components/ui/breadcrumb";
	import { ChevronRight, LayoutGridIcon, ListIcon } from "lucide-svelte";
	import { browser } from '$app/environment';
	import type { SharePointFile } from '$lib/types';
	import {ScrollArea} from "$lib/components/ui/scroll-area";
	import {Button} from '$lib/components/ui/button';
	export let data;
	let loading = false;
	let loadingProgress = 0;
	let isNavigating = false;
	let isGridView = false;
	onMount(() => {
	});

	$: sourceId = data.sourceId;
	$: itemId = data.itemId;
	$: user = data.user;
	$: breadcrumbData = data.breadcrumbData ?? [];

	const onFolderClick = async (file: SharePointFile) => {
		if (isNavigating || loading) {
			return;
		}
		
		let currentPath = $page.url;
		if (file.id) {
			isNavigating = true;
			loading = true;
			loadingProgress = 0;

			try {
				// Get the current path without the sourceId
				const currentPathParts = currentPath.pathname.split('/');
				const vaultIndex = currentPathParts.indexOf('vault');
				const sourceIndex = vaultIndex + 1;
				const currentRelativePath = currentPathParts.slice(sourceIndex + 2).join('/'); // +2 to skip 'root'
				
				// Build the new path
				const newPath = currentRelativePath ? `${currentRelativePath}/${file.id}` : file.id;
				
				const navUrl = new URL(`${currentPath.pathname}/${file.id}`, window.location.origin);
				navUrl.searchParams.set('requestType', 'navigation');
				
				await goto(navUrl.toString(), {
					invalidateAll: true,
					keepFocus: true
				});
			} finally {
				isNavigating = false;
				loading = false;
				loadingProgress = 100;
			}
		}
	};

	async function handleBreadcrumbClick(href: string, event: Event) {
		event.preventDefault();
		if (isNavigating || loading) {
			return;
		}
		
		isNavigating = true;
		loading = true;
		loadingProgress = 0;
		
		try {
			const pathParts = href.split('/');
			const vaultIndex = pathParts.indexOf('vault');
			const targetSourceId = pathParts[vaultIndex + 1];
			const targetPath = pathParts.slice(vaultIndex + 2).join('/'); // +2 to skip 'root'

		} finally {
			isNavigating = false;
			loading = false;
			loadingProgress = 100;
		}
	}

	// Watch for URL changes to ensure proper request type
	$: if (browser && $page.url.searchParams.get('requestType') === null && !isNavigating) {
		const newUrl = new URL($page.url);
		newUrl.searchParams.set('requestType', 'navigation');
		goto(newUrl.toString(), { replaceState: true, keepFocus: true });
	}

	// Create breadcrumb items using the breadcrumbData from the server
	$: breadcrumbItems = breadcrumbData.map((item) => ({
		label: item.name,
		href: `/company/${$page.params.companyId}/vault/${sourceId}/${item.path}`
	}));

	// Add home as the first item
	$: fullBreadcrumbs = [
		{ label: 'Vault', href: `/company/${$page.params.companyId}/vault/` },
		...breadcrumbItems
	];
</script>
<div class="sticky z-10 p-4 bg-background/80 backdrop-blur-sm top-16">
	<div class="mb-4">
		<Breadcrumb.Root>
			<Breadcrumb.List>
				{#each fullBreadcrumbs as crumb, i}
					<Breadcrumb.Item>
						{#if i === fullBreadcrumbs.length - 1}
							<Breadcrumb.Page>{crumb.label}</Breadcrumb.Page>
						{:else}
							<Breadcrumb.Link on:click={(e) => handleBreadcrumbClick(crumb.href, e)} href={crumb.href}>{crumb.label}</Breadcrumb.Link>
						{/if}
					</Breadcrumb.Item>
					{#if i !== fullBreadcrumbs.length - 1}
						<Breadcrumb.Separator>
							<ChevronRight class="w-4 h-4" />
						</Breadcrumb.Separator>
					{/if}
				{/each}
			</Breadcrumb.List>
		</Breadcrumb.Root>
	</div>

	<div class="flex flex-row justify-between">

		{#if breadcrumbData.length > 0}
		<h1 class="mb-4 text-2xl font-bold">{breadcrumbData[breadcrumbData.length - 1].name}</h1>
		{/if}
		<div class="flex justify-end">
			<Button variant="outline" size="icon" on:click={() => (isGridView = !isGridView)}>
			{#if isGridView}
			<ListIcon class="w-4 h-4" />
			{:else}
			<LayoutGridIcon class="w-4 h-4" />
			{/if}
		</Button>
	</div>
	</div>
</div>
	
	{#if loading}
		<div class="relative mb-4">
			<div class="w-full h-1 bg-secondary">
				<div 
					class="h-full transition-all duration-300 ease-in-out bg-primary" 
					style="width: {loadingProgress}%"
				/>
			</div>
		</div>
		<div class="space-y-4">
			{#each Array(5) as _}
				<div class="flex items-center space-x-4">
					<Skeleton class="w-12 h-12 rounded" />
					<div class="space-y-2">
						<Skeleton class="h-4 w-[250px]" />
						<Skeleton class="h-4 w-[200px]" />
					</div>
				</div>
			{/each}
		</div>
	{:else if sourceId}
			<!-- <ScrollArea class="h-[calc(100vh-200px)]"> -->
				<div class="relative max-w-full">
					<FileExplorer {user} {sourceId} isGridView={isGridView} files={data.folderContents} onFolderClick={onFolderClick} />
				</div>
			<!-- </ScrollArea> -->
	{/if}
