import { error, fail, redirect } from '@sveltejs/kit';
import { fetchFileAccessMembers, fetchFolderContents } from '$lib/utils/microsoftGraph';
import type { PageServerLoad } from './$types';
import { getSecretById } from '$lib/server/vault';
import { exchangeRefreshToken } from '$lib/server/microsoft';
import { generateState, getN8nWebhookUrl } from '$lib/utils';
import { fetchItemPermissions } from '$lib/utils';
import { PUBLIC_AZURE_CLIENT_ID } from '$env/static/public';
import { baseUrl } from '$lib/utils';

// Helper function to fetch folder details
async function getFolderDetails(
	userId: string,
	folderId: string,
	sourceId: string,
	pathSegments: string[],
	currentIndex: number,
	accessToken: string,
	fetch: typeof global.fetch
) {
	try {
		// Get the parent folder ID (or undefined for root level)
		const parentFolderId = currentIndex > 0 ? pathSegments[currentIndex - 1] : undefined;

		// Fetch contents of the parent folder
		const contents = await fetchFolderContents(accessToken, sourceId, fetch, parentFolderId);
		const folder = contents.find((item) => item.id === folderId);

		return folder?.name ?? folderId;
	} catch (err) {
		console.error(`Error fetching folder details for ${folderId}:`, err);
		return folderId;
	}
}

export const load: PageServerLoad = async ({
	params,
	locals: { supabase, user },
	fetch,
	url,
	cookies
}) => {
	const { sourceId, itemId } = params;
	const requestType = url.searchParams.get('requestType') || 'direct';
	const clientClickTime = url.searchParams.get('clickTime');
	const clientStartTime = url.searchParams.get('startTime');
	const serverStartTime = new Date().toISOString();

	const timingInfo = clientClickTime
		? `(${new Date(serverStartTime).getTime() - new Date(clientClickTime).getTime()}ms since click)`
		: '';

	console.log(
		`[${serverStartTime}] 🔍 Server Load - ${requestType.toUpperCase()} request for folder: ${sourceId}/${itemId} ${timingInfo}`
	);

	if (user?.id) {
		try {
			const startTime = performance.now();

			// Get the data source details
			const { data: dataSource, error: dataSourceError } = await supabase
				.from('data_sources')
				.select('id_token, secret_oauth, platform_type, scope, drive_id, display_name')
				.eq('id', sourceId)
				.eq('company_id', params.companyId)
				.single();

			if (dataSourceError || !dataSource) {
				console.error('Data source error:', {
					error: dataSourceError,
					sourceId,
					companyId: params.companyId
				});
				throw error(401, 'Microsoft data source not found');
			}

			// Verify it's a Microsoft data source
			if (dataSource.platform_type !== 'onedrive' && dataSource.platform_type !== 'sharepoint') {
				throw error(400, 'Invalid data source type - must be OneDrive or SharePoint');
			}

			// Fetch refresh token from vault
			const refreshToken = await getSecretById(dataSource.secret_oauth);
			if (!refreshToken) {
				throw error(500, 'Missing refresh token');
			}

			try {
				// Exchange refresh token for access token
				const accessToken = await exchangeRefreshToken(refreshToken as string, dataSource.scope);

				// Use the access token for subsequent API calls
				const itemIdSegment = itemId.split('/').pop();
				if (!itemIdSegment) {
					throw error(400, 'Invalid item ID');
				}

				// Build breadcrumb data
				const pathSegments = itemId.split('/').filter(Boolean);

				console.log(`[${serverStartTime}] 📂 Fetching folder contents for: ${sourceId}/${itemId}`);

				const [breadcrumbData, folderContents] = await Promise.all([
					Promise.all(
						pathSegments.map(async (segment, index) => {
							// For root folder, use the data source name
							if (segment === 'root') {
								return {
									name: dataSource.display_name || 'Root',
									path: pathSegments.slice(0, index + 1).join('/')
								};
							}
							const name = await getFolderDetails(
								user.id,
								segment,
								dataSource.drive_id,
								pathSegments,
								index,
								accessToken,
								fetch
							);
							return {
								name,
								path: pathSegments.slice(0, index + 1).join('/')
							};
						})
					),
					fetchFolderContents(accessToken, dataSource.drive_id, fetch, itemIdSegment)
				]);

				// Get the list of file IDs
				const fileIds = folderContents.filter((file) => !file.folder).map((file) => file.id);

				const { data: ingestedFiles, error: dbError } = await supabase
					.from('files')
					.select('id, external_id')
					.eq('company_id', params.companyId)
					.in('external_id', fileIds);

				if (dbError) {
					console.error('Database query error:', dbError);
					throw error(500, 'Failed to check ingestion status');
				}

				console.log(`[${serverStartTime}] 📑 Checking ingestion status:
				- File IDs to check: ${JSON.stringify(fileIds)}
				- Ingested files found: ${JSON.stringify(ingestedFiles)}
			`);

				const ingestedFileIds = new Set(ingestedFiles?.map((f) => f.external_id) || []);

				const contentsWithIngestionStatus = folderContents.map((file) => ({
					...file,
					isIngested: !file.folder && ingestedFileIds.has(file.id)
				}));

				const endTime = performance.now();
				const duration = (endTime - startTime).toFixed(2);

				console.log(
					`[${serverStartTime}] ✅ ${requestType.toUpperCase()} request completed in ${duration}ms`
				);
				console.log(`[${serverStartTime}] 📊 Stats:
				- Total items: ${contentsWithIngestionStatus.length}
					- Folders: ${contentsWithIngestionStatus.filter((f) => f.folder).length}
					- Files: ${contentsWithIngestionStatus.filter((f) => !f.folder).length}
				- Ingested files: ${ingestedFileIds.size}
				`);

				return {
					folderContents: contentsWithIngestionStatus,
					sourceId,
					itemId,
					user,
					breadcrumbData
				};
			} catch (err) {
				if (err instanceof Error && err.message === 'consent_required') {
					// Generate state with return URL
					const statePayload = {
						companyId: params.companyId,
						sourceId,
						returnUrl: url.pathname + url.search
					};
					const state = generateState(statePayload);

					// Store state in cookie
					cookies.set('state', state, {
						path: '/',
						httpOnly: true,
						secure: true,
						sameSite: 'lax'
					});

					// Redirect to Microsoft login for reauthorization
					const authUrl =
						`https://login.microsoftonline.com/common/oauth2/v2.0/authorize?` +
						`client_id=${encodeURIComponent(PUBLIC_AZURE_CLIENT_ID)}` +
						`&response_type=code` +
						`&redirect_uri=${encodeURIComponent(`${baseUrl}/auth/add-data-source`)}` +
						`&scope=${encodeURIComponent(dataSource.scope)}` +
						`&state=${encodeURIComponent(state)}` +
						`&prompt=consent`;

					throw redirect(302, authUrl);
				}
				throw error(500, 'Failed to access data source');
			}
		} catch (err) {
			console.error(`[${serverStartTime}] ❌ Error processing ${requestType} request:`, err);
			throw error(500, 'Failed to load folder contents');
		}
	}
};

export const actions = {
	ingest: async ({ request, locals: { supabase, user }, params }) => {
		const formData = await request.formData();
		const fileId = formData.get('fileId')?.toString();
		const fileName = formData.get('fileName')?.toString();

		if (!user?.id || !fileId || !fileName) {
			return fail(400, { message: 'Missing required data' });
		}

		try {
			const session = await supabase.auth.getSession();
			if (!session.data.session) {
				return fail(401, { message: 'No active session found' });
			}

			const url = getN8nWebhookUrl(
				'ingest',
				{
					jwt: session.data.session.access_token,
					userId: user.id,
					sourceId: params.sourceId,
					itemId: fileId
				},
				'staging'
			);

			const response = await fetch(url, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				}
			});

			const data = await response.json();

			if (data.status === '200') {
				return {
					success: true,
					message: `Successfully added ${fileName} to TOTM Knowledge`
				};
			} else {
				return fail(500, {
					message: data.message || `Failed to ingest: ${response.statusText}`
				});
			}
		} catch (error) {
			const message = error instanceof Error ? error.message : 'Unknown error occurred';
			return fail(500, {
				message: `Failed to add ${fileName} to TOTM Knowledge: ${message}`
			});
		}
	},

	delete: async ({ request, locals: { supabase, user }, params }) => {
		const formData = await request.formData();
		const fileId = formData.get('fileId')?.toString();
		const fileName = formData.get('fileName')?.toString();

		if (!user?.id || !fileId || !fileName) {
			return fail(400, { message: 'Missing required data' });
		}

		try {
			const { data: removedFile, error } = await supabase
				.from('files')
				.delete()
				.eq('external_id', fileId)
				.eq('company_id', params.companyId)
				.select();

			if (error) {
				return fail(500, { message: error.message });
			}

			if (!removedFile || removedFile.length === 0) {
				return fail(404, { message: 'File not found in knowledge base' });
			}

			return {
				success: true,
				message: `Successfully removed ${fileName} from TOTM Knowledge`
			};
		} catch (error) {
			const message = error instanceof Error ? error.message : 'Unknown error occurred';
			return fail(500, {
				message: `Failed to remove ${fileName} from TOTM Knowledge: ${message}`
			});
		}
	},

	getPermissions: async ({ request, locals: { supabase, user }, params, fetch }) => {
		const formData = await request.formData();
		const fileId = formData.get('fileId')?.toString();

		if (!user?.id || !fileId) {
			return fail(400, { message: 'Missing required data' });
		}

		try {
			// Get the data source details
			const { data: dataSource, error: dataSourceError } = await supabase
				.from('data_sources')
				.select('secret_oauth, scope, site_id')
				.eq('id', params.sourceId)
				.eq('company_id', params.companyId)
				.single();

			if (dataSourceError || !dataSource) {
				console.error('Data source error:', {
					error: dataSourceError,
					sourceId: params.sourceId,
					companyId: params.companyId
				});
				throw error(401, 'Microsoft data source not found');
			}

			// Get refresh token from vault
			const refreshToken = await getSecretById(dataSource.secret_oauth);
			if (!refreshToken) {
				throw error(500, 'Missing refresh token');
			}

			// Exchange refresh token for access token
			const accessToken = await exchangeRefreshToken(refreshToken as string, dataSource.scope);

			// Fetch all members with access to the file
			const members = await fetchFileAccessMembers(accessToken, dataSource.site_id, fileId, fetch);

			return { success: true, permissions: members };
		} catch (error) {
			const message = error instanceof Error ? error.message : 'Unknown error occurred';
			return fail(500, { message: `Failed to fetch permissions: ${message}` });
		}
	}
};
