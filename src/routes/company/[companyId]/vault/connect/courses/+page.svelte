<script lang="ts">
	import { page } from '$app/stores';
	import { Button } from '$lib/components/ui/button';
	import * as Card from '$lib/components/ui/card';
	import * as Form from '$lib/components/ui/form';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { goto } from '$app/navigation';
	import { superForm } from 'sveltekit-superforms/client';
	import { toast } from 'svelte-sonner';

	export let data;

	const { form, errors, enhance, submitting } = superForm(data.form, {
		onSubmit: () => {
			toast.info('Connecting to Courses...', {
				id: 'courses-connect'
			});
		},
		onError: (err) => {
			toast.error(err.formErrors?.[0] || 'Failed to connect to Courses', {
				id: 'courses-connect'
			});
		},
		onResult: ({ result }) => {
			if (result.type === 'success') {
				toast.success('Connected to Courses!', {
					id: 'courses-connect'
				});
				goto(`/company/${$page.params.companyId}/vault`);
			}
		}
	});

	let isLoading = false;
</script>

<div class="container max-w-3xl mx-auto py-6 space-y-6">
	<div class="mb-8">
		<h1 class="text-3xl font-bold tracking-tight">Connect to Courses</h1>
		<p class="text-muted-foreground mt-2">Enter your course platform credentials</p>
	</div>

	<Card.Root>
		<Card.Header>
			<Card.Title>Connect Your Course Platform</Card.Title>
			<Card.Description>
				Enter your credentials to connect to your course platform account.
			</Card.Description>
		</Card.Header>
		<Card.Content>
			<form method="POST" use:enhance>
				<div class="space-y-4">
					<div class="space-y-2">
						<Label for="name">Account Name</Label>
						<Input
							id="name"
							name="name"
							placeholder="My Courses"
							bind:value={$form.name}
							error={$errors.name?.[0]}
							required
						/>
						{#if $errors.name}
							<p class="text-sm text-destructive">{$errors.name}</p>
						{/if}
					</div>

					<div class="space-y-2">
						<Label for="apiKey">API Key</Label>
						<Input
							id="apiKey"
							name="apiKey"
							type="password"
							placeholder="Enter your API key"
							bind:value={$form.apiKey}
							error={$errors.apiKey?.[0]}
							required
						/>
						{#if $errors.apiKey}
							<p class="text-sm text-destructive">{$errors.apiKey}</p>
						{/if}
					</div>

					<div class="space-y-2">
						<Label for="apiSecret">API Secret</Label>
						<Input
							id="apiSecret"
							name="apiSecret"
							type="password"
							placeholder="Enter your API secret"
							bind:value={$form.apiSecret}
							error={$errors.apiSecret?.[0]}
							required
						/>
						{#if $errors.apiSecret}
							<p class="text-sm text-destructive">{$errors.apiSecret}</p>
						{/if}
					</div>

					<div class="space-y-2">
						<Label for="domain">Domain (Optional)</Label>
						<Input
							id="domain"
							name="domain"
							type="text"
							placeholder="courses.example.com"
							bind:value={$form.domain}
							error={$errors.domain?.[0]}
						/>
						{#if $errors.domain}
							<p class="text-sm text-destructive">{$errors.domain}</p>
						{/if}
						<p class="text-xs text-muted-foreground">
							Specify your course platform domain if you have a custom domain.
						</p>
					</div>
				</div>

				<div class="flex justify-end space-x-4 mt-6">
					<Button
						type="button"
						variant="outline"
						on:click={() => goto(`/company/${$page.params.companyId}/vault`)}
						disabled={isLoading || $submitting}
					>
						Cancel
					</Button>
					<Button type="submit" disabled={isLoading || $submitting}>
						{#if $submitting}Connecting...{:else}Connect{/if}
					</Button>
				</div>
			</form>
		</Card.Content>
	</Card.Root>
</div>
