import { error, redirect } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';
import { z } from 'zod';
import { superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';

// Simple implementation of the missing functions
function encryptValue(value: string): string {
	// In a real implementation, this would encrypt the value
	console.log('Encrypting value');
	return value; // Return the original value for now
}

function saveCredentials({
	dataSourceId,
	credentials
}: {
	dataSourceId: string;
	credentials: Record<string, string | null>;
}): void {
	// In a real implementation, this would save the credentials
	console.log('Saving credentials for data source', dataSourceId, credentials);
}

const coursesSchema = z.object({
	name: z.string().min(1).max(100),
	apiKey: z.string().min(1),
	apiSecret: z.string().min(1),
	domain: z.string().optional()
});

export const load: PageServerLoad = async (event) => {
	const { params, locals } = event;
	const { companyId } = params;
	const { session } = locals;

	if (!session) {
		throw redirect(303, '/auth');
	}

	const userId = session.user.id;

	// Check if user has access to the company
	const { data: userCompany } = await locals.supabase
		.from('company_members')
		.select('*')
		.eq('company_id', companyId)
		.eq('user_id', userId)
		.single();

	if (!userCompany) {
		throw error(403, 'You do not have access to this company');
	}

	// Check if Course Creator product is enabled for the company
	const { data: courseCreatorProduct } = await locals.supabase
		.from('company_products')
		.select('*')
		.eq('company_id', companyId)
		.eq('product_id', '0d7b3945-0900-4096-bd8a-2b0e7d3f95ef') // Course Creator product ID
		.single();

	if (!courseCreatorProduct || !courseCreatorProduct.is_enabled) {
		// Redirect to company settings to enable the Course Creator product
		throw redirect(303, `/company/${companyId}/settings`);
	}

	// Check if Courses product is enabled for the company
	const { data: coursesProduct } = await locals.supabase
		.from('company_products')
		.select('*')
		.eq('company_id', companyId)
		.eq('product_id', 'fc1a72b2-2c33-4857-b387-c28902dc3fb8') // Courses product ID
		.single();

	if (!coursesProduct || !coursesProduct.is_enabled) {
		// Enable Courses product if Course Creator is enabled
		await locals.supabase.from('company_products').upsert({
			company_id: companyId,
			product_id: 'fc1a72b2-2c33-4857-b387-c28902dc3fb8',
			is_enabled: true
		});
	}

	const form = await superValidate(zod(coursesSchema));
	return { form };
};

export const actions: Actions = {
	default: async (event) => {
		const { request, params, locals } = event;
		const { companyId } = params;
		const { session } = locals;

		if (!session) {
			throw error(401, 'Unauthorized');
		}

		const form = await superValidate(request, zod(coursesSchema));
		if (!form.valid) {
			return { form };
		}

		try {
			// Encrypt sensitive data
			const encryptedApiKey = encryptValue(form.data.apiKey);
			const encryptedApiSecret = encryptValue(form.data.apiSecret);

			// Create a new data source
			const { data: newDataSource, error: insertError } = await locals.supabase
				.from('data_sources')
				.insert({
					company_id: companyId,
					platform: 'courses',
					name: form.data.name,
					external_id: form.data.domain || 'courses-default'
				})
				.select('id')
				.single();

			if (insertError || !newDataSource) {
				console.error('Error creating Courses data source:', insertError);
				throw error(500, 'Error creating Courses data source');
			}

			// Save credentials
			saveCredentials({
				dataSourceId: newDataSource.id,
				credentials: {
					api_key: encryptedApiKey,
					api_secret: encryptedApiSecret,
					domain: form.data.domain || null
				}
			});

			return { form };
		} catch (err) {
			console.error('Error connecting to Courses:', err);
			throw error(500, 'Error connecting to Courses');
		}
	}
};
