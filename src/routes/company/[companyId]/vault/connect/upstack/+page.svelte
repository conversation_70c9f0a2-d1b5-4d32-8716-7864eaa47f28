<script lang="ts">
	import { superForm } from 'sveltekit-superforms/client';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Textarea } from '$lib/components/ui/textarea';
	import { Loader2 } from 'lucide-svelte';
	import { Alert, AlertDescription } from '$lib/components/ui/alert';

	// Initialize the form with empty data
	const { form, errors, enhance, submitting, message } = superForm({
		entityName: '',
		entityDescription: ''
	});
</script>

<div class="container max-w-lg py-8 mx-auto">
	<div class="mb-8">
		<h1 class="text-4xl font-extrabold tracking-tight scroll-m-20 lg:text-5xl">
			Connect to Upstack
		</h1>
		<p class="mt-2 text-muted-foreground">
			Create a new Upstack entity to connect to your company vault.
		</p>
	</div>

	<form method="POST" use:enhance class="space-y-6">
		{#if $message}
			<Alert variant="destructive">
				<AlertDescription>
					{$message}
				</AlertDescription>
			</Alert>
		{/if}

		<div class="space-y-2">
			<Label for="entityName">Entity Name <span class="text-destructive">*</span></Label>
			<Input
				id="entityName"
				name="entityName"
				bind:value={$form.entityName}
				disabled={$submitting}
				aria-invalid={$errors.entityName ? 'true' : undefined}
				required
			/>
			{#if $errors.entityName}
				<p class="text-sm text-destructive">{$errors.entityName}</p>
			{/if}
		</div>

		<div class="space-y-2">
			<Label for="entityDescription">Description</Label>
			<Textarea
				id="entityDescription"
				name="entityDescription"
				bind:value={$form.entityDescription}
				disabled={$submitting}
				placeholder="Optional description of this entity"
				rows={3}
			/>
		</div>

		<div class="flex justify-between pt-4">
			<Button
				type="button"
				variant="outline"
				disabled={$submitting}
				on:click={() => (window.location.href = `/company/${$page.params.companyId}/vault`)}
			>
				Cancel
			</Button>
			<Button type="submit" disabled={$submitting}>
				{#if $submitting}
					<Loader2 class="w-4 h-4 mr-2 animate-spin" />
					Connecting...
				{/if}
				{#if !$submitting}
					Connect
				{/if}
			</Button>
		</div>
	</form>
</div>
