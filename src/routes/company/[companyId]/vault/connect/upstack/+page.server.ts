import { error, fail, redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import type { Actions } from './$types';
// import { getUpstackClient } from '$lib/server/upstack';
// import { env } from '$env/dynamic/private';

export const load: PageServerLoad = async ({ locals, params }) => {
	const { supabase, user } = locals;
	const { companyId } = params;

	if (!companyId) {
		throw error(400, 'Company ID is required');
	}

	if (!user) {
		throw error(401, 'Authentication required');
	}

	// Verify Upstack environment variables are set
	// if (!env.UPSTACK_CLIENT_ID || !env.UPSTACK_CLIENT_SECRET) {
	// 	console.error('Upstack API credentials not configured in environment variables');
	// 	throw error(500, 'Upstack API is not properly configured');
	// }

	// Check if the user has access to this company
	const { data: memberData, error: memberError } = await supabase
		.from('company_members')
		.select('id, role')
		.eq('company_id', companyId)
		.eq('user_id', user.id)
		.eq('is_active', true)
		.single();

	if (memberError || !memberData) {
		console.error('Error fetching company member:', memberError);
		throw error(403, 'You do not have access to this company');
	}

	// Check if the Upstack product is enabled for this company
	const { data: productData, error: productError } = await supabase
		.from('company_products')
		.select(
			`
      id,
      is_enabled,
      products (
        id,
        name
      )
    `
		)
		.eq('company_id', companyId)
		.eq('is_enabled', true);

	if (productError) {
		console.error('Error fetching company products:', productError);
		throw error(500, 'Failed to check if Upstack is enabled');
	}

	// Check if Upstack is enabled
	const upstackEnabled = productData?.some(
		(p) =>
			p.products &&
			typeof p.products === 'object' &&
			'name' in p.products &&
			p.is_enabled &&
			p.products.name === 'Upstack Integration'
	);

	if (!upstackEnabled) {
		// Redirect to company settings to enable Upstack
		return redirect(303, `/company/${companyId}/manage-company?tab=integrations&missing=upstack`);
	}

	return {
		companyId
	};
};

export const actions: Actions = {
	default: async ({ request, locals, params }) => {
		// const { supabase } = locals;
		// const { companyId } = params;

		// Extract form data
		const formData = await request.formData();
		const entityName = formData.get('entityName')?.toString();
		// const entityDescription = formData.get('entityDescription')?.toString() || '';

		// Validate required fields
		if (!entityName) {
			return fail(400, { error: 'Entity name is required' });
		}

		// try {
		// Create an Upstack client
		// const upstackClient = getUpstackClient();

		// Create an entity in Upstack
		// const entityResponse = await upstackClient.createEntity(entityName, entityDescription);

		// if (entityResponse.status !== 'success') {
		// 	console.error('Failed to create entity in Upstack:', entityResponse);
		// 	return fail(500, {
		// 		error: `Failed to create entity in Upstack: ${entityResponse.message || 'Unknown error'}`
		// 	});
		// }

		// Generate tokens for secure storage
		// const tokenResponse = await upstackClient.authenticate();

		// Store tokens securely using vault
		// const { data: secretIds, error: secretError } = await supabase.rpc('create_secrets_batch', {
		// 	secrets: [tokenResponse.access_token, tokenResponse.refresh_token]
		// });

		// if (secretError) {
		// 	console.error('Failed to store tokens securely:', secretError);
		// 	return fail(500, {
		// 		error: 'Failed to store authentication credentials'
		// 	});
		// }

		// Create a data source for this Upstack entity
		// const { error: dataSourceError } = await supabase
		// 	.from('data_sources')
		// 	.insert({
		// 		company_id: companyId,
		// 		platform_type: 'upstack',
		// 		display_name: entityName,
		// 		secret_oauth: secretIds[1], // refresh token
		// 		access_token: secretIds[0], // access token
		// 		metadata: {
		// 			entity_id: entityResponse.id,
		// 			entity_name: entityName,
		// 			entity_description: entityDescription
		// 		}
		// 	})
		// 	.select('id')
		// 	.single();

		// if (dataSourceError) {
		// 	console.error('Failed to create data source:', dataSourceError);
		// 	return fail(500, {
		// 		error: 'Failed to create data source'
		// 	});
		// }

		// Redirect to the vault page
		// 	throw redirect(303, `/company/${companyId}/vault`);
		// } catch (err) {
		// 	console.error('Error creating Upstack entity:', err);
		// 	return fail(500, {
		// 		error: err instanceof Error ? err.message : 'Failed to create Upstack entity'
		// 	});
		// }
	}
};
