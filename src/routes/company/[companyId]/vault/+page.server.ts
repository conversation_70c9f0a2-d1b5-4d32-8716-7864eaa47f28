import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { verifyUserRoleForUrl } from '$lib/utils/roleBasedAccess';

export const load: PageServerLoad = async ({ locals: { supabase, user }, params, parent }) => {
	const { companyId } = params;

	// Check user role permissions
	const hasAccess = await verifyUserRoleForUrl(user, companyId, 'vault', supabase);

	if (!hasAccess) {
		console.error(`User ${user?.id} does not have access to vault for company ${companyId}`);
		return redirect(
			303,
			`/company/${companyId}/access-denied?resource=vault&redirectUrl=/company/${companyId}/dashboard`
		);
	}

	const { data: dataSources, error: dataSourcesError } = await supabase
		.from('data_sources')
		.select('*')
		.eq('company_id', companyId);

	if (dataSourcesError) {
		console.error('Error fetching datasources:', dataSourcesError);
		console.error('Details:', JSON.stringify(dataSourcesError));
	}

	// Add additional info to each datasource
	const enhancedDataSources =
		dataSources?.map((source) => {
			return {
				...source,
				webUrl: source.web_url || null,
				// Map platform_type to type and display_name to name for component compatibility
				type: source.platform_type || 'unknown',
				name: source.display_name || 'Unnamed Source'
			};
		}) || [];

	// Fetch enabled storage products
	const { data: enabledStorageProducts, error: storageProductsError } = await supabase
		.from('company_products')
		.select(
			`
			id,
			is_enabled,
			products (
				id,
				name,
				description,
				category,
				features
			)
		`
		)
		.eq('company_id', companyId)
		.eq('is_enabled', true);

	if (storageProductsError) {
		console.error('Error fetching storage products:', storageProductsError);
	}

	// Format storage products for front-end
	const storageProviders =
		enabledStorageProducts
			?.filter(
				(item) =>
					item.products &&
					item.is_enabled &&
					typeof item.products === 'object' &&
					'category' in item.products &&
					item.products.category === 'Document Storage'
			)
			.map((item) => {
				// Define a more specific type
				interface ProductData {
					id: string;
					name: string;
					description: string;
					category: string;
					features: string[] | string;
				}
				// Cast to unknown first to avoid TypeScript error
				const productData = item.products as unknown;
				const product = productData as ProductData;
				return {
					id: product.id,
					name: product.name,
					description: product.description,
					features:
						typeof product.features === 'string' ? JSON.parse(product.features) : product.features
				};
			}) || [];

	// Fetch all products for the product selector
	const { data: allProducts, error: productsError } = await supabase
		.from('products')
		.select('*')
		.eq('category', 'Document Storage');

	if (productsError) {
		console.error('Error fetching products:', productsError);
	}

	// Fetch company products for mapping enabled status
	const { data: companyProducts, error: companyProductsError } = await supabase
		.from('company_products')
		.select('*')
		.eq('company_id', companyId);

	if (companyProductsError) {
		console.error('Error fetching company products:', companyProductsError);
	}

	// If a product is marked as coming soon, make sure it's not enabled in company_products
	// This ensures consistency in case database state is inconsistent
	const cleanedCompanyProducts =
		companyProducts?.map((cp) => {
			const product = allProducts?.find((p) => p.id === cp.product_id);
			if (product?.is_coming_soon && cp.is_enabled) {
				// Return a copy with is_enabled set to false
				return { ...cp, is_enabled: false };
			}
			return cp;
		}) || [];

	// Get parent data
	const parentData = await parent();

	// Debug log to check storageProviders
	console.log('Storage providers for vault page:', storageProviders);

	// Debug log to check data sources
	console.log('Data sources from database:', dataSources);
	console.log('Enhanced data sources for frontend:', enhancedDataSources);

	return {
		dataSources: enhancedDataSources,
		user: parentData.user,
		storageProviders,
		products: allProducts || [],
		companyProducts: cleanedCompanyProducts
	};
};
