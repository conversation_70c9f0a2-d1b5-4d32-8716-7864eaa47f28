<script lang="ts">
	/**
	 * @module VaultPage
	 * @description This module represents the Vault page of the application.
	 * It includes navigation and a file explorer to display SharePoint files.
	 */

	import { onDestroy } from 'svelte';
	import { invalidate } from '$app/navigation';
	import { Button } from '$lib/components/ui/button';
	import StorageProviderCard from '$lib/components/StorageProviderCard.svelte';
	import * as Dialog from '$lib/components/ui/dialog';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { companyStore } from '$lib/stores/companyStore';
	import { page } from '$app/stores';
	import AddDataSourceDropdown from '$lib/components/AddDataSourceDropdown.svelte';
	import ProductSelector from '$lib/components/ProductSelector.svelte';
	import { goto } from '$app/navigation';
	import { toast } from 'svelte-sonner';
	import { onMount } from 'svelte';
	import { afterNavigate } from '$app/navigation';
	import { HelpCircle, FileBox } from 'lucide-svelte';
	import * as Tooltip from '$lib/components/ui/tooltip';
	import DataSourceCard from '$lib/components/DataSourceCard.svelte';

	interface DataSource {
		id: string;
		type: string;
		name: string;
		drive_id?: string;
		created_at: string;
		updated_at: string;
		webUrl: string | null;
	}

	interface StorageProvider {
		id: string;
		name: string;
		description: string;
		features: string[];
	}

	export let data: {
		dataSources: DataSource[];
		user: any;
		storageProviders?: StorageProvider[];
		products?: any[];
		companyProducts?: any[];
	};

	let dataSources = data.dataSources;
	let storageProviders = data.storageProviders || [];
	let { user } = data;
	let showStorageProvidersModal = false;
	let showProductSelectorModal = false;

	// Update local variables when data changes
	$: {
		dataSources = data.dataSources;
		storageProviders = data.storageProviders || [];
	}

	// Subscribe to companyStore and invalidate the page when companyId changes
	const unsubscribe = companyStore.subscribe((companyId) => {
		if (companyId) {
			invalidate('supabase:auth');
		}
	});

	// This reactive statement is no longer needed as we have the reactive declarations above

	// Get companyId from page params - ensure it's a string
	$: companyId = $page.params.companyId || '';

	// Add function to handle data source deletion
	function handleDataSourceDelete(datasourceId: string) {
		dataSources = dataSources.filter((source) => source.id !== datasourceId);
	}

	// Handle connecting to a provider
	function handleConnectProvider(event: CustomEvent<{ provider: StorageProvider }>) {
		const provider = event.detail.provider;
		if (!companyId) return;

		// Check if this is SharePoint, which requires special handling
		if (provider.name.toLowerCase().includes('sharepoint')) {
			// Set the company ID in a cookie
			document.cookie = `companyId=${companyId}; path=/; max-age=3600; SameSite=Lax`;

			// Create and submit a form to handle the POST request
			const form = document.createElement('form');
			form.method = 'POST';
			form.action = '/api/link-sharepoint';
			document.body.appendChild(form);
			form.submit();
			return;
		}

		// For all other providers, use the default pattern
		const route = `/company/${companyId}/vault/connect/${provider.name.toLowerCase().replace(/\s+/g, '-')}`;
		goto(route);
	}

	// Handle product toggling
	async function handleProductToggled(event: CustomEvent<{ productId: string; enabled: boolean }>) {
		const productId = event.detail.productId;
		const enabled = event.detail.enabled;
		console.log('Product toggled:', { productId, enabled });

		// Find the product in the products array
		const product = data.products?.find((p) => p.id === productId);

		// If a storage product was enabled, update the UI immediately
		if (
			enabled &&
			product &&
			(product.category === 'Document Storage' || product.categories?.includes('Document Storage'))
		) {
			console.log('Storage provider enabled:', product.name);

			// Manually update the storageProviders array for immediate feedback
			const providerExists = storageProviders.some((p) => p.id === productId);
			if (!providerExists) {
				const newProvider = {
					id: product.id,
					name: product.name,
					description: product.description,
					features: Array.isArray(product.features) ? product.features : []
				};
				console.log('Adding new provider to dropdown:', newProvider);
				storageProviders = [...storageProviders, newProvider];
			}
		} else if (!enabled && product) {
			// If a product was disabled, remove it from the storageProviders array
			console.log('Product disabled, removing from dropdown if present');
			storageProviders = storageProviders.filter((p) => p.id !== productId);
		}

		// Force a reload of the page data
		try {
			// Invalidate multiple dependencies to ensure all data is refreshed
			await Promise.all([
				invalidate('supabase:auth'),
				invalidate('company:db'),
				invalidate('supabase:db:companies')
			]);

			// Force a refresh of the dropdown data without opening it
			// This is a more subtle approach that doesn't interfere with the UI
			console.log('Forcing dropdown data refresh without opening it');

			console.log('Data invalidated and UI updated');
		} catch (error) {
			console.error('Error refreshing data:', error);
		}
	}

	// Browse to data source files
	function browseDatasource(sourceId: string) {
		goto(`/company/${companyId}/vault/${sourceId}/root`);
	}

	// Delete data source
	async function deleteDataSource(source: DataSource) {
		if (
			!confirm(
				`Are you sure you want to remove "${source.name}" from your vault? This will not delete the data from its original source.`
			)
		) {
			return;
		}

		try {
			const response = await fetch(`/api/datasources/${source.id}`, {
				method: 'DELETE',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ userId: user.id })
			});

			if (!response.ok) {
				const data = await response.json();
				throw new Error(data.message || 'Failed to delete data source');
			}

			toast.success('Data source successfully removed');
			handleDataSourceDelete(source.id);
		} catch (err) {
			console.error('Error deleting data source:', err);
			toast.error('Failed to remove data source. Please try again.');
		}
	}

	// Refresh data after navigation to this page
	afterNavigate(() => {
		console.log('Navigated to vault page, refreshing data');
		invalidate('supabase:auth');
	});

	// Also refresh on mount
	onMount(() => {
		console.log('Vault page mounted, refreshing data');
		invalidate('supabase:auth');
	});

	onDestroy(() => {
		unsubscribe();
	});

	// Format date for better readability
	function formatDate(dateString: string): string {
		const date = new Date(dateString);
		return new Intl.DateTimeFormat('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		}).format(date);
	}
</script>

<div class="container mx-auto max-w-6xl px-4">
	<div class="flex items-center justify-between mb-8">
		<div class="flex items-center gap-2">
			<h1 class="text-3xl font-bold">Document Vault</h1>
		</div>

		<!-- Add Data Source Dropdown positioned on the right -->
		<AddDataSourceDropdown
			{storageProviders}
			companyId={companyId || ''}
			bind:showProductSelectorModal
			on:connectProvider={handleConnectProvider}
		>
			<svelte:fragment slot="product-selector">
				{#if data.products && data.companyProducts}
					<ProductSelector
						companyId={companyId || ''}
						products={data.products}
						companyProducts={data.companyProducts}
						category="Document Storage"
						compact={true}
						on:productToggled={handleProductToggled}
					/>
				{:else}
					<div class="text-center py-4">
						<p class="text-muted-foreground">Loading available products...</p>
					</div>
				{/if}
			</svelte:fragment>
		</AddDataSourceDropdown>
	</div>

	<!-- Linked Data Sources Section -->
	{#if dataSources?.length > 0}
		<div class="grid grid-cols-1 gap-6 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-2">
			{#each dataSources as source}
				<DataSourceCard
					{source}
					{user}
					on:datasourceDelete={({ detail }) => handleDataSourceDelete(detail.datasourceId)}
				/>
			{/each}
		</div>
	{:else}
		<div
			class="flex flex-col items-center justify-center p-10 border rounded-lg bg-muted/10 text-center"
		>
			{#if storageProviders.length === 0}
				<!-- No products enabled yet -->
				<div
					class="w-16 h-16 mb-4 rounded-full bg-muted/30 flex items-center justify-center text-muted-foreground"
				>
					<FileBox size={28} />
				</div>
				<h2 class="text-xl font-semibold mb-2">No Data Sources Linked</h2>
				<p class="text-muted-foreground max-w-md mb-6">
					Connect your document repositories to make all your files searchable and accessible in one
					place.
				</p>
				<Button
					variant="default"
					class="px-6"
					on:click={() => {
						showProductSelectorModal = true;
					}}
				>
					Add Your First Data Source
				</Button>
			{:else}
				<!-- Products enabled but no data sources linked yet -->
				<h2 class="text-xl font-semibold mb-4">Available Storage Providers</h2>
				<p class="text-muted-foreground max-w-md mb-6">
					You have enabled the following storage providers. Connect to one to start adding documents
					to your vault.
				</p>

				<!-- Display available storage providers in a grid -->
				<div class="grid grid-cols-1 gap-4 md:grid-cols-2 w-full max-w-3xl mb-6">
					{#each storageProviders as provider}
						<StorageProviderCard {provider} companyId={companyId || ''} />
					{/each}
				</div>

				<Button
					on:click={() => {
						showProductSelectorModal = true;
					}}
				>
					Enable More Providers
				</Button>
			{/if}
		</div>
	{/if}
</div>

<!-- Storage Providers Modal -->
<Dialog.Root bind:open={showStorageProvidersModal}>
	<Dialog.Content class="max-w-3xl">
		<Dialog.Header>
			<Dialog.Title>Available Storage Providers</Dialog.Title>
			<Dialog.Description>
				Connect any of these storage providers to access your documents in the vault.
			</Dialog.Description>
		</Dialog.Header>

		<div class="py-4">
			{#if storageProviders?.length > 0}
				<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
					{#each storageProviders as provider}
						<StorageProviderCard {provider} companyId={companyId || ''} />
					{/each}
				</div>
			{:else}
				<div
					class="flex flex-col items-center justify-center p-8 border rounded-lg bg-muted/10 text-center"
				>
					<h2 class="text-lg font-semibold mb-2">No Storage Providers Available</h2>
					<p class="text-sm text-muted-foreground mb-4">
						You need to enable document storage providers for your company before you can connect
						data sources.
					</p>
					<Button
						variant="default"
						on:click={() => {
							showStorageProvidersModal = false;
							setTimeout(() => {
								showProductSelectorModal = true;
							}, 300);
						}}
					>
						Enable Provider
					</Button>
				</div>
			{/if}
		</div>

		<Dialog.Footer>
			<Button variant="outline" on:click={() => (showStorageProvidersModal = false)}>Close</Button>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>
