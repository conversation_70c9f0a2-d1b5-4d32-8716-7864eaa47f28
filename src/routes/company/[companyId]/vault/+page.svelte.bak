<script lang="ts">
	/**
	 * @module VaultPage
	 * @description This module represents the Vault page of the application.
	 * It includes navigation and a file explorer to display SharePoint files.
	 */

	import { onDestroy } from 'svelte';
	import { invalidate } from '$app/navigation';
	import { Button } from '$lib/components/ui/button';
	import StorageProviderCard from '$lib/components/StorageProviderCard.svelte';
	import * as Dialog from '$lib/components/ui/dialog';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import CreateCompanyForm from '$lib/components/CreateCompanyForm.svelte';
	import { companyStore } from '$lib/stores/companyStore';
	import { page } from '$app/stores';
	import AddDataSourceDropdown from '$lib/components/AddDataSourceDropdown.svelte';
	import ProductSelector from '$lib/components/ProductSelector.svelte';
	import { goto } from '$app/navigation';
	import { toast } from 'svelte-sonner';
	import { onMount } from 'svelte';
	import { afterNavigate } from '$app/navigation';
	import { HelpCircle, FileBox, MoreVertical, ExternalLink, Trash2, FileSearch } from 'lucide-svelte';

	interface DataSource {
		id: string;
		type: string;
		name: string;
		drive_id?: string;
		created_at: string;
		updated_at: string;
		webUrl: string | null;
	}

	interface StorageProvider {
		id: string;
		name: string;
		description: string;
		features: string[];
	}

	export let data: {
		dataSources: DataSource[];
		user: any;
		storageProviders?: StorageProvider[];
		products?: any[];
		companyProducts?: any[];
	};

	let dataSources = data.dataSources;
	let storageProviders = data.storageProviders || [];
	let { user } = data;
	let showProductSelector = false;
	let showStorageProvidersModal = false;

	// Subscribe to companyStore and invalidate the page when companyId changes
	const unsubscribe = companyStore.subscribe((companyId) => {
		if (companyId) {
			invalidate('supabase:auth');
		}
	});

	// Add reactive statement to update dataSources when data changes
	$: ({ dataSources, storageProviders = [] } = data);

	// Get companyId from page params - ensure it's a string
	$: companyId = $page.params.companyId || '';

	// Add function to handle data source deletion
	function handleDataSourceDelete(datasourceId: string) {
		dataSources = dataSources.filter((source) => source.id !== datasourceId);
	}

	// Handle connecting to a provider
	function handleConnectProvider(event: CustomEvent<{ provider: string; route: string }>) {
		goto(event.detail.route);
	}

	// Handle product toggling
	function handleProductToggled(event: CustomEvent<{ productId: string; enabled: boolean }>) {
		// If a storage product was enabled, just refresh the data
		if (event.detail.enabled) {
			// Reload the page data to get updated storage providers
			invalidate('supabase:auth');
		}
	}

	// Direct browse function
	function browseDatasource(sourceId: string) {
		goto(`/company/${companyId}/vault/${sourceId}/root`);
	}

	// Delete data source
	async function deleteDataSource(source: DataSource) {
		if (!confirm(`Are you sure you want to remove "${source.name}" from your vault? This will not delete the data from its original source.`)) {
			return;
		}

		try {
			const response = await fetch(`/api/datasources/${source.id}`, {
				method: 'DELETE',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ userId: user.id })
			});

			if (!response.ok) {
				const data = await response.json();
				throw new Error(data.message || 'Failed to delete data source');
			}

			toast.success('Data source successfully removed');
			handleDataSourceDelete(source.id);
		} catch (err) {
			console.error('Error deleting data source:', err);
			toast.error('Failed to remove data source. Please try again.');
		}
	}

	// Refresh data after navigation to this page
	afterNavigate(() => {
		console.log('Navigated to vault page, refreshing data');
		invalidate('supabase:auth');
	});

	// Also refresh on mount
	onMount(() => {
		console.log('Vault page mounted, refreshing data');
		invalidate('supabase:auth');
	});

	onDestroy(() => {
		unsubscribe();
	});

	// Format date for better readability
	function formatDate(dateString: string): string {
		const date = new Date(dateString);
		return new Intl.DateTimeFormat('en-US', { 
			year: 'numeric', 
			month: 'short', 
			day: 'numeric' 
		}).format(date);
	}
</script>

<div class="container flex flex-col mx-auto max-w-6xl">
	<div class="flex items-center justify-between mb-8">
		<div class="flex items-center gap-2">
			<h1 class="text-3xl font-bold">Document Vault</h1>
			<button 
				class="text-muted-foreground hover:text-primary transition-colors flex items-center justify-center bg-muted/30 rounded-full w-6 h-6" 
				on:click={() => showStorageProvidersModal = true}
				aria-label="View available storage providers"
				title="View available storage providers"
			>
				<HelpCircle size={16} />
			</button>
		</div>
		
		<!-- Add Data Source Dropdown positioned on the right -->
		<AddDataSourceDropdown
			{storageProviders}
			companyId={companyId || ''}
			on:connectProvider={handleConnectProvider}
		>
			<svelte:fragment slot="product-selector">
				{#if data.products && data.companyProducts}
					<ProductSelector
						companyId={companyId || ''}
						products={data.products}
						companyProducts={data.companyProducts}
						category="Document Storage"
						compact={true}
						on:productToggled={handleProductToggled}
					/>
				{:else}
					<div class="text-center py-4">
						<p class="text-muted-foreground">Loading available products...</p>
					</div>
				{/if}
			</svelte:fragment>
		</AddDataSourceDropdown>
	</div>

	<!-- Linked Data Sources Section -->
	<div>
		{#if dataSources?.length > 0}
			<div class="grid w-full grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
				{#each dataSources as source}
					<div class="group relative bg-card border rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-200">
						<!-- Card header with colored accent -->
						<div class="h-2 bg-primary/80"></div>
						
						<div class="p-5">
							<!-- Title and icon -->
							<div class="flex items-center justify-between mb-4">
								<div class="flex items-center gap-3">
									<div class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary">
										<FileBox size={20} />
									</div>
									<div>
										<h2 class="text-xl font-semibold line-clamp-1">{source.name}</h2>
										<p class="text-muted-foreground text-sm">{source.type}</p>
									</div>
								</div>
								
								<!-- Options Menu -->
								<DropdownMenu.Root>
									<DropdownMenu.Trigger asChild let:builder>
										<Button variant="ghost" size="icon" class="h-8 w-8" builders={[builder]}>
											<MoreVertical class="h-5 w-5" />
											<span class="sr-only">Options</span>
										</Button>
									</DropdownMenu.Trigger>
									<DropdownMenu.Content align="end">
										<DropdownMenu.Label>Actions</DropdownMenu.Label>
										<DropdownMenu.Separator />
										<DropdownMenu.Item on:click={() => browseDatasource(source.id)}>
											<FileSearch class="w-4 h-4 mr-2" />
											Browse Files
										</DropdownMenu.Item>

										{#if source.webUrl}
											<DropdownMenu.Item on:click={() => source.webUrl && window.open(source.webUrl, '_blank')}>
												<ExternalLink class="w-4 h-4 mr-2" />
												Open in {source.type}
											</DropdownMenu.Item>
										{/if}

										<DropdownMenu.Separator />
										<DropdownMenu.Item
											class="text-destructive focus:text-destructive"
											on:click={() => deleteDataSource(source)}
										>
											<Trash2 class="w-4 h-4 mr-2" />
											Remove Connection
										</DropdownMenu.Item>
									</DropdownMenu.Content>
								</DropdownMenu.Root>
							</div>
							
							<!-- Metadata -->
							<div class="text-sm text-muted-foreground mb-5">
								<p>Connected on {formatDate(source.created_at)}</p>
								{#if source.updated_at && source.updated_at !== source.created_at}
									<p class="mt-1">Last synced: {formatDate(source.updated_at)}</p>
								{/if}
							</div>

							<!-- Action button -->
							<Button 
								variant="default" 
								class="w-full flex items-center justify-center gap-2 mt-auto"
								on:click={() => browseDatasource(source.id)}
							>
								<FileSearch class="w-4 h-4" />
								Browse Files
							</Button>
						</div>
					</div>
				{/each}
			</div>
		{:else}
			<div class="flex flex-col items-center justify-center p-10 border rounded-lg bg-muted/10 text-center">
				<div class="w-16 h-16 mb-4 rounded-full bg-muted/30 flex items-center justify-center text-muted-foreground">
					<FileBox size={28} />
				</div>
				<h2 class="text-xl font-semibold mb-2">No Data Sources Linked</h2>
				<p class="text-muted-foreground max-w-md mb-6">
					Connect your document repositories to make all your files searchable and accessible in one place.
				</p>
				<Button 
					variant="default" 
					class="px-6"
					on:click={() => document.querySelector('[aria-label="Add Data Source"]')?.click()}
				>
					Add Your First Data Source
				</Button>
			</div>
		{/if}
	</div>
</div>

<!-- Storage Providers Modal -->
<Dialog.Root bind:open={showStorageProvidersModal}>
	<Dialog.Content class="max-w-3xl">
		<Dialog.Header>
			<Dialog.Title>Available Storage Providers</Dialog.Title>
			<Dialog.Description>
				Connect any of these storage providers to access your documents in the vault.
			</Dialog.Description>
		</Dialog.Header>
		
		<div class="py-4">
			{#if storageProviders?.length > 0}
				<div class="grid w-full grid-cols-1 gap-4 md:grid-cols-2">
					{#each storageProviders as provider}
						<StorageProviderCard {provider} companyId={companyId || ''} />
					{/each}
				</div>
			{:else}
				<div class="flex flex-col items-center justify-center p-8 border rounded-lg bg-muted/10 text-center">
					<h2 class="text-lg font-semibold mb-2">No Storage Providers Available</h2>
					<p class="text-sm text-muted-foreground mb-4">
						You need to enable document storage providers for your company before you can connect data sources.
					</p>
					<Button
						variant="default"
						on:click={() => {
							showStorageProvidersModal = false;
							// Wait for modal to close before opening dropdown
							setTimeout(() => {
								const addButton = document.querySelector('[aria-label="Add Data Source"]');
								if (addButton) (addButton as HTMLElement).click();
							}, 300);
						}}
					>
						Add New Storage Provider
					</Button>
				</div>
			{/if}
		</div>
		
		<Dialog.Footer>
			<Button variant="outline" on:click={() => showStorageProvidersModal = false}>
				Close
			</Button>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>
