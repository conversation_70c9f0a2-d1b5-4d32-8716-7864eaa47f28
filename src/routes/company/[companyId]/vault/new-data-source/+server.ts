import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@sveltejs/kit";
import { error, json } from "@sveltejs/kit";

export const POST: RequestHandler = async ({ request, cookies, params }) => {
  try {
    const formData = await request.formData();
    const siteId = formData.get("siteId")?.toString();

    if (!siteId) {
      throw error(400, "Site ID is required");
    }

    // Get tokens from cookies
    const accessToken = cookies.get("ms_access_token");
    const refreshToken = cookies.get("ms_refresh_token");

    if (!accessToken || !refreshToken) {
      throw error(401, "Authentication tokens not found");
    }

    const tokens = {
      access_token: accessToken,
      refresh_token: refreshToken,
    };

    // Make the request to SharePoint
    const response = await fetch(
      `https://graph.microsoft.com/v1.0/sites/${siteId}`,
      {
        headers: {
          "Authorization": `Bearer ${tokens.access_token}`,
          "Accept": "application/json",
        },
      },
    );

    if (!response.ok) {
      const errorData = await response.json();
      console.error("SharePoint API error:", errorData);
      throw error(
        response.status,
        errorData.error?.message || "Failed to fetch site info",
      );
    }

    const siteInfo = await response.json();

    // If successful, store any necessary data in cookies
    cookies.set("current_site_id", siteId, {
      path: "/",
      httpOnly: true,
      secure: true,
      sameSite: "lax",
    });

    return json({
      success: true,
      site: siteInfo,
    });
  } catch (err) {
    console.error("Error in new data source endpoint:", err);
    throw error(
      err instanceof Error ? 500 : err.status || 500,
      err instanceof Error ? err.message : "Internal server error",
    );
  }
};
