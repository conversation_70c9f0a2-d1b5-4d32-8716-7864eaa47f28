// src/routes/company/[companyId]/vault/new-data-source/+page.server.ts
import { PRIVATE_AZURE_CLIENT_SECRET } from '$env/static/private';
import { exchangeCodeForTokens, sendWebhook } from '$lib/utils';
import type { PageServerLoad } from './$types';
import { error, fail, redirect } from '@sveltejs/kit';
import type { Actions } from './$types';
import { exchangeRefreshToken, sharepointScopes } from '$lib/server/microsoft';
import { upsertSecret } from '$lib/server/vault';

interface TokenData {
	access_token: string;
	refresh_token: string;
	id_token: string;
	scope: string;
	tid: string;
	oid: string;
}

interface SharePointSite {
	id: string;
	displayName: string;
	webUrl: string;
	siteCollection?: {
		hostname: string;
	};
}

interface SitesResponse {
	value: SharePointSite[];
}

const scope = sharepointScopes.join(' ');

export const load: PageServerLoad = async ({ url, locals, cookies, fetch, params }) => {
	console.log('[New Data Source] Starting page load');

	// Get tokens from cookies instead of URL
	const accessToken = cookies.get('ms_access_token');
	const refreshToken = cookies.get('ms_refresh_token');

	if (!refreshToken) {
		console.error('[New Data Source] No refresh token found in cookies');
		throw error(401, 'Authentication tokens not found');
	}

	exchangeRefreshToken(refreshToken, scope);

	try {
		// First get user details to verify token works
		console.log('[New Data Source] Fetching user details from Graph API');
		const userResponse = await fetch('https://graph.microsoft.com/v1.0/me', {
			headers: {
				Authorization: `Bearer ${accessToken}`,
				Accept: 'application/json',
				'Content-Type': 'application/json'
			}
		});

		if (!userResponse.ok) {
			console.error('[New Data Source] Failed to fetch user details:', {
				status: userResponse.status,
				statusText: userResponse.statusText
			});
			const errorText = await userResponse.text();
			console.error('[New Data Source] Error response:', errorText);
			throw error(userResponse.status, 'Failed to fetch user details');
		}

		const userData = await userResponse.json();
		console.log('[New Data Source] User data received:', {
			displayName: userData.displayName,
			email: userData.userPrincipalName
		});

		// Fetch SharePoint sites using multiple approaches
		console.log('[New Data Source] Fetching SharePoint sites');

		// First try getting my sites
		console.log('[New Data Source] Trying /me/followedSites approach');
		const mySitesResponse = await fetch(
			'https://graph.microsoft.com/v1.0/me/followedSites?$select=id,displayName,webUrl,siteCollection',
			{
				headers: {
					Authorization: `Bearer ${accessToken}`,
					Accept: 'application/json'
				}
			}
		);

		let data: SitesResponse = { value: [] };

		if (mySitesResponse.ok) {
			data = (await mySitesResponse.json()) as SitesResponse;
			console.log('[New Data Source] Followed sites found:', JSON.stringify(data, null, 2));
		}

		// If no sites found, try getting root sites
		if (!data.value?.length) {
			console.log('[New Data Source] No followed sites found, trying /sites/root');
			const rootResponse = await fetch('https://graph.microsoft.com/v1.0/sites/root', {
				headers: {
					Authorization: `Bearer ${accessToken}`,
					Accept: 'application/json'
				}
			});

			if (rootResponse.ok) {
				const rootSite = (await rootResponse.json()) as SharePointSite;
				console.log('[New Data Source] Root site found:', rootSite);

				// Get the SharePoint hostname from the root site
				const hostname = rootSite.siteCollection?.hostname;

				if (hostname) {
					console.log('[New Data Source] Found SharePoint hostname:', hostname);

					// Now try to get all sites using the hostname
					const allSitesResponse = await fetch(
						`https://graph.microsoft.com/v1.0/sites/${hostname}:/sites?$select=id,displayName,webUrl,siteCollection`,
						{
							headers: {
								Authorization: `Bearer ${accessToken}`,
								Accept: 'application/json',
								ConsistencyLevel: 'eventual'
							}
						}
					);

					if (allSitesResponse.ok) {
						const allSitesData = (await allSitesResponse.json()) as SitesResponse;
						console.log(
							'[New Data Source] All sites found:',
							JSON.stringify(allSitesData, null, 2)
						);
						data.value = [...data.value, rootSite, ...(allSitesData.value || [])];
					} else {
						// If we can't get all sites, at least include the root site
						data.value = [rootSite];
					}
				}
			}
		}

		// If still no sites found, try one last approach with search
		if (!data.value?.length) {
			console.log('[New Data Source] No sites found, trying search approach');
			const searchResponse = await fetch(
				'https://graph.microsoft.com/v1.0/sites?$search=*&$select=id,displayName,webUrl,siteCollection',
				{
					headers: {
						Authorization: `Bearer ${accessToken}`,
						Accept: 'application/json',
						ConsistencyLevel: 'eventual'
					}
				}
			);

			if (searchResponse.ok) {
				const searchData = (await searchResponse.json()) as SitesResponse;
				console.log('[New Data Source] Search results:', JSON.stringify(searchData, null, 2));
				if (searchData.value?.length) {
					data.value = searchData.value;
				}
			}
		}

		console.log('[New Data Source] Sites data received:', {
			totalSites: data.value?.length,
			siteNames: data.value?.map((s: any) => s.displayName)
		});

		// Transform sites data to match the format needed for the dropdown
		const sites = data.value.map((site: any) => ({
			value: site.id,
			label: site.displayName,
			disabled: false
		}));

		console.log('[New Data Source] Transformed sites data:', {
			totalSites: sites.length,
			firstSite: sites[0]
		});

		return {
			sites,
			companyId: params.companyId
		};
	} catch (err) {
		console.error('[New Data Source] Error in load function:', err);
		throw error(500, err instanceof Error ? err.message : 'Failed to fetch SharePoint sites');
	}
};

export const actions = {
	default: async ({ request, locals, params, cookies, fetch }) => {
		console.log('[New Data Source Action] Starting form action');
		const formData = await request.formData();
		console.log('[New Data Source Action] Form data received:', {
			siteId: formData.get('siteId'),
			companyId: params.companyId
		});

		const siteId = formData.get('siteId')?.toString();
		const companyId = params.companyId;

		// Get tokens from cookies
		const accessToken = cookies.get('ms_access_token');
		const refreshToken = cookies.get('ms_refresh_token');
		const idToken = cookies.get('ms_id_token');
		if (!siteId || !companyId || !accessToken || !refreshToken || !idToken) {
			console.error('[New Data Source Action] Missing required data:', {
				hasSiteId: !!siteId,
				hasCompanyId: !!companyId,
				hasAccessToken: !!accessToken,
				hasRefreshToken: !!refreshToken,
				hasIdToken: !!idToken
			});
			return fail(400, { message: 'Missing required data' });
		}

		try {
			// 1. Get site info
			console.log('[New Data Source Action] Fetching site info for site:', siteId);
			const siteResponse = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteId}`, {
				headers: {
					Authorization: `Bearer ${accessToken}`,
					Accept: 'application/json',
					'Content-Type': 'application/json'
				}
			});

			if (!siteResponse.ok) {
				console.error('[New Data Source Action] Failed to fetch site info:', {
					status: siteResponse.status,
					statusText: siteResponse.statusText
				});
				const errorText = await siteResponse.text();
				console.error('[New Data Source Action] Error response:', errorText);
				return fail(siteResponse.status, {
					message: 'Failed to fetch site info'
				});
			}

			const siteInfo = await siteResponse.json();
			console.log('[New Data Source Action] Site info received:', {
				displayName: siteInfo.displayName,
				id: siteInfo.id
			});

			// 2. Get site drive
			const driveResponse = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteId}/drive`, {
				headers: {
					Authorization: `Bearer ${accessToken}`,
					Accept: 'application/json',
					'Content-Type': 'application/json'
				}
			});

			if (!driveResponse.ok) {
				return fail(driveResponse.status, {
					message: 'Failed to fetch drive info'
				});
			}

			const driveInfo = await driveResponse.json();

			// 3. Get root folder
			const rootFolderResponse = await fetch(
				`https://graph.microsoft.com/v1.0/sites/${siteInfo.id}/drives/${driveInfo.id}/root/children`,
				{
					headers: {
						Authorization: `Bearer ${accessToken}`,
						Accept: 'application/json',
						'Content-Type': 'application/json'
					}
				}
			);

			if (!rootFolderResponse.ok) {
				return fail(rootFolderResponse.status, {
					message: 'Failed to fetch root folder'
				});
			}

			const rootFolder = await rootFolderResponse.json();

			// 4. First create the secrets in vault
			try {
				console.log('[New Data Source Action] Creating secrets in vault');
				const [refreshTokenId, idTokenId] = await Promise.all([
					upsertSecret(
						null, // No data source ID yet
						refreshToken // refresh token value
					),
					upsertSecret(
						null, // No data source ID yet
						idToken // id token value
					)
				]);

				// 5. Then create data source with the secret IDs
				const { data: dataSource, error: dataSourceError } = await locals.supabase
					.from('data_sources')
					.insert({
						company_id: companyId,
						display_name: siteInfo.displayName,
						platform_type: 'sharepoint',
						tenant_id: siteInfo.siteCollection.hostname.split('.')[0],
						scope: scope,
						site_id: siteId,
						drive_id: driveInfo.id,
						secret_oauth: refreshTokenId,
						id_token: idTokenId
					})
					.select()
					.single();

				if (dataSourceError) {
					console.error('[New Data Source Action] Failed to create data source:', dataSourceError);
					return fail(500, { message: 'Failed to create data source' });
				}

				// Get the company member ID for the current user
				const { data: companyMember, error: companyMemberError } = await locals.supabase
					.from('company_members')
					.select('id')
					.eq('company_id', companyId)
					.eq('user_id', locals?.user?.id)
					.single();

				if (companyMemberError) {
					console.error(
						'[New Data Source Action] Failed to get company member:',
						companyMemberError
					);
					// Don't fail the request, just log the error since the data source was created
					console.warn('Could not create user platform profile due to missing company member');
				} else {
					// Create user platform profile
					const { error: platformProfileError } = await locals.supabase
						.from('user_platform_profiles')
						.upsert({
							company_member_id: companyMember.id,
							platform_type: 'email', // Using email as placeholder until enum is extended
							platform_user_id: idTokenId,
							is_primary: true,
							metadata: {
								data_source_id: dataSource.id,
								platform_type: dataSource.type,
								display_name: dataSource.display_name,
								platform: dataSource.platform
							}
						});

					if (platformProfileError) {
						console.error(
							'[New Data Source Action] Failed to create platform profile:',
							platformProfileError
						);
						// Don't fail the request, just log the error since the data source was created
						console.warn('Could not create user platform profile');
					}
				}

				console.log('[New Data Source Action] Successfully created data source:', dataSource);

				// Just return the redirect
				return { status: 303, redirect: `/company/${companyId}/vault` };
			} catch (err) {
				console.error('[New Data Source Action] Error creating data source:', err);
				return fail(500, { message: 'Failed to create data source' });
			}
		} catch (err) {
			console.error('[New Data Source Action] Error processing site selection:', err);
			return fail(500, { message: 'Internal server error' });
		}
	}
} as Actions;
