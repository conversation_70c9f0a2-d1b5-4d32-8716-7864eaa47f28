<!-- src/routes/company/[companyId]/vault/new-data-source/+page.svelte -->

<script lang="ts">
	import { goto } from '$app/navigation';
	import type { PageData } from './$types';
	import Button from '$lib/components/ui/button/button.svelte';
	import * as Select from '$lib/components/ui/select';
	import * as AlertDialog from '$lib/components/ui/alert-dialog';
	import type { User } from '$lib/types/User';
	import { enhance } from '$app/forms';

	export let data: PageData;
	export let form;

	interface SharePointSite {
		value: string;
		label: string;
		disabled: boolean;
	}

	let { sites } = data;
	// Get companyId from the URL params in page data
	$: companyId = data.companyId;

	let selectedSite: SharePointSite | null = null;
	let isLoadingSiteSelection = false;
	let siteUsers: User[] = [];
	let error: string | null = null;

	$: selectedValue = selectedSite ? {
		value: selectedSite.value,
		label: selectedSite.label
	} : undefined;

	function handleSelectedChange(selected: any | undefined) {
		console.log('Selected:', selected);
		if (selected?.value) {
			selectedSite = sites.find((site: SharePointSite) => site.value === selected.value) || null;
			console.log('Selected site:', selectedSite);
			error = null;
		} else {
			selectedSite = null;
		}
	}

	// $: if (form?.success) {
	// 	siteUsers = [{
	// 		id: '1',
	// 		displayName: 'placeholder',
	// 		businessPhones: [],
	// 		givenName: null,
	// 		jobTitle: null,
	// 		mail: '<EMAIL>',
	// 		mobilePhone: null,
	// 		officeLocation: null,
	// 		preferredLanguage: null,
	// 		surname: null,
	// 		userPrincipalName: '<EMAIL>',
	// 		phoneNumber: null
	// 	}];
	// }
</script>

<main class="container max-w-3xl px-4 py-8 mx-auto">
	{#if error}
		<p class="mb-4 text-destructive">{error}</p>
	{:else if form?.error}
		<p class="mb-4 text-destructive">{form.error}</p>
	{:else if sites && sites.length > 0}
		<form 
			method="POST" 
			class="space-y-6" 
			use:enhance={({ cancel }) => {
				isLoadingSiteSelection = true;
				return async ({ result, update }) => {
					isLoadingSiteSelection = false;
					console.log('Form submission result:', result);
					if (result.type === 'success') {
						// Navigate to vault
						const redirectUrl = `/company/${companyId}/vault`;
						console.log('Redirecting to:', redirectUrl);
						await goto(redirectUrl);
					}
				};
			}}
		>
			<div class="space-y-2">
				<h2 class="text-2xl font-semibold tracking-tight">Select a SharePoint Site</h2>
				<p class="text-muted-foreground">Choose the SharePoint site you want to connect to your vault.</p>
			</div>

			<div class="space-y-4">
				<Select.Root selected={selectedValue} onSelectedChange={handleSelectedChange}>
					<Select.Trigger class="w-full">
						<Select.Value placeholder="Choose a site..." />
					</Select.Trigger>
					<Select.Content>
						{#each sites as site (site.value)}
							<Select.Item value={site.value} label={site.label}>
								{site.label}
							</Select.Item>
						{/each}
					</Select.Content>
				</Select.Root>

				<!-- Only need siteId in the form now -->
				<input type="hidden" name="siteId" value={selectedSite?.value || ''} />

				<Button 
					type="submit"
					disabled={!selectedSite || isLoadingSiteSelection}
					class="w-full sm:w-auto"
				>
					{#if isLoadingSiteSelection}
						<span class="flex items-center gap-2">
							Loading... <span class="animate-spin">⏳</span>
						</span>
					{:else}
						Confirm Selection
					{/if}
				</Button>
			</div>
		</form>
		
		{#if siteUsers && siteUsers.length > 0}
			<AlertDialog.Root>
				<AlertDialog.Content>
					<AlertDialog.Header>Your SharePoint Site Has Been Added</AlertDialog.Header>
					<AlertDialog.Description class="mt-2 text-muted-foreground">
						What would you like to do next?
					</AlertDialog.Description>
					<div class="space-y-4">
						<p class="text-sm text-muted-foreground">
							You may now activate users for your company or alternatively view your vault to start ingesting files.
						</p>
						
						<div class="flex flex-col gap-4 sm:flex-row">
							<Button 
								class="flex-1"
								variant="default"
								on:click={() => goto(`/company/${companyId}/users`)}
							>
								Activate Users
							</Button>
							<Button 
								class="flex-1"
								variant="outline"
								on:click={() => goto(`/company/${companyId}/vault`)}
							>
								Go to Vault
							</Button>
						</div>
					</div>
				</AlertDialog.Content>
			</AlertDialog.Root>
		{/if}
	{:else}
		<div class="py-8 text-center">
			<p class="text-muted-foreground">No SharePoint sites available.</p>
		</div>
	{/if}
</main>
