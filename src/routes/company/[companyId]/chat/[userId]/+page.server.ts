import { redirect } from "@sveltejs/kit";
import type { PageServerLoad } from "./$types";

export const load: PageServerLoad = async (
  { params, url, locals: { supabase } },
) => {
  const { userId } = params;

  // Check for existing email session
  const { data: existingSession, error: sessionError } = await supabase
    .from("user_sessions")
    .select("session_id")
    .eq("user_id", userId)
    .single();

  if (!existingSession) {
    // Create new session with a generated UUID
    const { data: newSession, error: insertError } = await supabase
      .from("user_sessions")
      .insert({
        user_id: userId,
        session_id: crypto.randomUUID(), // Generate UUID for session
      })
      .select()
      .single();

    if (insertError) {
      throw new Error(`Failed to create session: ${insertError.message}`);
    }

    // Preserve existing URL parameters
    const searchParams = new URLSearchParams(url.search);
    const queryString = searchParams.toString();
    const redirectUrl = `${url.pathname}/${newSession.session_id}${
      queryString ? `?${queryString}` : ""
    }`;

    throw redirect(303, redirectUrl);
  }

  // If session exists, redirect to the session chat
  throw redirect(303, `${url.pathname}/${existingSession.session_id}`);
};
