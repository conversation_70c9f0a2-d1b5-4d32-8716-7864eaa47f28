export interface Message {
  id: string;
  type: "question" | "answer";
  text: string;
  sources?: any[];
  relevantSource?: {
    metadata?: {
      file_name: string;
    };
    content: string;
    similarity?: number;
  } | null;
  chatId?: string;
  sessionId?: string;
}

export interface ChatResponse {
  text: string;
  sourceDocuments?: any[];
  chatId?: string;
  sessionId?: string;
}

export interface PageData {
  messages: Message[];
  sessionId: string;
  userId: string;
  companyId: string;
}
