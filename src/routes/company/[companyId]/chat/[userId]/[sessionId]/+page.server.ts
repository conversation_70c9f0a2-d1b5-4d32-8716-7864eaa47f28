import { error } from "@sveltejs/kit";
import type { PageServerLoad } from "./$types";
import type { Actions } from "./$types";
import type { ChatResponse, Message } from "./types";

export const load = (async ({ params, fetch, locals: { supabase } }) => {
  try {
    const { userId, companyId, sessionId } = params;

    if (!userId || !companyId || !sessionId) {
      console.error("Missing required parameters:", {
        userId,
        companyId,
        sessionId,
      });
      throw error(400, "Missing required parameters");
    }

    // Calculate timestamp for 3 days ago
    const twoDaysAgo = Date.now() - 1000 * 60 * 60 * 24 * 2;

    console.log("Fetching chat history for session:", sessionId);

    // Fetch from Flowise
    const response = await fetch(
      `https://flowise-029x.onrender.com/api/v1/chatmessage/bae8ce0b-7d58-4a2f-b346-97f4bb371ab2?sessionId=${sessionId}&startDate=${twoDaysAgo}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer HrKyNGWvcn1xiXwDBcBJBfuQZdezLZgkJcOEWcrwpt0=",
        },
      },
    );
    console.log("🔍 [GET] Chat history async response:", response);
    if (!response.ok) {
      console.error(
        "API response not OK:",
        response.status,
        response.statusText,
      );
      throw new Error(
        `API response not OK: ${response.status} ${response.statusText}`,
      );
    }

    const data = await response.json();
    console.log("🔍 [GET] Chat history response:", data);

    // if (!Array.isArray(data)) {
    //   console.error("Data is not an array:", data);
    //   return {
    //     messages: [],
    //     sessionId,
    //     companyId,
    //     userId,
    //   };
    // }

    // Transform Flowise messages into our app's format
    const messages: Message[] = data.map((item: any) => ({
      id: item.id || crypto.randomUUID(),
      type: item.role === "userMessage" ? "question" : "answer",
      text: item.content,
      sources: item.sourceDocuments || [],
      relevantSource: item.sourceDocuments?.[0] || null,
      chatId: item.chatId,
      sessionId: item.sessionId,
    }));

    // Store messages in Supabase for persistence
    for (const msg of messages) {
      await supabase.from("chat_messages").upsert({
        id: msg.id,
        content: msg.text,
        role: msg.type === "question" ? "user" : "assistant",
        session_id: sessionId,
        user_id: userId,
        company_id: companyId,
        source_documents: msg.sources,
        chat_id: msg.chatId,
      }, {
        onConflict: "id",
      });
    }

    console.log("Transformed messages:", messages);

    return {
      messages,
      sessionId,
      companyId,
      userId,
    };
  } catch (err) {
    console.error("Error loading chat history:", err);
    // Try to fetch from Supabase as fallback
    try {
      const { data: chatHistory } = await supabase
        .from("chat_messages")
        .select("*")
        .eq("session_id", params.sessionId)
        .order("created_at", { ascending: true });

      const messages: Message[] = chatHistory?.map((msg) => ({
        id: msg.id,
        type: msg.role === "user" ? "question" : "answer",
        text: msg.content,
        sources: msg.source_documents || [],
        relevantSource: msg.source_documents?.[0] || null,
        chatId: msg.chat_id,
        sessionId: msg.session_id,
      })) || [];

      return {
        messages,
        sessionId: params.sessionId || "",
        companyId: params.companyId || "",
        userId: params.userId || "",
      };
    } catch (fallbackErr) {
      console.error("Fallback also failed:", fallbackErr);
      throw error(500, "Failed to load chat history");
    }
  }
}) satisfies PageServerLoad;

export const actions = {
  sendMessage: async ({ request, params, locals: { supabase } }) => {
    const formData = await request.formData();
    const message = formData.get("message")?.toString();
    const { userId, companyId, sessionId } = params;

    console.log("📝 [Server] Received message request:", {
      message,
      userId,
      companyId,
      sessionId,
    });

    if (!message) {
      throw error(400, "Message is required");
    }

    try {
      // Store user message in Supabase
      console.log("💾 [Server] Storing user message in Supabase...");
      const { error: insertError } = await supabase
        .from("chat_messages")
        .insert({
          content: message,
          role: "user",
          session_id: sessionId,
          user_id: userId,
          company_id: companyId,
        });

      if (insertError) {
        console.error("❌ [Server] Error storing user message:", insertError);
        throw insertError;
      }
      console.log("✅ [Server] User message stored successfully");

      // Get user data for analytics
      console.log("🔍 [Server] Fetching user data...");
      const { data: userData, error: userError } = await supabase
        .from("company_members")
        .select(`
          *,
          user_contact_info (
            type,
            value,
            is_primary
          ),
          base_users!inner (
            id,
            email,
            display_name,
            avatar_url
          )
        `)
        .eq("user_id", userId)
        .eq("company_id", companyId)
        .single();

      if (userError) {
        console.error("❌ [Server] Error fetching user data:", userError);
        throw userError;
      }

      // Get company data for personalization
      console.log("🔍 [Server] Fetching company data...");
      const { data: companyData, error: companyError } = await supabase
        .from("companies")
        .select(`
          *,
          company_personalization (
            custom_prompt
          )
        `)
        .eq("id", companyId)
        .single();

      if (companyError) {
        console.error("❌ [Server] Error fetching company data:", companyError);
        throw companyError;
      }

      // Determine environment
      const environment = process.env.NODE_ENV === "production"
        ? "production"
        : "staging";

      // Prepare rich user and company information
      const userInfo = {
        id: userId,
        role: userData.role || "member",
        found: true,
        avatarUrl: userData.base_users?.avatar_url,
        displayName: userData.base_users?.display_name,
      };

      const companyInfo = {
        id: companyId,
        name: companyData.name,
        found: true,
        status: companyData.status || "trial",
        branding: {
          logo: companyData.logo_url || null,
          slug: companyData.slug || null,
          smallLogo: null,
        },
        createdAt: companyData.created_at,
      };

      // Send message to Flowise
      console.log("🚀 [Server] Sending message to Flowise...");
      const response = await fetch(
        "https://flowise-029x.onrender.com/api/v1/prediction/bae8ce0b-7d58-4a2f-b346-97f4bb371ab2",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization":
              "Bearer HrKyNGWvcn1xiXwDBcBJBfuQZdezLZgkJcOEWcrwpt0=",
          },
          body: JSON.stringify({
            question: message,
            overrideConfig: {
              analytics: {
                langFuse: {
                  userId: userId,
                  metadata: {
                    platform: "TOTM_BETA_DEMO", // This can be dynamically determined if needed
                    user: userInfo,
                    company: companyInfo,
                  },
                },
              },
              sessionId,
              vars: {
                environment,
                user: userInfo,
                companyData: companyInfo,
                customPrompt:
                  companyData.company_personalization?.custom_prompt || "",
              },
            },
          }),
        },
      );

      if (!response.ok) {
        console.error("❌ [Server] Flowise API error:", response.statusText);
        throw new Error(`Flowise API error: ${response.statusText}`);
      }

      const data: ChatResponse = await response.json();
      console.log("📨 [Server] Flowise response:", data);

      // Extract the first item from the array if it's an array
      const responseData = Array.isArray(data) ? data[0] : data;
      console.log("🔍 [Server] Extracted response data:", responseData);

      // Store bot response in Supabase
      console.log("💾 [Server] Storing bot response in Supabase...");
      const { error: botInsertError } = await supabase
        .from("chat_messages")
        .insert({
          content: responseData.text,
          role: "assistant",
          session_id: sessionId,
          user_id: userId,
          company_id: companyId,
          source_documents: responseData.sourceDocuments || [],
        });

      if (botInsertError) {
        console.error(
          "❌ [Server] Error storing bot response:",
          botInsertError,
        );
        throw botInsertError;
      }
      console.log("✅ [Server] Bot response stored successfully");

      const returnData = {
        success: true,
        message: {
          type: "answer",
          text: responseData.text,
          sources: responseData.sourceDocuments || [],
          relevantSource: responseData.sourceDocuments?.[0] || null,
          chatId: responseData.chatId,
          sessionId: responseData.sessionId,
        },
      };

      console.log("📤 [Server] Returning data to client:", returnData);
      return returnData;
    } catch (err) {
      console.error("❌ [Server] Error in sendMessage:", err);
      throw error(500, "Failed to send message");
    }
  },
};
