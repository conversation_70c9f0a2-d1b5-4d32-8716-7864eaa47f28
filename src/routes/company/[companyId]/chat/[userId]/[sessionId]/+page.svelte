<script lang="ts">
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';
	import { applyAction, enhance } from '$app/forms';
	import type { SubmitFunction } from '@sveltejs/kit';
	import Logo from '$lib/components/Logo.svelte';
	import SourcesPanel from '$lib/components/SourcesPanel.svelte';
	import ChatMessage from '$lib/components/ChatMessage.svelte';
	import ChatInput from '$lib/components/ChatInput.svelte';
	import { writable, type Writable } from 'svelte/store';
	import { ScrollArea } from "$lib/components/ui/scroll-area";

	interface Message {
		type: 'question' | 'answer';
		text: string;
		sources?: any[];
		relevantSource?: any;
		chatId?: string;
		sessionId?: string;
	}

	interface PageData {
		messages: Message[];
		sessionId: string;
		userId: string;
		companyId: string;
	}

	export let data: PageData;
	
	// Debug log the initial data
	console.log('Page Data:', {
		messagesCount: data.messages?.length,
		sessionId: data.sessionId,
		userId: data.userId,
		companyId: data.companyId
	});
	
	// Initialize messages store with data.messages
	const messages = writable<Message[]>(data.messages);
	
	// Update messages when data changes
	$: if (data.messages) {
		console.log('Updating messages store with count:', data.messages.length);
		messages.set(data.messages);
	}

	let showSourcesPanel = false;
	let selectedSources: any[] = [];
	let isLoading = false;
	let chatContainer: HTMLDivElement;
	let messagesEndRef: HTMLDivElement;
	let chatInput: ChatInput;

	function openSources(sources: any[]) {
		selectedSources = sources;
		showSourcesPanel = true;
	}

	function closeSourcesPanel() {
		showSourcesPanel = false;
		selectedSources = [];
	}

	function onVoiceInput() {
		// Implement voice input functionality
	}

	// Function to scroll to bottom
	function scrollToBottom() {
		if (messagesEndRef) {
			setTimeout(() => {
				messagesEndRef.scrollIntoView({ behavior: 'smooth', block: 'end' });
			}, 100);
		}
	}

	const handleFormSubmit: SubmitFunction = ({ formElement, formData, cancel }) => {
		const message = formData.get('message')?.toString();
		
		console.log('📝 [Client] Submitting message:', message);
		
		if (!message?.trim()) {
			console.log('⚠️ [Client] Empty message, cancelling submission');
			cancel();
			return;
		}

		console.log('✨ [Client] Adding user message to UI');
		messages.update(msgs => [...msgs, { type: 'question', text: message }]);
		isLoading = true;
		chatInput?.reset();
		scrollToBottom();

		return async ({ result }) => {
			console.log('📨 [Client] Received form submission result:', result);
			isLoading = false;
			
			if (result.type === 'success' && result.data) {
				try {
					console.log('🔍 [Client] Raw response data:', result.data);
					
					// The response is now a properly structured object
					const botMessage: Message = {
						type: 'answer',
						text: result.data.message.text,
						sources: result.data.message.sources || [],
						relevantSource: result.data.message.relevantSource,
						chatId: result.data.message.chatId,
						sessionId: result.data.message.sessionId
					};
					
					console.log('🤖 [Client] Constructed bot message:', botMessage);
					messages.update(msgs => [...msgs, botMessage]);
					
					await applyAction(result);
					scrollToBottom();
				} catch (error) {
					console.error('❌ [Client] Error parsing bot response:', error);
					messages.update(msgs => [...msgs, {
						type: 'answer',
						text: 'Sorry, there was an error processing the response.',
						sources: []
					}]);
				}
			} else {
				console.warn('⚠️ [Client] Unexpected result type or missing data:', result);
			}
		};
	};

	// Watch messages store for changes and scroll to bottom
	$: if ($messages?.length) {
		scrollToBottom();
	}

	onMount(() => {
		scrollToBottom();
		chatInput?.focus();
	});
</script>

<div class="relative flex flex-col overflow-hidden bg-gradient-to-b from-background via-background to-background">
	<main class="flex flex-col flex-1">
		<ScrollArea class="h-screen px-4 pb-20 pr-4">
			<div class="max-w-4xl mx-auto space-y-4">
				{#if $messages && $messages.length > 0}
					{#each $messages as message}
						<ChatMessage {message} onViewSources={openSources} />
					{/each}
				{:else}
					<div class="flex flex-col items-center justify-center h-full text-muted-foreground">
						<p>No messages yet. Start a conversation!</p>
					</div>
				{/if}
				<div class="h-20" bind:this={messagesEndRef} />
				</div>
		</ScrollArea>

	</main>
	<div class="fixed bottom-0 w-full max-w-4xl p-4 border-t bg-gradient-to-t from-background to-background/80">
		<div class="w-full max-w-4xl mx-auto">
			<form
				method="POST"
				action="?/sendMessage"
				use:enhance={handleFormSubmit}
				class="w-full"
			>
				<ChatInput bind:this={chatInput} {onVoiceInput} {isLoading} />
			</form>
		</div>
	</div>

	{#if showSourcesPanel}
		<SourcesPanel sources={selectedSources} onClose={closeSourcesPanel} />
	{/if}
</div>
