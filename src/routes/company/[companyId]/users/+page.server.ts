// src/routes/company/[companyId]/users/+page.server.ts

import { PRIVATE_AZURE_CLIENT_SECRET } from '$env/static/private';
import { PUBLIC_AZURE_CLIENT_ID } from '$env/static/public';
import { getSecretById } from '$lib/server/vault';
import {
	addUpstackUser,
	removeUpstackUser,
	handleCourseCreatorTagAddition,
	handleCourseCreatorTagRemoval
} from '$lib/server/upstack';
import {
	exchangeRefreshToken,
	getGraphUsers,
	getSiteUsers,
	matchUserByEmail,
	processGraphUser,
	sharepointScopes,
	type SharePointSiteUser
} from '$lib/server/microsoft';
import { error, fail, redirect } from '@sveltejs/kit';
import { supabaseAdmin } from '$lib/server/supabaseAdmin';
import { superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import type { Actions, PageServerLoad } from './$types';
import { z } from 'zod';
import type { Database } from '../../../../../database.types';
import { formSchema } from '$lib/components/EditContactDetails/schema';
import { baseUrl } from '$lib/utils';

type CompanyUser = Database['public']['Views']['company_user_profiles']['Row'];

const contactFormSchema = z.object({
	email: z.string().email('Please enter a valid email address'),
	phone: z
		.string()
		.regex(/^\d{11}$/, 'Phone number must be 11 digits (including country code, e.g. 44123456789)'),
	userId: z.string().uuid(),
	role: z.enum(['admin', 'expert', 'member'], {
		required_error: 'Please select a role'
	})
});

export const load: PageServerLoad = async ({
	request,
	params,
	locals: { supabase, user },
	parent,
	url
}) => {
	// Check if user is authenticated
	if (!user) {
		throw redirect(302, '/auth');
	}

	// Get parent data which includes the userRole
	const parentData = await parent();
	const userRole = parentData.userRole;

	// Check if user has appropriate permissions
	// Only 'admin' and 'expert' roles can access the users page
	if (userRole !== 'admin' && userRole !== 'expert') {
		console.error(`Access denied: Role '${userRole}' cannot access users page`, {
			userId: user.id,
			companyId: params.companyId,
			role: userRole
		});

		// Redirect to access-denied page instead of directly to company dashboard
		const redirectUrl =
			`/access-denied?` +
			new URLSearchParams({
				redirectUrl: `/company/${params.companyId}`,
				resourceType: 'user management page',
				requiredRole: 'admin or expert role',
				companyId: params.companyId
			}).toString();

		throw redirect(302, redirectUrl);
	}

	const companyId = params.companyId;
	let graphUsers: any[] = [];
	let sharePointSiteUsers: SharePointSiteUser[] = [];
	const graphUserMap = new Map<string, any>();

	// Create empty form with default values
	const form = await superValidate(zod(contactFormSchema));

	// Get all company users first
	const { data: companyUsers, error: companyUsersError } = await supabase
		.from('company_user_profiles')
		.select('*')
		.eq('company_id', companyId);

	if (companyUsersError) {
		console.error('Error fetching company users:', companyUsersError);
		return { companyUsers: [], needsConsent: false, form };
	}

	// Get all course creator tags for this company
	const { data: courseCreatorTags, error: tagsError } = await supabase
		.from('user_tags')
		.select('*')
		.eq('company_id', companyId)
		.eq('tag_name', 'course_creator');

	if (tagsError) {
		console.error('Error fetching course creator tags:', tagsError);
	}

	// Create a map of user IDs who are course creators
	const courseCreatorUserIds = new Set(courseCreatorTags?.map((tag) => tag.user_id) || []);

	// Get all user tags for this company
	const { data: allUserTags, error: allTagsError } = await supabase
		.from('user_tags')
		.select('*')
		.eq('company_id', companyId);

	if (allTagsError) {
		console.error('Error fetching all user tags:', allTagsError);
	}

	// Create a map of user IDs to their tags
	const userTagsMap = new Map();
	allUserTags?.forEach((tag) => {
		const existingTags = userTagsMap.get(tag.user_id) || [];
		existingTags.push(tag);
		userTagsMap.set(tag.user_id, existingTags);
	});

	// Get contact information for active users from user_platform_profiles
	const { data: contactInfo, error: contactError } = await supabase
		.from('user_platform_profiles')
		.select(
			`
      company_member_id,
      platform_type,
      platform_user_id,
      is_primary,
      created_at,
      company_members!inner(
        user_id,
        company_id
      )
    `
		)
		.in('platform_type', ['email', 'whatsapp'])
		.eq('is_primary', true)
		.eq('company_members.company_id', companyId);

	if (contactError) {
		console.error('Error fetching contact info:', contactError);
	}

	// Create a map of user contact information from platform profiles
	const userContactMap = new Map();

	// Sort contactInfo by created_at to ensure we use the latest entries
	const sortedContacts = contactInfo?.sort((a, b) => {
		return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
	});

	sortedContacts?.forEach((contact) => {
		const userId = contact.company_members.user_id;

		if (!userContactMap.has(userId)) {
			userContactMap.set(userId, {});
		}

		const currentData = userContactMap.get(userId);

		// Only update if we don't have this type yet (since we're processing newest first)
		if (contact.platform_type === 'email' && !currentData.email) {
			currentData.email = contact.platform_user_id;
		} else if (contact.platform_type === 'whatsapp' && !currentData.phone) {
			currentData.phone = contact.platform_user_id;
		}
	});

	// Get data sources to check for SharePoint integration
	const { data: dataSources, error: dataSourcesError } = await supabase
		.from('data_sources')
		.select('id, secret_oauth, site_id, drive_id, platform_type')
		.eq('company_id', companyId);

	if (!dataSourcesError && dataSources?.length) {
		try {
			// Get Microsoft Graph access token
			const refreshToken = await getSecretById(dataSources[0].secret_oauth);
			const accessToken = await exchangeRefreshToken(
				refreshToken as string,
				sharepointScopes.join(' ')
			);

			// Get users from Microsoft Graph API (organization-wide)
			graphUsers = await getGraphUsers(accessToken);

			// Process graph users into a map
			graphUsers.forEach((gUser) => {
				const graphData = processGraphUser(gUser);
				// Map both email and userPrincipalName to handle different login methods
				if (graphData.user_email) {
					graphUserMap.set(graphData.user_email.toLowerCase(), graphData);
					// Also map the userPrincipalName without the domain suffix
					const upnEmail = gUser.userPrincipalName?.split('@')[0]?.replace('_', '@');
					if (upnEmail && upnEmail !== graphData.user_email.toLowerCase()) {
						graphUserMap.set(upnEmail.toLowerCase(), graphData);
					}
				}
			});

			// NEW CODE: Get SharePoint site users for each data source that has a site_id
			for (const dataSource of dataSources) {
				if (dataSource.site_id) {
					try {
						const siteUsers = await getSiteUsers(accessToken, dataSource.site_id);

						// Add site users to our collection
						if (siteUsers.length > 0) {
							sharePointSiteUsers = [...sharePointSiteUsers, ...siteUsers];
						}
					} catch (siteError) {
						console.error(`DEBUG: Error fetching users for site ${dataSource.site_id}:`, siteError);
					}
				}
			}
		} catch (error) {
			console.error('Error fetching Graph users:', error);
			if (error instanceof Error && error.message.includes('invalid_grant')) {
				console.log('User needs to provide consent');
			}
		}
	}

	// Format company users with contact information, prioritizing platform profiles
	const formattedCompanyUsers =
		companyUsers?.map((user) => {
			const contactData = userContactMap.get(user.user_id) || {};
			// Use email from base_users (via company_user_profiles view) as primary source
			const userEmail = user.user_email?.toLowerCase();
			const graphData = userEmail ? graphUserMap.get(userEmail) : null;

			// Try alternate email formats if no direct match
			let alternateGraphData = null;
			if (!graphData && userEmail) {
				// Try with #EXT# suffix
				alternateGraphData = graphUserMap.get(`${userEmail}#ext#`);

				if (!alternateGraphData) {
					// Try matching by local part (before @)
					const localPart = userEmail.split('@')[0];
					const matchingGraphEmail = Array.from(graphUserMap.keys()).find(
						(email) => email.startsWith(localPart + '@') || email.startsWith(localPart + '_')
					);
					if (matchingGraphEmail) {
						alternateGraphData = graphUserMap.get(matchingGraphEmail);
					}
				}
			}

			const effectiveGraphData = graphData || alternateGraphData;

			return {
				user_name: user.raw_user_meta_data?.full_name || effectiveGraphData?.user_name || userEmail,
				user_email: userEmail, // Use email from base_users as primary source
				user_phone_number:
					user.user_phone_number || // Use phone from base_users first
					contactData.phone ||
					effectiveGraphData?.user_phone_number ||
					null,
				is_active: user.is_active,
				user_id: user.user_id,
				role: user.role || 'member',
				company_id: companyId,
				invite_id: user.invite_id,
				invite_status: user.invite_status,
				user_status: user.user_status,
				has_file_access:
					!!effectiveGraphData ||
					!!graphUsers.find(
						(gu) =>
							gu.mail?.toLowerCase() === userEmail ||
							gu.userPrincipalName?.toLowerCase().includes(userEmail)
					)
			};
		}) || [];

	// Add Graph-only users that aren't in company_user_profiles
	const existingEmails = new Set(formattedCompanyUsers.map((u) => u.user_email?.toLowerCase()));

	const graphOnlyUsers = graphUsers
		.filter((gu) => {
			const email = gu.mail?.toLowerCase();
			return email && !existingEmails.has(email);
		})
		.map((gu) => {
			const graphData = processGraphUser(gu);
			return {
				user_name: graphData.user_name,
				user_email: graphData.user_email,
				user_phone_number: graphData.user_phone_number,
				is_active: false, // Graph-only users are inactive until added to Supabase
				user_id: gu.id, // Using Graph user ID as temporary user_id
				role: 'member',
				company_id: companyId,
				invite_id: undefined,
				invite_status: null,
				user_status: 'inactive', // Set status to inactive since they're not in Supabase
				has_file_access: true // They still have SharePoint access
			};
		});

	// NEW CODE: Add SharePoint site users that aren't in either company_user_profiles or graph users
	const allExistingEmails = new Set(
		[
			...formattedCompanyUsers.map((u) => u.user_email?.toLowerCase()),
			...graphOnlyUsers.map((u) => u.user_email?.toLowerCase())
		].filter(Boolean)
	);

	const siteOnlyUsers = sharePointSiteUsers
		.filter((siteUser) => {
			// Include both person accounts and groups with email
			if (!siteUser.user_email) {
				return false;
			}

			// For person accounts, check if they already exist in other sources
			if (siteUser.is_person) {
				const exists = allExistingEmails.has(siteUser.user_email.toLowerCase());

				return !exists;
			}

			// Always include groups (they have different email format so won't cause duplicates)
			return true;
		})
		.map((siteUser) => {
			return {
				user_name: siteUser.user_name,
				user_email: siteUser.user_email,
				user_phone_number: siteUser.user_phone_number,
				is_active: false,
				user_id: siteUser.user_id, // Using site user ID as temporary user_id
				role: 'member',
				company_id: companyId,
				invite_id: undefined,
				invite_status: null,
				user_status: 'inactive', // Set status to inactive since they're not in Supabase
				has_file_access: true, // They have SharePoint site access
				source: siteUser.is_person ? 'sharepoint_site' : 'sharepoint_group', // Distinguish between person and group
				user_type: siteUser.user_type // Add the user type for better filtering and display
			};
		});

	// Combine all users from different sources
	const allUsers = [...formattedCompanyUsers, ...graphOnlyUsers, ...siteOnlyUsers];

	return {
		user,
		companyUsers: allUsers,
		needsConsent: false,
		form,
		companyId,
		courseCreatorUserIds: Array.from(courseCreatorUserIds),
		userTags: Object.fromEntries(userTagsMap) // Convert Map to plain object for serialization
	};
};

// Helper function to handle invite process
const handleInviteProcess = async (
	userEmail: string,
	companyId: string,
	invitedBy: string | undefined,
	supabase: any,
	existingInviteId?: string
): Promise<{ success: boolean; error?: string; warning?: string }> => {
	try {
		// Send invite email first to ensure it works
		const emailResult = await sendInviteEmail(userEmail, supabase);

		if (!emailResult.success) {
			console.error('Failed to send invite email:', emailResult.error);
			return {
				success: false,
				error: emailResult.error || 'Failed to send invite email'
			};
		}

		// If email sent successfully, update or create invite record
		const inviteData = {
			company_id: companyId,
			email: userEmail,
			invited_by: invitedBy,
			role: 'member',
			status: 'pending',
			expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
			updated_at: new Date()
		};

		const { error: inviteError } = existingInviteId
			? await supabase.from('company_invites').update(inviteData).eq('id', existingInviteId)
			: await supabase.from('company_invites').insert(inviteData);

		if (inviteError) {
			console.error('Error managing invite record:', inviteError);
			return {
				success: true,
				warning: 'Invite sent but there was an issue recording it in the database'
			};
		}

		return { success: true };
	} catch (error) {
		console.error('Error in handleInviteProcess:', error);
		return {
			success: false,
			error: error instanceof Error ? error.message : 'Failed to process invite'
		};
	}
};

// Helper function to send emails with consistent template
const sendEmailWithTemplate = async (
	email: string,
	type: 'invite' | 'login' | 'company_invite' | 'promotion',
	options: {
		redirectTo: string;
		companyName?: string;
		role?: string;
		isAuthRoute?: boolean;
	}
) => {
	const { redirectTo, companyName, role, isAuthRoute } = options;

	// Use signInWithOtp for all email types for consistency
	const { data, error } = await supabaseAdmin.auth.signInWithOtp({
		email,
		options: {
			emailRedirectTo: redirectTo,
			data: {
				type,
				companyName: companyName || 'your company',
				role,
				message: getEmailMessage(type, companyName, role, isAuthRoute)
			}
		}
	});

	if (error) {
		console.error(`Error sending ${type} email:`, error);
		return { success: false, error: error.message };
	}

	return { success: true, data };
};

// Helper function to get email message based on type
const getEmailMessage = (
	type: 'invite' | 'login' | 'company_invite' | 'promotion',
	companyName?: string,
	role?: string,
	isAuthRoute?: boolean
): string => {
	switch (type) {
		case 'login':
			return 'Click here to log in to your account';
		case 'company_invite':
			return `You have been invited to join ${companyName || 'a company'} on TOTM Search`;
		case 'promotion':
			return `You have been promoted to ${role} in ${companyName || 'your company'}`;
		case 'invite':
		default:
			return `You have been invited to join ${companyName || 'your company'}`;
	}
};

export const actions: Actions = {
	getTags: async ({ params, locals: { supabase } }) => {
		try {
			const { data: allUserTags, error: allTagsError } = await supabase
				.from('user_tags')
				.select('*')
				.eq('company_id', params.companyId);

			if (allTagsError) {
				console.error('Error fetching all user tags:', allTagsError);
				return fail(500, {
					type: 'error',
					error: allTagsError.message
				});
			}

			// Create a map of user IDs to their tags
			const userTagsMap = new Map();
			allUserTags?.forEach((tag) => {
				const existingTags = userTagsMap.get(tag.user_id) || [];
				existingTags.push(tag);
				userTagsMap.set(tag.user_id, existingTags);
			});

			return {
				type: 'success',
				data: Object.fromEntries(userTagsMap)
			};
		} catch (err) {
			console.error('Error in getTags action:', err);
			return fail(500, {
				type: 'error',
				error: 'An unexpected error occurred'
			});
		}
	},

	activateUser: async ({ request, locals: { supabaseAdmin, supabase } }) => {
		const formData = await request.formData();
		const userId = formData.get('userId') as string;
		const companyId = formData.get('companyId') as string;
		const userEmail = formData.get('userEmail') as string;

		console.log('🔄 Activate user request received:', {
			userId,
			companyId,
			userEmail,
			timestamp: new Date().toISOString()
		});

		if (!userId || !companyId || !userEmail) {
			console.error('❌ Missing required fields:', {
				userId,
				companyId,
				userEmail
			});
			return { success: false, error: 'Missing required fields' };
		}

		// Track operation statuses
		const status = {
			emailSent: false,
			userCreated: false,
			memberAdded: false,
			warnings: [] as string[]
		};

		try {
			// 1. Get company data first (we'll need this for both flows)
			console.log('📧 Fetching company data');
			const { data: companyData, error: companyDataError } = await supabase
				.from('companies')
				.select('name')
				.eq('id', companyId)
				.single();

			console.log('Company data response:', {
				data: companyData,
				error: companyDataError,
				timestamp: new Date().toISOString()
			});

			if (companyDataError) {
				console.warn('⚠️ Error fetching company data:', companyDataError);
				status.warnings.push('Could not fetch company name for email');
			}

			// 2. Check if user already exists
			console.log('🔍 Checking if user exists');
			const { data: existingUser, error: userCheckError } =
				await supabaseAdmin.auth.admin.getUserById(userId);

			console.log('User check response:', {
				data: existingUser,
				error: userCheckError,
				userId,
				timestamp: new Date().toISOString()
			});

			if (userCheckError) {
				console.log('User not found or error checking existence:', userCheckError);
				// For non-existent users, send an invitation
				console.log('🆕 New user, sending invitation');
				const emailResult = await sendEmailWithTemplate(userEmail, 'invite', {
					redirectTo: `${request.url.split('/company/')[0]}/company/${companyId}`,
					companyName: companyData?.name
				});

				if (!emailResult.success) {
					console.error('Failed to send invitation:', emailResult.error);
					status.warnings.push('Failed to send invitation to new user');
				} else {
					status.emailSent = true;
				}
			} else if (existingUser?.user) {
				console.log('✨ User exists, sending magic link');
				const emailResult = await sendEmailWithTemplate(userEmail, 'login', {
					redirectTo: `${baseUrl}/profile`,
					companyName: companyData?.name,
					isAuthRoute: true
				});

				if (!emailResult.success) {
					console.error('Failed to send magic link:', emailResult.error);
					status.warnings.push('Failed to send magic link to existing user');
				} else {
					status.emailSent = true;
				}
			} else {
				console.error('❌ Unexpected state checking user existence');
				status.warnings.push('Could not determine user status');
			}

			// 3. Check/Create user in base_users
			console.log('🔍 Checking if user exists in base_users');
			let { data: baseUser, error: baseUserError } = await supabaseAdmin
				.from('base_users')
				.select('id')
				.eq('email', userEmail)
				.single();

			console.log('Base user check response:', {
				data: baseUser,
				error: baseUserError,
				email: userEmail,
				timestamp: new Date().toISOString()
			});

			let effectiveUserId = baseUser?.id;

			if (!baseUser) {
				try {
					console.log('➕ Creating new user in auth system');
					const { data: userData, error: userError } = await supabaseAdmin.auth.admin.createUser({
						email: userEmail as string,
						email_confirm: true
					});

					console.log('User creation response:', {
						data: userData,
						error: userError,
						email: userEmail,
						timestamp: new Date().toISOString()
					});

					if (userError) {
						console.error('❌ Error creating user:', userError);
						status.warnings.push('Failed to create user account');
					} else {
						const { data: newBaseUser, error: newBaseUserError } = await supabaseAdmin
							.from('base_users')
							.select('id')
							.eq('email', userEmail)
							.single();

						console.log('New base user fetch response:', {
							data: newBaseUser,
							error: newBaseUserError,
							email: userEmail,
							timestamp: new Date().toISOString()
						});

						if (newBaseUserError) {
							console.error('❌ Error fetching new base user:', newBaseUserError);
							status.warnings.push('User created but could not fetch details');
						} else {
							effectiveUserId = newBaseUser.id;
							status.userCreated = true;
						}
					}
				} catch (createError) {
					console.error('❌ Error in user creation process:', createError);
					status.warnings.push('Failed to create user account due to unexpected error');
				}
			} else {
				status.userCreated = true;
			}

			// 4. Add user to company_members if we have a valid user ID
			if (effectiveUserId) {
				try {
					console.log('➕ Adding user to company_members with data:', {
						companyId,
						userId: effectiveUserId,
						timestamp: new Date().toISOString()
					});

					const { data: memberData, error: memberError } = await supabase
						.from('company_members')
						.upsert(
							{
								company_id: companyId,
								user_id: effectiveUserId,
								role: 'member',
								is_active: true
							},
							{
								onConflict: 'user_id,company_id',
								ignoreDuplicates: false
							}
						)
						.select();

					console.log('Company member upsert response:', {
						data: memberData,
						error: memberError,
						timestamp: new Date().toISOString()
					});

					if (memberError) {
						console.error('❌ Error adding to company_members:', memberError);
						status.warnings.push('Failed to add user to company');
					} else {
						status.memberAdded = true;

						// 5. Add user's email to user_platform_profiles
						if (memberData && memberData.length > 0) {
							try {
								console.log('➕ Adding user email to user_platform_profiles');
								const companyMemberId = memberData[0].id;

								// Add email to user_platform_profiles
								const { error: profileError } = await supabase
									.from('user_platform_profiles')
									.upsert(
										{
											company_member_id: companyMemberId,
											platform_type: 'sharepoint' as Database['public']['Enums']['platform_type'],
											platform_user_id: userEmail as string,
											is_primary: true,
											metadata: {
												type: 'email',
												service: 'microsoft'
											}
										},
										{
											onConflict: 'company_member_id,platform_type,platform_user_id',
											ignoreDuplicates: false
										}
									);

								if (profileError) {
									console.error(
										'❌ Error adding sharepoint profile to user_platform_profiles:',
										profileError
									);
									status.warnings.push(
										'User activated but failed to store SharePoint profile information'
									);
								} else {
									console.log('✅ Successfully added sharepoint profile to user_platform_profiles');
								}

								// If phone number is available in user metadata, add it too
								const { data: userData, error: userDataError } = await supabase
									.from('base_users')
									.select('phone_number, raw_user_meta_data')
									.eq('id', effectiveUserId)
									.single();

								if (!userDataError && userData) {
									// Check for phone in base_users or metadata
									const phoneNumber =
										userData.phone_number ||
										(userData.raw_user_meta_data && userData.raw_user_meta_data.phone_number);

									if (phoneNumber) {
										console.log('➕ Adding additional contact info to user_platform_profiles');
										const { error: phoneError } = await supabase
											.from('user_platform_profiles')
											.upsert(
												{
													company_member_id: companyMemberId,
													platform_type: 'whatsapp' as Database['public']['Enums']['platform_type'],
													platform_user_id: phoneNumber,
													is_primary: true,
													metadata: { type: 'phone' }
												},
												{
													onConflict: 'company_member_id,platform_type,platform_user_id',
													ignoreDuplicates: false
												}
											);

										if (phoneError) {
											console.error(
												'❌ Error adding whatsapp profile to user_platform_profiles:',
												phoneError
											);
											status.warnings.push('Failed to store WhatsApp profile information');
										} else {
											console.log(
												'✅ Successfully added whatsapp profile to user_platform_profiles'
											);
										}
									}
								}
							} catch (profileError) {
								console.error('❌ Error in contact information addition:', profileError);
								status.warnings.push('Failed to store contact information');
							}
						}
					}
				} catch (memberError) {
					console.error('❌ Error in company member addition:', memberError);
					status.warnings.push('Failed to add user to company due to unexpected error');
				}
			}

			// Return appropriate response based on operation statuses
			if (!status.emailSent && !status.userCreated && !status.memberAdded) {
				return {
					success: false,
					status: 'failed',
					details: {
						emailSent: false,
						userCreated: false,
						memberAdded: false
					},
					warnings: status.warnings
				};
			}

			return {
				success: true,
				status:
					status.emailSent && status.userCreated && status.memberAdded ? 'complete' : 'partial',
				details: {
					emailSent: status.emailSent,
					userCreated: status.userCreated,
					memberAdded: status.memberAdded
				},
				warnings: status.warnings.length > 0 ? status.warnings : undefined
			};
		} catch (error) {
			console.error('❌ Unexpected error in activateUser:', error);
			return {
				success: false,
				status: 'error',
				details: {
					emailSent: status.emailSent,
					userCreated: status.userCreated,
					memberAdded: status.memberAdded
				},
				error: 'An unexpected error occurred',
				warnings: status.warnings
			};
		}
	},

	deactivateUser: async ({ request, locals: { supabase, user } }) => {
		const formData = await request.formData();
		const userId = formData.get('userId') as string;
		const companyId = formData.get('companyId') as string;

		console.log('Deactivate user request received:', {
			userId,
			companyId,
			requestingUserId: user?.id
		});

		if (!userId || !companyId) {
			console.error('Missing required fields:', { userId, companyId });
			return { success: false, error: 'Missing required fields' };
		}

		// Verify the current user has admin privileges
		console.log('Verifying admin privileges');
		const { data: currentUserProfile, error: profileError } = await supabase
			.from('company_members')
			.select('role')
			.eq('company_id', companyId)
			.eq('user_id', user?.id)
			.single();

		if (profileError) {
			console.error('Error fetching current user profile:', profileError);
			return { success: false, error: 'Failed to verify admin privileges' };
		}

		console.log('Current user profile:', currentUserProfile);

		if (!currentUserProfile || currentUserProfile.role !== 'admin') {
			console.error('User lacks admin privileges:', {
				profile: currentUserProfile,
				userId: user?.id
			});
			return { success: false, error: 'Only admins can deactivate users' };
		}

		try {
			console.log('Attempting to deactivate user in company_members');
			// Update the company_members record to set is_active to false
			const { data: deactivateData, error: deactivateError } = await supabase
				.from('company_members')
				.update({ is_active: false })
				.eq('company_id', companyId)
				.eq('user_id', userId)
				.select();

			if (deactivateError) {
				console.error('Error deactivating user:', deactivateError);
				return { type: 'error', error: deactivateError.message };
			}

			console.log('Successfully deactivated user');
			console.log('Deactivate data:', deactivateData);

			// Verify the update was successful
			const { data: verifyData, error: verifyError } = await supabase
				.from('company_members')
				.select('is_active')
				.eq('company_id', companyId)
				.eq('user_id', userId)
				.single();

			if (verifyError) {
				console.error('Error verifying deactivation:', verifyError);
				return { type: 'error', error: 'Failed to verify deactivation' };
			}

			console.log('Verification result:', verifyData);
			return { type: 'success', data: deactivateData };
		} catch (error) {
			console.error('Error in deactivateUser:', error);
			return {
				type: 'error',
				error: error instanceof Error ? error.message : 'Failed to deactivate user'
			};
		}
	},

	// inviteUser: async ({ request, locals: { supabase, user } }) => {
	// 	const formData = await request.formData();
	// 	const userId = formData.get('userId') as string;
	// 	const companyId = formData.get('companyId') as string;
	// 	const userEmail = formData.get('userEmail') as string;

	// 	if (!userEmail || !companyId) {
	// 		return { success: false, error: 'Missing required fields' };
	// 	}

	// 	// Handle the invite process
	// 	return await handleInviteProcess(userEmail, companyId, user?.id, supabase);
	// },

	resendInvite: async ({ request, locals: { supabase, user } }) => {
		const formData = await request.formData();
		const inviteId = formData.get('inviteId') as string;
		const userEmail = formData.get('userEmail') as string;
		const companyId = formData.get('companyId') as string;

		if (!userEmail || !inviteId || !companyId) {
			return { type: 'error', error: 'Missing required fields' };
		}

		try {
			// Get company data for the email
			const { data: companyData, error: companyError } = await supabase
				.from('companies')
				.select('name')
				.eq('id', companyId)
				.single();

			if (companyError) {
				console.error('Error fetching company data:', companyError);
				return { type: 'error', error: 'Could not fetch company data' };
			}

			const isAuthRoute = request.url.includes('/auth/');
			// Send the OTP using signInWithOtp for better reliability
			const emailResult = await sendEmailWithTemplate(
				userEmail,
				isAuthRoute ? 'login' : 'company_invite',
				{
					redirectTo: isAuthRoute ? `${baseUrl}/profile` : `${baseUrl}/company/${companyId}`,
					companyName: companyData?.name,
					isAuthRoute
				}
			);

			if (!emailResult.success) {
				// If OTP fails, try resending the signup email
				const { error: resendError } = await supabase.auth.resend({
					type: 'signup',
					email: userEmail,
					options: {
						emailRedirectTo: isAuthRoute ? `${baseUrl}/profile` : `${baseUrl}/company/${companyId}`,
						data: {
							type: isAuthRoute ? 'login' : 'company_invite',
							companyName: companyData?.name || 'your company',
							message: getEmailMessage(
								isAuthRoute ? 'login' : 'company_invite',
								companyData?.name,
								undefined,
								isAuthRoute
							)
						}
					}
				});

				if (resendError) {
					console.error('Error resending signup email:', resendError);
					return { type: 'error', error: resendError.message };
				}
			}

			// Update the invite record
			const { error: updateError } = await supabase
				.from('company_invites')
				.update({
					status: 'pending',
					expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
					updated_at: new Date()
				})
				.eq('id', inviteId);

			if (updateError) {
				console.warn('Warning: Could not update invite record:', updateError);
				// Don't fail the request, just return success with a warning
				return {
					type: 'success',
					data: emailResult.data,
					warning: 'Invite sent but could not update invite record'
				};
			}

			return {
				type: 'success',
				data: emailResult.data
			};
		} catch (error) {
			console.error('Error in resendInvite:', error);
			return {
				type: 'error',
				error: error instanceof Error ? error.message : 'An unexpected error occurred'
			};
		}
	},

	promoteUser: async ({ request, locals: { supabase, user } }) => {
		const formData = await request.formData();
		const userId = formData.get('userId') as string;
		const companyId = formData.get('companyId') as string;
		const role = formData.get('role') as string;

		console.log('Promotion request received:', {
			userId,
			companyId,
			role,
			requestingUserId: user?.id
		});

		if (!userId || !companyId || !role) {
			console.error('Missing required fields:', { userId, companyId, role });
			return { success: false, error: 'Missing required fields' };
		}

		// Verify the current user has admin privileges
		console.log('Verifying admin privileges');
		const { data: currentUserProfile, error: profileError } = await supabase
			.from('company_user_profiles')
			.select('role')
			.eq('company_id', companyId)
			.eq('user_id', user?.id)
			.single();

		if (profileError) {
			console.error('Error fetching current user profile:', profileError);
			return { success: false, error: 'Failed to verify admin privileges' };
		}

		console.log('Current user profile:', currentUserProfile);

		if (!currentUserProfile || currentUserProfile.role !== 'admin') {
			console.error('User lacks admin privileges:', {
				profile: currentUserProfile,
				userId: user?.id
			});
			return { success: false, error: 'Only admins can promote users' };
		}

		try {
			// Get user's email and company info
			console.log('Fetching user data');
			const { data: userData, error: userError } = await supabase
				.from('company_user_profiles')
				.select('email:user_email, company_name')
				.eq('user_id', userId)
				.eq('company_id', companyId)
				.single();

			if (userError || !userData?.email) {
				console.error('Error fetching user data:', userError);
				throw new Error('Could not find user email');
			}

			console.log('Found user data:', userData);

			// Update the role
			console.log('Updating user role');
			const { error } = await supabase
				.from('company_members')
				.update({ role })
				.eq('company_id', companyId)
				.eq('user_id', userId);

			if (error) {
				console.error('Error updating role:', error);
				throw error;
			}

			console.log('Role updated successfully');

			// Send confirmation email using consistent template
			const emailResult = await sendEmailWithTemplate(userData.email, 'promotion', {
				redirectTo: `${request.url.split('/company/')[0]}/company/${companyId}`,
				companyName: userData.company_name,
				role
			});

			if (!emailResult.success) {
				console.error('Failed to send promotion email:', emailResult.error);
				return {
					success: true,
					warning: 'Role updated but failed to send confirmation email'
				};
			}

			return { success: true };
		} catch (error) {
			console.error('❌ Error promoting user:', error);
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Failed to promote user'
			};
		}
	},

	updateContactInfo: async (event) => {
		console.log('Starting updateContactInfo action');
		const {
			locals: { supabase },
			params: { companyId }
		} = event;

		try {
			const form = await superValidate(event, zod(formSchema));
			console.log('Form validation result:', form);

			if (!form.valid) {
				console.log('Form validation failed:', form.errors);
				return fail(400, { form });
			}

			const { userId, email, phone, role } = form.data;
			console.log('Updating contact info for user:', {
				userId,
				email,
				phone,
				role,
				companyId
			});

			// First update the contact info
			const { data: contactData, error: contactError } = await supabase.rpc('update_contact_info', {
				p_user_id: userId,
				p_company_id: companyId,
				p_email: email,
				p_phone: phone
			});

			if (contactError) {
				console.error('Error updating contact info:', contactError);
				return fail(500, {
					form,
					error: contactError.message || 'Failed to update contact information'
				});
			}

			// Then update the role in company_members
			const { error: roleError } = await supabase
				.from('company_members')
				.update({ role })
				.eq('company_id', companyId)
				.eq('user_id', userId);

			if (roleError) {
				console.error('Error updating role:', roleError);
				return fail(500, {
					form,
					error: roleError.message || 'Failed to update role'
				});
			}

			// If the role was changed to admin, send a promotion email
			const { data: previousRole } = await supabase
				.from('company_members')
				.select('role')
				.eq('company_id', companyId)
				.eq('user_id', userId)
				.single();

			if (previousRole?.role !== role && role === 'admin') {
				// Get company name for the email
				const { data: company } = await supabase
					.from('companies')
					.select('name')
					.eq('id', companyId)
					.single();

				if (company) {
					const { error: emailError } = await supabase.auth.admin.generateLink({
						type: 'magiclink',
						email,
						options: {
							data: {
								type: 'promotion',
								role: 'admin',
								companyName: company.name,
								message: `You have been promoted to admin in ${company.name}`
							}
						}
					});

					if (emailError) {
						console.error('Error sending promotion email:', emailError);
						// Don't fail the request, just log the error
					}
				}
			}

			console.log('Successfully updated contact info and role');
			return {
				form,
				success: true
			};
		} catch (error) {
			console.error('Unexpected error in updateContactInfo:', error);
			return fail(500, {
				form: null,
				error: 'An unexpected error occurred'
			});
		}
	},

	addCourseCreatorTag: async ({ request, locals: { supabase } }) => {
		const formData = await request.formData();
		const userId = formData.get('userId') as string;
		const companyId = formData.get('companyId') as string;
		const userName = formData.get('userName') as string;
		const userEmail = formData.get('userEmail') as string;

		if (!userId || !companyId || !userName || !userEmail) {
			return fail(400, {
				success: false,
				error: 'Missing required fields'
			});
		}

		const result = await handleCourseCreatorTagAddition(
			supabase,
			userId,
			companyId,
			userName,
			userEmail
		);

		if (!result.success) {
			return fail(result.error?.includes('DB Error:') ? 500 : 400, {
				success: false,
				error: result.error || 'Failed to add course creator tag'
			});
		}

		return {
			success: true,
			message: result.message,
			data: result.data
		};
	},

	removeCourseCreatorTag: async ({ request, locals: { supabase } }) => {
		const formData = await request.formData();
		const tagId = formData.get('tagId') as string;
		const userId = formData.get('userId') as string;
		const userEmail = formData.get('userEmail') as string;

		if (!tagId || !userId || !userEmail) {
			return fail(400, {
				success: false,
				error: 'Missing required fields'
			});
		}

		const result = await handleCourseCreatorTagRemoval(supabase, tagId, userId, userEmail);

		if (!result.success) {
			return fail(result.error?.includes('DB Error:') ? 500 : 400, {
				success: false,
				error: result.error || 'Failed to remove course creator tag'
			});
		}

		return {
			success: true,
			message: result.message
		};
	},

	addTag: async ({ request, params, locals: { supabase } }) => {
		const formData = await request.formData();
		const userId = formData.get('userId')?.toString() || '';
		const tagName = formData.get('tagName')?.toString() || '';
		const userEmail = formData.get('userEmail')?.toString() || '';
		const companyId = params.companyId;

		if (!userId || !tagName || !companyId) {
			return fail(400, {
				type: 'error',
				error: 'Missing required fields'
			});
		}

		try {
			const { data: existingTag, error: existingTagError } = await supabase
				.from('user_tags')
				.select('*')
				.eq('user_id', userId)
				.eq('tag_name', tagName)
				.eq('company_id', companyId)
				.single();

			if (existingTagError && existingTagError.code !== 'PGRST116') {
				console.error('Error checking existing tag:', existingTagError);
				return fail(500, {
					type: 'error',
					error: 'Failed to check existing tag'
				});
			}

			if (existingTag) {
				return fail(400, {
					type: 'error',
					error: 'User already has this tag'
				});
			}

			const { data: insertedTag, error: insertError } = await supabase
				.from('user_tags')
				.insert([
					{
						user_id: userId,
						tag_name: tagName,
						company_id: companyId
					}
				])
				.select();

			if (insertError) {
				console.error('Error inserting tag:', insertError);
				return fail(500, {
					type: 'error',
					error: 'Failed to add tag'
				});
			}

			return {
				type: 'success',
				data: insertedTag
			};
		} catch (err) {
			console.error('Unexpected error in addTag:', err);
			return fail(500, {
				type: 'error',
				error: 'An unexpected error occurred'
			});
		}
	},

	removeTag: async ({ request, locals: { supabase } }) => {
		const formData = await request.formData();
		const tagId = formData.get('tagId')?.toString() || '';
		const userId = formData.get('userId')?.toString() || '';
		const tagName = formData.get('tagName')?.toString() || '';

		if (!tagId || !userId || !tagName) {
			return fail(400, {
				type: 'error',
				error: 'Missing required fields'
			});
		}

		try {
			const { error: deleteError } = await supabase
				.from('user_tags')
				.delete()
				.eq('id', tagId)
				.eq('user_id', userId)
				.eq('tag_name', tagName);

			if (deleteError) {
				console.error('Error removing tag:', deleteError);
				return fail(500, {
					type: 'error',
					error: 'Failed to remove tag'
				});
			}

			return {
				type: 'success'
			};
		} catch (err) {
			console.error('Unexpected error in removeTag:', err);
			return fail(500, {
				type: 'error',
				error: 'An unexpected error occurred'
			});
		}
	},

	signUp: async ({ request }) => {
		const formData = await request.formData();
		const email = formData.get('email')?.toString();
		const password = formData.get('password')?.toString();

		if (!email || !password) {
			return fail(400, {
				error: 'Missing email or password'
			});
		}

		const { error: signUpError } = await supabaseAdmin.auth.signUp({
			email,
			password,
			options: {
				emailRedirectTo: `${baseUrl}/auth/callback`
			}
		});

		if (signUpError) {
			return fail(400, {
				error: signUpError.message
			});
		}

		return { success: true };
	}
};

// Function to send invite email
const sendInviteEmail = async (
	userEmail: string,
	locals: { supabase }
): Promise<{ success: boolean; error?: string }> => {
	try {
		const { data: emailSend, error: emailError } =
			await supabaseAdmin.auth.admin.inviteUserByEmail(userEmail);

		if (emailError) {
			console.error('Email invite error:', emailError);
			return { success: false, error: emailError.message };
		}

		// If we have data and the user object exists, it's a success
		if (emailSend?.user) {
			console.log('Invite email sent to', userEmail);
			return { success: true };
		}

		// If we get here without an error but also without user data, something went wrong
		console.error('No user data returned from invite:', emailSend);
		return { success: false, error: 'Failed to create user invite' };
	} catch (error) {
		console.error('Error in sendInviteEmail:', error);
		return {
			success: false,
			error: error instanceof Error ? error.message : 'Failed to send invite email'
		};
	}
};
