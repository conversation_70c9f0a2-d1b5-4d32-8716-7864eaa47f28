<script lang="ts">
	import { invalidateAll } from '$app/navigation';
	import { Badge } from '$lib/components/ui/badge';
	import * as Card from '$lib/components/ui/card';
	import * as Table from '$lib/components/ui/table';
	import * as Dialog from '$lib/components/ui/dialog';
	import UserActions from '$lib/components/UserActions.svelte';
	import EditContactForm from '$lib/components/EditContactDetails/EditContactForm.svelte';
	import type { PageData } from './$types';
	import { formSchema } from '$lib/components/EditContactDetails/schema';
	import { superValidate, message } from 'sveltekit-superforms/client';
	import { zod } from 'sveltekit-superforms/adapters';
	import { enhance } from '$app/forms';
	import { BookOpen, Tag, X, Plus, AlertTriangle } from 'lucide-svelte';
	import { onMount } from 'svelte';
	import { supabase } from '$lib/supabaseClient';
	import { toast } from 'svelte-sonner';
	import { Toaster } from 'svelte-sonner';
	import * as Tooltip from '$lib/components/ui/tooltip';
	import * as Alert from '$lib/components/ui/alert';
	import { goto } from '$app/navigation';

	export let data: PageData;
	let isLoading = true;
	let debugInfo = {
		dataLoaded: false,
		hasCompanyUsers: false,
		userCount: 0,
		errors: [] as string[]
	};

	// Define extended user type that includes SharePoint properties
	interface ExtendedUser {
		user_name: any;
		user_email: any;
		user_phone_number: any;
		is_active: any;
		user_id: any;
		role: any;
		company_id: string;
		invite_id: any;
		invite_status: any;
		user_status: any;
		has_file_access: boolean;
		source?: string;
		user_type?: string;
		tags: UserTag[];
	}

	// Cast companyUsers to ExtendedUser type with safety checks
	$: typedCompanyUsers = data.companyUsers ? (data.companyUsers as ExtendedUser[]) : [];
	$: userTagsMap = new Map(Object.entries(data.userTags || {}));
	$: filteredUsers = [] as ExtendedUser[];

	// Map to store user tags for each user
	type UserTag = {
		id: string;
		user_id: string;
		tag_name: string;
		company_id: string;
		created_at: string;
	};

	// Available tag types
	const availableTags = [{ value: 'course_creator', label: 'Course Creator', paid: true }];

	let isRemovingTag = false;
	let tagToRemove: UserTag | null = null;
	let showRemoveTagDialog = false;

	// Add tag variables
	let showAddTagDialog = false;
	let userForTagAddition: ExtendedUser | null = null;
	let selectedTagToAdd = '';
	let isAddingTag = false;

	// On mount, fetch all user tags for this company
	onMount(async () => {
		try {
			console.log('DEBUG: Page mounted, initial data:', data);
			console.log('DEBUG: Company users from server:', data.companyUsers);
			console.log('DEBUG: Course creator user IDs from server:', data.courseCreatorUserIds);

			// Continue only if we have company users
			if (!data.companyUsers || data.companyUsers.length === 0) {
				console.log('DEBUG: No company users found in the data');
				isLoading = false;
				return;
			}

			isLoading = false;
		} catch (err) {
			console.error('Error in onMount:', err);
			toast.error('Failed to initialize user management');
			debugInfo.errors.push(`onMount error: ${err instanceof Error ? err.message : String(err)}`);
		} finally {
			isLoading = false;
		}
	});

	// Update tags when user tags change
	async function handleUserUpdate() {
		try {
			const response = await fetch(`?/getTags`, {
				method: 'GET'
			});

			if (!response.ok) {
				throw new Error(`HTTP error! status: ${response.status}`);
			}

			// Invalidate all data to refresh the page
			await invalidateAll();
		} catch (err) {
			console.error('Error refreshing user tags:', err);
			toast.error('Failed to refresh user tags');
		}
	}

	// Function to add a tag to a user
	async function addTag() {
		if (!selectedTagToAdd || !userForTagAddition) {
			toast.error('Please select a tag and user');
			return;
		}

		console.log('Starting tag addition process', {
			user: userForTagAddition.user_email,
			tag: selectedTagToAdd
		});
	}

	// Function to remove a tag from a user
	async function removeTag(user: ExtendedUser, tag: UserTag) {
		console.log('🏷️ Starting tag removal process', { user, tag });

		try {
			// Get the user's tags from our map
			const userTags = userTagsMap.get(user.user_id) || [];
			console.log('📋 Current user tags:', userTags);

			// Find the actual tag in our map
			const actualTag = userTags.find((t) => t.tag_name === tag.tag_name);

			if (!actualTag) {
				console.error('❌ Tag not found in user tags map:', { tag, userTags });
				toast.error('Error removing tag: Tag not found');
				return;
			}

			console.log('📝 Found actual tag:', actualTag);

			const formData = new FormData();
			formData.append('tagId', actualTag.id);
			formData.append('userId', user.user_id);
			formData.append('userEmail', user.user_email);
			formData.append('tagName', tag.tag_name);

			const response = await fetch('?/removeTag', {
				method: 'POST',
				body: formData
			});

			const result = await response.json();

			if (!response.ok) {
				console.error('❌ Failed to remove tag:', result);
				toast.error('Failed to remove tag: ' + (result.error || 'Unknown error'));
				return;
			}

			// Close dialog and clear selection
			showRemoveTagDialog = false;
			tagToRemove = null;

			toast.success('Tag removed successfully');
			await handleUserUpdate(); // This will call invalidateAll
		} catch (error) {
			console.error('❌ Unexpected error removing tag:', error);
			toast.error('An unexpected error occurred while removing the tag');
		}
	}

	// Helper function to get the actual tag ID from the database
	async function getActualTagId(userId: string, tagName: string): Promise<string | null> {
		try {
			const response = await fetch(`/api/tags?userId=${userId}&tagName=${tagName}`);
			const data = await response.json();
			return data.tagId || null;
		} catch (error) {
			console.error('Error fetching actual tag ID:', error);
			return null;
		}
	}

	// Function to confirm tag removal
	function confirmRemoveTag(tag: UserTag) {
		console.log('DEBUG: Opening remove tag confirmation', { tag });
		tagToRemove = tag;
		showRemoveTagDialog = true;
	}

	// Function to open add tag dialog
	function openAddTagDialog(user: ExtendedUser) {
		userForTagAddition = user;
		selectedTagToAdd = '';
		showAddTagDialog = true;
	}

	// Helper function to get tag display name
	function getTagDisplayName(tagName: string): { name: string; icon?: any; color?: string } {
		switch (tagName) {
			case 'course_creator':
				return {
					name: 'Course Creator',
					icon: BookOpen,
					color:
						'bg-green-100 text-green-800 dark:bg-green-800/30 dark:text-green-400 border-green-200 dark:border-green-800'
				};
			case 'premium_support':
				return {
					name: 'Premium Support',
					icon: Tag,
					color:
						'bg-purple-100 text-purple-800 dark:bg-purple-800/30 dark:text-purple-400 border-purple-200 dark:border-purple-800'
				};
			case 'beta_tester':
				return {
					name: 'Beta Tester',
					icon: Tag,
					color:
						'bg-amber-100 text-amber-800 dark:bg-amber-800/30 dark:text-amber-400 border-amber-200 dark:border-amber-800'
				};
			case 'power_user':
				return {
					name: 'Power User',
					icon: Tag,
					color:
						'bg-indigo-100 text-indigo-800 dark:bg-indigo-800/30 dark:text-indigo-400 border-indigo-200 dark:border-indigo-800'
				};
			default:
				return {
					name: tagName.replace('_', ' '),
					icon: Tag,
					color:
						'bg-blue-100 text-blue-800 dark:bg-blue-800/30 dark:text-blue-400 border-blue-200 dark:border-blue-800'
				};
		}
	}

	// Option to filter users
	let showGroups = true;

	// Add debugging
	console.log('DEBUG: Initial users data:', data.companyUsers);

	// Check specifically for TOTMTester
	const totmTester = data.companyUsers
		? data.companyUsers.find((user) => user.user_email?.toLowerCase().includes('totmtester'))
		: null;

	// Safely check for TOTMTester and SharePoint groups
	$: {
		if (totmTester) {
			console.log('DEBUG: Found TOTMTester in users:', totmTester);
		} else {
			console.log('DEBUG: TOTMTester not found in users data');

			// Only look for SharePoint groups if typedCompanyUsers is defined
			if (typedCompanyUsers && typedCompanyUsers.length > 0) {
				const spGroups = typedCompanyUsers.filter((user) => user.source === 'sharepoint_group');
				console.log(`DEBUG: Found ${spGroups.length} SharePoint groups:`, spGroups.slice(0, 5));
			}
		}
	}

	$: {
		// Make sure typedCompanyUsers is defined before filtering
		filteredUsers =
			typedCompanyUsers && typedCompanyUsers.length > 0
				? typedCompanyUsers.filter((user) => {
						// Filter out sharepoint groups if the checkbox is unchecked
						if (!showGroups && user.source === 'sharepoint_group') {
							return false;
						}

						// Filter out users with sharepoint.group in their email
						if (user.user_email && user.user_email.includes('sharepoint.group')) {
							return false;
						}

						return true;
					})
				: [];

		console.log(
			`DEBUG: Filtered users: showing ${filteredUsers.length} out of ${data.companyUsers?.length || 0} users`
		);
	}

	let showEditContactDialog = false;
	let selectedUser: ExtendedUser | null = null;

	function handleEditContact(event: CustomEvent<{ user: ExtendedUser }>) {
		selectedUser = event.detail.user;
		showEditContactDialog = true;
	}

	// Create a form with the correct schema
	let editForm: any = null;
	$: if (selectedUser) {
		// Re-create the form whenever selectedUser changes
		(async () => {
			editForm = await superValidate(zod(formSchema), {
				id: 'edit-contact-form',
				defaults: {
					userId: selectedUser.user_id,
					email: selectedUser.user_email || '',
					phone: selectedUser.user_phone_number || '',
					role: selectedUser.role || 'member'
				}
			});
		})();
	}

	// Handle refresh attempt
	function handleRefresh() {
		isLoading = true;
		// Invalidate data and reload
		invalidateAll();
	}

	// Add this function to handle form completion and invalidate data
	async function handleFormResult() {
		await invalidateAll();
	}

	// Add helper function for user tags
	function getUserTags(userId: string): UserTag[] {
		const tags = userTagsMap.get(userId);
		return Array.isArray(tags) ? (tags as UserTag[]) : [];
	}
</script>

{#if data.needsConsent}
	<div class="flex flex-col items-center justify-center p-4">
		<h2 class="mb-4 text-lg font-semibold">Additional Permissions Required</h2>
		<p class="mb-4">We need your permission to access Microsoft Graph data.</p>
		<form action="/api/link-sharepoint" method="POST" use:enhance={handleFormResult}>
			<button type="submit" class="btn btn-primary"> Grant Permissions </button>
		</form>
	</div>
{:else if isLoading}
	<div class="flex items-center justify-center p-8">
		<div class="w-12 h-12 border-t-2 border-b-2 rounded-full animate-spin border-primary"></div>
		<p class="ml-4 text-lg">Loading user data...</p>
	</div>
{:else if !data.companyUsers || data.companyUsers.length === 0}
	<Card.Root class="bg-card text-card-foreground">
		<Card.Header class="px-7">
			<Card.Title>Company Users</Card.Title>
			<Card.Description class="text-muted-foreground">
				No users found for this company.
			</Card.Description>
		</Card.Header>
		<Card.Content class="py-8 text-center">
			<p>There are no users associated with this company yet.</p>

			<!-- Debug Information -->
			<details class="p-4 mt-6 border rounded-md">
				<summary class="font-medium cursor-pointer">Debug Information</summary>
				<pre class="p-2 mt-2 overflow-auto text-xs rounded bg-muted max-h-96">
Data loaded: {debugInfo.dataLoaded}
Has company users: {debugInfo.hasCompanyUsers}
User count: {debugInfo.userCount}
Errors: {JSON.stringify(debugInfo.errors, null, 2)}
				</pre>
			</details>
		</Card.Content>
	</Card.Root>
{:else}
	<Card.Root class="bg-card text-card-foreground">
		<Card.Header class="flex items-start justify-between px-7">
			<div>
				<Card.Title>Company Users</Card.Title>
				<Card.Description class="text-muted-foreground"
					>Manage user access and permissions.</Card.Description
				>
			</div>
			<div class="flex items-center space-x-2">
				<label class="flex items-center space-x-1 text-sm cursor-pointer">
					<input type="checkbox" bind:checked={showGroups} class="checkbox" />
					<span>Show SharePoint Groups</span>
				</label>
			</div>
		</Card.Header>
		<Card.Content>
			{#if filteredUsers.length === 0}
				<div class="py-12 text-center text-gray-500 dark:text-gray-400">
					<p>No users found with current filter settings</p>
				</div>
			{:else}
				<Table.Root>
					<Table.Header>
						<Table.Row class="border-border">
							<Table.Head>User</Table.Head>
							<Table.Head class="hidden sm:table-cell">Phone Number</Table.Head>
							<Table.Head class="hidden sm:table-cell">Status</Table.Head>
							<Table.Head class="text-right">Action</Table.Head>
						</Table.Row>
					</Table.Header>
					<Table.Body>
						{#each filteredUsers as cUser}
							<Table.Row class="border-border hover:bg-muted/50">
								<Table.Cell>
									<div class="font-medium text-card-foreground">
										{cUser.user_name || 'Unnamed User'}
										{#if cUser.source === 'sharepoint_group'}
											<Badge variant="outline" class="ml-2">
												{cUser.user_type === 'domaingroup'
													? 'Microsoft 365 Group'
													: 'SharePoint Group'}
											</Badge>
										{/if}
									</div>
									<div class="hidden text-sm text-muted-foreground md:inline">
										{cUser.user_email || 'No email'}
									</div>

									<!-- User Tags Section -->
									<div class="flex flex-wrap items-center gap-1 mt-1">
										{#if getUserTags(cUser.user_id).length}
											{#each getUserTags(cUser.user_id) as tag}
												{@const tagDisplay = getTagDisplayName(tag.tag_name)}
												<Tooltip.Root>
													<Tooltip.Trigger>
														<Badge
															variant="outline"
															class="ml-0 mr-1 {tagDisplay.color} inline-flex items-center group"
														>
															<svelte:component this={tagDisplay.icon} class="w-3 h-3 mr-1" />
															{tagDisplay.name}
															<button
																class="ml-1 hover:bg-red-200 dark:hover:bg-red-800 rounded-full p-0.5 opacity-70 hover:opacity-100 transition-opacity"
																on:click|stopPropagation={() => confirmRemoveTag(tag)}
																aria-label="Remove tag"
															>
																<X class="w-2 h-2" />
															</button>
														</Badge>
													</Tooltip.Trigger>
													<Tooltip.Content side="top">Click X to remove this tag</Tooltip.Content>
												</Tooltip.Root>
											{/each}
										{/if}

										<!-- Add Tag Button -->
										{#if cUser.user_status !== 'inactive'}
											<Tooltip.Root>
												<Tooltip.Trigger>
													<button
														class="inline-flex items-center justify-center w-6 h-6 transition-colors rounded-full bg-primary/10 hover:bg-primary/20"
														on:click={() => openAddTagDialog(cUser)}
														aria-label="Add tag"
													>
														<Plus class="w-3 h-3 text-primary" />
													</button>
												</Tooltip.Trigger>
												<Tooltip.Content side="top">Add a tag</Tooltip.Content>
											</Tooltip.Root>
										{/if}
									</div>
								</Table.Cell>
								<Table.Cell class="hidden sm:table-cell">
									{cUser.user_phone_number || ''}
								</Table.Cell>
								<Table.Cell class="hidden sm:table-cell">
									<Badge
										class="text-xs"
										variant={cUser.user_status === 'active'
											? cUser.role === 'admin'
												? 'destructive'
												: 'secondary'
											: cUser.user_status === 'pending'
												? 'default'
												: 'outline'}
									>
										{#if cUser.user_status === 'active'}
											{cUser.role === 'admin'
												? 'Admin'
												: cUser.role === 'expert'
													? 'Expert'
													: 'Member'}
										{:else if cUser.user_status === 'pending'}
											Pending Invite
										{:else}
											Inactive
										{/if}
									</Badge>
								</Table.Cell>
								<Table.Cell class="text-right">
									<UserActions
										user={cUser}
										form={data.form}
										on:userUpdate={handleUserUpdate}
										on:editContact={handleEditContact}
									/>
								</Table.Cell>
							</Table.Row>
						{/each}
					</Table.Body>
				</Table.Root>
			{/if}
		</Card.Content>
	</Card.Root>

	<!-- Edit Contact Dialog -->
	<Dialog.Root bind:open={showEditContactDialog}>
		<Dialog.Content>
			<Dialog.Header>
				<Dialog.Title>Edit Contact Information</Dialog.Title>
				<Dialog.Description>
					Update contact details for {selectedUser?.user_name || 'User'}
				</Dialog.Description>
			</Dialog.Header>
			{#if selectedUser && editForm}
				<EditContactForm
					data={editForm}
					user={{
						user_id: selectedUser.user_id,
						user_email: selectedUser.user_email || '',
						user_phone_number: selectedUser.user_phone_number || '',
						role: selectedUser.role || 'member'
					}}
				/>
			{/if}
		</Dialog.Content>
	</Dialog.Root>

	<!-- Remove Tag Dialog -->
	<Dialog.Root bind:open={showRemoveTagDialog}>
		<Dialog.Content>
			<Dialog.Header>
				<Dialog.Title>Remove Tag</Dialog.Title>
				<Dialog.Description>
					Are you sure you want to remove this tag? This action cannot be undone.
				</Dialog.Description>
			</Dialog.Header>
			<form
				method="POST"
				action="?/removeTag"
				use:enhance={() => {
					return async ({ result }) => {
						if (result.type === 'success') {
							toast.success('Tag removed successfully');
							showRemoveTagDialog = false;
							tagToRemove = null;
							await invalidateAll();
						} else if (result.type === 'error') {
							toast.error(`Failed to remove tag: ${result.error || 'Unknown error'}`);
						}
					};
				}}
			>
				{#if tagToRemove}
					<input type="hidden" name="tagId" value={tagToRemove.id} />
					<input type="hidden" name="userId" value={tagToRemove.user_id} />
					<input type="hidden" name="tagName" value={tagToRemove.tag_name} />
					<input
						type="hidden"
						name="userEmail"
						value={typedCompanyUsers.find((u) => u.user_id === tagToRemove.user_id)?.user_email ||
							''}
					/>
				{/if}
				<div class="flex justify-end space-x-2">
					<button
						type="button"
						class="btn variant-ghost"
						on:click={() => {
							showRemoveTagDialog = false;
							tagToRemove = null;
						}}
					>
						Cancel
					</button>
					<button type="submit" class="btn variant-filled-error" disabled={!tagToRemove}>
						Remove Tag
					</button>
				</div>
			</form>
		</Dialog.Content>
	</Dialog.Root>

	<!-- Add Tag Dialog -->
	<Dialog.Root bind:open={showAddTagDialog}>
		<Dialog.Content>
			<Dialog.Header>
				<Dialog.Title>Add Tag</Dialog.Title>
				<Dialog.Description>
					Assign a tag to {userForTagAddition?.user_name || 'User'}
				</Dialog.Description>
			</Dialog.Header>
			<form
				method="POST"
				action="?/addTag"
				use:enhance={() => {
					return async ({ result }) => {
						if (result.type === 'success') {
							toast.success(`Successfully added ${getTagDisplayName(selectedTagToAdd).name} tag`);
							showAddTagDialog = false;
							await invalidateAll();
						} else if (result.type === 'error') {
							const errorMessage = result.error || 'Failed to add tag';
							if (errorMessage.includes('already has this tag')) {
								toast.error(`User already has the ${getTagDisplayName(selectedTagToAdd).name} tag`);
							} else {
								toast.error(`Error adding tag: ${errorMessage}`);
							}
						}
						userForTagAddition = null;
						selectedTagToAdd = '';
					};
				}}
			>
				<div class="my-4">
					<input type="hidden" name="userId" value={userForTagAddition?.user_id || ''} />
					<input type="hidden" name="tagName" value={selectedTagToAdd} />
					<input type="hidden" name="userName" value={userForTagAddition?.user_name || ''} />
					<input type="hidden" name="userEmail" value={userForTagAddition?.user_email || ''} />

					<label
						for="tag-select"
						class="block mb-2 text-sm font-medium text-gray-900 dark:text-gray-100"
					>
						Select a tag
					</label>
					<select
						id="tag-select"
						bind:value={selectedTagToAdd}
						class="w-full p-2 text-gray-900 bg-white border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100"
					>
						<option value="" disabled>Select a tag</option>
						{#each availableTags as tag}
							<option value={tag.value}>
								{tag.label}
								{tag.paid ? '(Paid Feature)' : ''}
							</option>
						{/each}
					</select>

					{#if selectedTagToAdd && availableTags.find((tag) => tag.value === selectedTagToAdd)?.paid}
						<p class="mt-2 text-sm text-amber-600 dark:text-amber-400">
							<strong>Note:</strong> This is a paid feature. Adding this tag will enable premium functionality
							for this user.
						</p>
					{/if}
				</div>
				<div class="flex justify-end space-x-2">
					<button
						type="button"
						class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm dark:border-gray-600 dark:text-gray-300 dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
						on:click={() => {
							showAddTagDialog = false;
							userForTagAddition = null;
							selectedTagToAdd = '';
						}}
					>
						Cancel
					</button>
					<button
						type="submit"
						class="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-green-300 rounded-md shadow-sm dark:border-green-700 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-800"
						disabled={!selectedTagToAdd}
					>
						Add Tag
					</button>
				</div>
			</form>
		</Dialog.Content>
	</Dialog.Root>
{/if}

<!-- Toast notifications -->
<Toaster />
