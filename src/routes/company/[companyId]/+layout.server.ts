import type { LayoutServerLoad } from "./$types";
import { redirect } from "@sveltejs/kit";

export const load: LayoutServerLoad = async (
  { locals: { supabase, user }, params },
) => {
  if (!user) {
    // Redirect unauthenticated users to the auth page
    throw redirect(303, "/auth");
  }

  const { companyId } = params;

  // First get the company member record for this user to verify membership
  const { data: memberData, error: memberError } = await supabase
    .from("company_members")
    .select("id, role")
    .eq("company_id", companyId)
    .eq("user_id", user.id)
    .single();

  // If there's an error or no member data, the user is not a member of this company
  if (memberError || !memberData) {
    console.error(
      "User is not a member of this company:",
      memberError ? memberError.message : "No membership found",
    );

    // Redirect unauthorized users to the company selection page
    throw redirect(303, "/company");
  }

  // Fetch company's communication channels
  const { data: channels, error: channelsError } = await supabase
    .from("communication_channels")
    .select("*")
    .eq("company_id", companyId);

  if (channelsError) {
    console.error("Error fetching channels:", channelsError);
  }

  // Then fetch user's platform profiles using the company_member_id
  const { data: userPlatformProfiles, error: profilesError } = await supabase
    .from("user_platform_profiles")
    .select("*")
    .eq("company_member_id", memberData.id);

  if (profilesError) {
    console.error("Error fetching user platform profiles:", profilesError);
  }

  return {
    channels: channels || [],
    userPlatformProfiles: userPlatformProfiles || [],
    userRole: memberData?.role || null,
  };
};
