<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { notifications } from '$lib/stores/notifications';
  import type { LayoutData } from './$types';
  import { page } from '$app/stores';
  import { companySwitcherVisible } from '$lib/stores/companySwitcher';

  export let data: LayoutData;
  
  let { channels, userPlatformProfiles } = data;
  $: companyId = $page.params.companyId;

  // Show company switcher when in company route
  $: if (companyId) {
    companySwitcherVisible.set(true);
  }

  // Check if company has WhatsApp and user doesn't
  let showWhatsAppNotification = channels.some(c => c.platform_type === 'whatsapp') && 
    !userPlatformProfiles.some(p => p.platform_type === 'whatsapp');

  onMount(() => {
    if (showWhatsAppNotification) {
      // Create a unique ID for this company's WhatsApp notification
      const notificationId = `whatsapp-notification-${companyId}`;
      
      // Check if this specific company's WhatsApp notification already exists
      const existingNotification = $notifications.find(n => 
        n.id === notificationId
      );

      // Only add if no notification exists for this company
      if (!existingNotification) {
        notifications.add({
          id: notificationId,
          type: "info",
          message: "This company uses WhatsApp for communication. Add your WhatsApp number to start chatting.",
          link: "/profile",
          linkText: "Add WhatsApp number in profile settings"
        });
      }
    }
  });

  onDestroy(() => {
    // Clear all notifications when leaving company route
    notifications.clear();
    // Hide company switcher
    companySwitcherVisible.set(false);
  });
</script>

<slot /> 