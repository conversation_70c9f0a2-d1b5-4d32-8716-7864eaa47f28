import type { PageServerLoad } from "./$types";
import { error, redirect } from "@sveltejs/kit";
import type { Actions } from "./$types";

export const load: PageServerLoad = async (
  { locals: { supabase, user }, params, parent, url },
) => {
  // Check if user is authenticated
  if (!user) {
    throw redirect(302, "/auth");
  }

  // Get parent data which includes the userRole
  const parentData = await parent();
  const userRole = parentData.userRole;

  // Check if user has appropriate permissions
  // Only 'admin' and 'expert' roles can access the inbox
  if (userRole !== "admin" && userRole !== "expert") {
    console.error(`Access denied: Role '${userRole}' cannot access inbox`, {
      userId: user.id,
      companyId: params.companyId,
      role: userRole,
    });

    // Redirect to access-denied page instead of directly to company dashboard
    const redirectUrl = `/access-denied?` + new URLSearchParams({
      redirectUrl: `/company/${params.companyId}`,
      resourceType: "inbox",
      requiredRole: "admin or expert role",
      companyId: params.companyId,
    }).toString();

    throw redirect(302, redirectUrl);
  }

  console.log("Loading inbox questions...");

  const { data: questions, error: questionsError } = await supabase
    .from("totm_inbox_questions")
    .select(`
      *,
      responses:totm_inbox_responses(
        id,
        response_text,
        created_at,
        expert:expert_id(
          id,
          email
        )
      )
    `)
    .order("created_at", { ascending: false });

  if (questionsError) {
    console.error("Error fetching questions:", questionsError);
    return {
      questions: [],
    };
  }

  console.log(`Successfully loaded ${questions?.length ?? 0} questions`);
  console.debug("Questions data:", questions);

  return {
    questions,
  };
};

export const actions = {
  respond: async (event) => {
    const { request, locals: { supabase, user }, params, parent } = event;

    // Check if user is authenticated
    if (!user) {
      throw error(401, "Unauthorized");
    }

    try {
      // Get parent data which includes the userRole
      const parentData = await parent();
      const userRole = parentData.userRole;

      // Check if user has appropriate permissions
      // Only 'admin' and 'expert' roles can respond to inbox questions
      if (userRole !== "admin" && userRole !== "expert") {
        throw error(
          403,
          "Access denied: Insufficient permissions to respond to questions",
        );
      }

      const formData = await request.formData();
      const questionId = formData.get("questionId")?.toString();
      const responseText = formData.get("responseText")?.toString();

      if (!questionId || !responseText?.trim()) {
        throw error(400, "Missing required fields");
      }

      // Call the RPC function to add the response
      const { data, error: rpcError } = await supabase
        .rpc("add_response", {
          question_id: questionId,
          response_text: responseText,
        });

      if (rpcError) {
        console.error("Error adding response:", rpcError);
        throw error(500, "Failed to add response");
      }

      // Fetch the updated question with responses
      const { data: updatedQuestion, error: fetchError } = await supabase
        .from("totm_inbox_questions")
        .select(`
          *,
          responses:totm_inbox_responses(
            id,
            response_text,
            created_at,
            expert:expert_id(
              id,
              email
            )
          )
        `)
        .eq("id", questionId)
        .single();

      if (fetchError) {
        console.error("Error fetching updated question:", fetchError);
        // Don't throw error since response was added successfully
      }

      return {
        success: true,
        question: updatedQuestion,
      };
    } catch (e) {
      // Rethrow errors from permission checks
      if (e instanceof Error) {
        throw error(500, e.message);
      }
      throw e;
    }
  },
} satisfies Actions;
