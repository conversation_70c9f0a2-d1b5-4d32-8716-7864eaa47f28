import { writable } from "svelte/store";
import type { Tables } from "../../../../../database.types";

type Question = Tables<"totm_inbox_questions">;

type Response = {
  id: string;
  response_text: string | null;
  created_at: string;
  expert: {
    id: string;
    email: string;
  } | null;
};

export type Mail = {
  id: string;
  text: string | null;
  category: string | null;
  priority: string | null;
  status: string | null;
  assigned_to: string | null;
  created_at: string;
  responses: Response[];
};

export const mails = writable<Mail[]>([]);
export const selectedMail = writable<Mail>();
