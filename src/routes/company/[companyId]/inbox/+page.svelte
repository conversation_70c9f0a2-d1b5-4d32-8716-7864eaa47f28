<script lang="ts">
  import { page } from '$app/stores'
  import { mails, selectedMail } from './store'
  import type { PageData } from './$types'
  import { enhance } from '$app/forms'
  import type { ActionResult, SubmitFunction } from '@sveltejs/kit'
  import { But<PERSON> } from "$lib/components/ui/button"
  import { Separator } from "$lib/components/ui/separator"
  import { Archive, ArchiveX, Clock, Forward, MoreVertical, Reply, ReplyAll, Trash2 } from 'lucide-svelte'
  import * as DropdownMenu from "$lib/components/ui/dropdown-menu"
  import { invalidateAll } from '$app/navigation'
  import { toast } from 'svelte-sonner'

  export let data: PageData
  let responseText = ''
  let responseForm: HTMLFormElement

  function handleKeyDown(event: KeyboardEvent) {
    // Check for Cmd+Return (Mac) or Ctrl+Return (Windows/Linux)
    if ((event.metaKey || event.ctrlKey) && event.key === 'Enter') {
      event.preventDefault()
      if (responseText.trim()) {
        responseForm.requestSubmit()
      }
    }
  }

  const handleSubmitResponse: SubmitFunction = () => {
    return async ({ result, update }) => {
      if (result.type === 'success') {
        // Clear the response text
        responseText = ''
        toast.success('Response sent successfully')
        
        // Invalidate all data to refresh from server
        await invalidateAll()
        
        // Update the form
        await update()
      } else {
        console.error('Error submitting response:', result)
        toast.error('Failed to submit response. Please try again.')
      }
    }
  }

  // Initialize mails store with data from server
  $: {
    if (data.questions) {
      console.log("Updating mails store with questions data");
      console.debug("Raw questions data:", data.questions);
      
      $mails = data.questions.map(q => ({
        id: q.id,
        text: q.question_text,
        category: q.category,
        priority: q.priority,
        status: q.status,
        assigned_to: q.assigned_to,
        created_at: q.created_at,
        responses: q.responses || []
      }));
      
      console.log(`Processed ${$mails.length} mail items`);
      console.debug("Processed mails data:", $mails);
    }
  }

  // Log when selected mail changes
  $: if ($selectedMail) {
    console.log("Selected mail changed:", {
      id: $selectedMail.id,
      category: $selectedMail.category,
      status: $selectedMail.status
    });
  }
</script>

<div class="flex flex-col h-[calc(100vh-theme(spacing.32))]">
  <!-- <div class="border-b">
    <div class="flex items-center h-16 px-4">
      <div class="flex items-center gap-2">
        <Button variant="ghost" size="icon" disabled={!$selectedMail}>
          <Archive class="w-4 h-4" />
          <span class="sr-only">Archive</span>
        </Button>
        <Button variant="ghost" size="icon" disabled={!$selectedMail}>
          <ArchiveX class="w-4 h-4" />
          <span class="sr-only">Move to junk</span>
        </Button>
        <Button variant="ghost" size="icon" disabled={!$selectedMail}>
          <Trash2 class="w-4 h-4" />
          <span class="sr-only">Move to trash</span>
        </Button>
        <Separator orientation="vertical" class="h-6 mx-1" />
        <Button variant="ghost" size="icon" disabled={!$selectedMail}>
          <Forward class="w-4 h-4" />
          <span class="sr-only">Forward</span>
        </Button>
        <Button variant="ghost" size="icon" disabled={!$selectedMail}>
          <Reply class="w-4 h-4" />
          <span class="sr-only">Reply</span>
        </Button>
        <Button variant="ghost" size="icon" disabled={!$selectedMail}>
          <ReplyAll class="w-4 h-4" />
          <span class="sr-only">Reply all</span>
        </Button>
      </div>
      <div class="flex items-center gap-2 ml-auto">
        {#if $selectedMail}
          <Button variant="ghost" size="icon">
            <Clock class="w-4 h-4" />
            <span class="sr-only">Snooze</span>
          </Button>
          <DropdownMenu.Root>
            <DropdownMenu.Trigger asChild let:builder>
              <Button variant="ghost" size="icon" builders={[builder]}>
                <MoreVertical class="w-4 h-4" />
                <span class="sr-only">More</span>
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content align="end">
              <DropdownMenu.Label>More actions</DropdownMenu.Label>
              <DropdownMenu.Separator />
              <DropdownMenu.Item>Mark as unread</DropdownMenu.Item>
              <DropdownMenu.Item>Flag message</DropdownMenu.Item>
              <DropdownMenu.Separator />
              <DropdownMenu.Item>Add label</DropdownMenu.Item>
              <DropdownMenu.Item>Move to folder</DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu.Root>
        {/if}
      </div>
    </div>
  </div> -->
  <div class="flex flex-1 overflow-hidden">
    <div class="w-[300px] flex-shrink-0 overflow-y-auto border-r">
      <div class="flex flex-col gap-2 p-4">
        {#each $mails as mail}
          <button
            class="flex flex-col items-start gap-2 p-3 text-sm text-left transition-all border rounded-lg hover:bg-muted"
            class:bg-muted={$selectedMail?.id === mail.id}
            on:click={() => {
              console.log("Mail item clicked:", {
                id: mail.id,
                category: mail.category
              });
              $selectedMail = mail;
            }}
          >
            <div class="flex flex-col w-full gap-1">
              <div class="flex items-center">
                <div class="flex-1 font-semibold truncate">
                  {mail.category || 'Uncategorized'}
                </div>
                <div class="text-xs text-muted-foreground">
                  {new Date(mail.created_at).toLocaleDateString()}
                </div>
              </div>
              <div class="text-xs font-medium">Status: {mail.status || 'New'}</div>
            </div>
            <div class="text-xs line-clamp-2 text-muted-foreground">
              {mail.text || 'No question text'}
            </div>
            {#if mail.priority === 'high'}
              <div class="flex items-center gap-2">
                <div class="h-1.5 w-1.5 rounded-full bg-red-500" />
                <span class="text-xs text-red-500">High priority</span>
              </div>
            {/if}
          </button>
        {/each}
      </div>
    </div>
    <div class="flex flex-col flex-1 overflow-hidden">
      {#if $selectedMail}
        <div class="flex-none p-4">
          <div class="flex flex-col gap-2">
            <h1 class="text-xl font-bold">{$selectedMail.category || 'Uncategorized'}</h1>
            <div class="flex items-center gap-4">
              <div class="flex items-center gap-2 text-sm text-muted-foreground">
                <span>Status: {$selectedMail.status || 'New'}</span>
                {#if $selectedMail.assigned_to}
                  <span>•</span>
                  <span>Assigned to: {$selectedMail.assigned_to}</span>
                {/if}
              </div>
            </div>
          </div>
        </div>
        <Separator />
        
        <div class="flex-1 overflow-y-auto">
          <div class="p-4 space-y-4">
            <div class="prose max-w-none">
              <p>{$selectedMail.text || 'No question text'}</p>
            </div>
            <div class="text-sm text-muted-foreground">
              Received: {new Date($selectedMail.created_at).toLocaleString()}
            </div>

            {#if $selectedMail.responses?.length > 0}
              <div class="mt-8">
                <h3 class="mb-4 text-lg font-semibold">Responses</h3>
                <div class="space-y-4">
                  {#each $selectedMail.responses as response}
                    <div class="p-4 border rounded-lg shadow-sm bg-card text-card-foreground">
                      <div class="prose max-w-none">
                        <p>{response.response_text}</p>
                      </div>
                      <div class="flex justify-between mt-2 text-sm text-muted-foreground">
                        <span>From: {response.expert?.email || 'Unknown'}</span>
                        <span>{new Date(response.created_at).toLocaleString()}</span>
                      </div>
                    </div>
                  {/each}
                </div>
              </div>
            {/if}
          </div>
        </div>

        <div class="flex-none p-4 border-t bg-background">
          <form 
            bind:this={responseForm}
            method="POST" 
            action="?/respond" 
            class="space-y-4" 
            use:enhance={handleSubmitResponse}
          >
            <input type="hidden" name="questionId" value={$selectedMail.id} />
            <div class="grid w-full gap-2">
              <label for="response" class="text-sm font-medium">Response</label>
              <textarea
                id="response"
                name="responseText"
                bind:value={responseText}
                on:keydown={handleKeyDown}
                class="w-full h-24 px-3 py-2 text-sm border rounded-md border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                placeholder="Type your response here... (Cmd+Return or Ctrl+Return to send)"
              />
            </div>
            <div class="flex justify-end">
              <Button type="submit" disabled={!responseText.trim()}>
                Send Response
              </Button>
            </div>
          </form>
        </div>
      {:else}
        <div class="flex items-center justify-center h-full">
          <p class="text-muted-foreground">Select a message to read</p>
        </div>
      {/if}
    </div>
  </div>
</div> 