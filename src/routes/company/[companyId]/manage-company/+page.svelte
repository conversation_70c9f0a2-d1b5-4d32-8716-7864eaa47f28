<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle,
		CardFooter
	} from '$lib/components/ui/card';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { toast } from 'svelte-sonner';
	import { enhance } from '$app/forms';
	import type { PageData } from './$types';
	import { onMount } from 'svelte';
	import { Separator } from '$lib/components/ui/separator';
	import * as Tabs from '$lib/components/ui/tabs';
	import { page } from '$app/stores';

	export let data: PageData;
	let { channels, companyId, personalization, enabledProducts } = data;
	console.log('[Channels]', channels);
	console.log('[EnabledProducts]', enabledProducts);
	console.log('[EnabledProducts Keys]', Object.keys(enabledProducts || {}));
	let whatsappNumber = channels.find((c) => c.platform_type === 'whatsapp')?.channel_id || '';
	let slackWorkspace = channels.find((c) => c.platform_type === 'slack')?.slack_workspace_id || '';
	let isConnectingWhatsApp = false;
	let whatsappStatus = whatsappNumber ? 'connected' : 'disconnected';

	// Default tab string that's guaranteed to be a string
	const defaultTab = 'branding';
	let activeTab: string = defaultTab;

	// Check if WhatsApp and Slack products are enabled
	const isWhatsAppEnabled = enabledProducts?.['whatsapp business'] || false;
	const isSlackEnabled = enabledProducts?.['slack integration'] || false;

	// Logo file inputs - use regular HTMLInputElements
	let brandingLogoInputElement: HTMLInputElement | null = null;
	let smallLogoInputElement: HTMLInputElement | null = null;
	let brandingLogoPreview = personalization?.branding_logo || '';
	let smallLogoPreview = personalization?.small_logo || '';
	let isUploadingLogos = false;

	// Handle logo file selection
	function handleLogoFileSelect(event: Event, type: 'branding' | 'small') {
		const input = event.target as HTMLInputElement;
		if (!input.files?.length) return;

		const file = input.files[0];
		const reader = new FileReader();

		reader.onload = (e) => {
			if (type === 'branding') {
				brandingLogoPreview = e.target?.result as string;
			} else {
				smallLogoPreview = e.target?.result as string;
			}
		};

		reader.readAsDataURL(file);
	}

	// Handle logo form submission
	async function handleLogoSubmit(event: Event) {
		const form = event.target as HTMLFormElement;
		const formData = new FormData(form);
		isUploadingLogos = true;

		try {
			const response = await fetch('?/updateLogos', {
				method: 'POST',
				body: formData
			});

			const result = await response.json();

			if (result.type === 'success') {
				toast.success('Company logos updated successfully');
				if (result.data.brandingLogo) brandingLogoPreview = result.data.brandingLogo;
				if (result.data.smallLogo) smallLogoPreview = result.data.smallLogo;
			} else {
				throw new Error(result.error || 'Failed to update logos');
			}
		} catch (error) {
			console.error('Error updating logos:', error);
			toast.error(error instanceof Error ? error.message : 'Failed to update logos');
		} finally {
			isUploadingLogos = false;
		}
	}

	// WhatsApp Business setup functions
	async function initiateWhatsAppSetup() {
		isConnectingWhatsApp = true;
		try {
			console.log('[WhatsApp Setup] Starting setup process');

			// Encode state with companyId
			const state = btoa(JSON.stringify({ companyId }));
			console.log('[WhatsApp Setup] Encoded state:', { companyId, state });

			// Add event listener for callback before initiating login
			window.addEventListener('message', handleWhatsAppCallback);

			// Log the exact redirect URI we're using - hardcode to match Facebook
			const redirectUri = 'https://localhost:5173/api/whatsapp/callback';
			console.log('[WhatsApp Setup] Using hardcoded redirect URI:', redirectUri);
			console.log('[WhatsApp Setup] Window location:', {
				href: window.location.href,
				origin: window.location.origin,
				protocol: window.location.protocol
			});

			// Use FB.login with WhatsApp config - direct type assertion here
			console.log('[WhatsApp Setup] Calling FB.login');
			const fbWindow = window as any;
			fbWindow.FB?.login(
				// Use a regular function instead of async function
				function (response: any) {
					console.log('[WhatsApp Setup] FB.login response:', response);

					if (response.authResponse) {
						console.log('[WhatsApp Setup] User authenticated, proceeding with code');
						if (response.authResponse.code) {
							// The code will be exchanged server-side
							console.log('[WhatsApp Setup] Received auth code:', response.authResponse.code);

							// Send the code to our backend for exchange using a separate async function
							handleAuthCode(response.authResponse.code, state);
						} else {
							console.error('[WhatsApp Setup] No auth code received');
							toast.error('Failed to receive authentication code');
							isConnectingWhatsApp = false;
						}
					} else {
						console.error('[WhatsApp Setup] User cancelled login or did not authorize');
						toast.error('WhatsApp setup was cancelled');
						isConnectingWhatsApp = false;
						window.removeEventListener('message', handleWhatsAppCallback);
					}
				},
				{
					config_id: '563963783154974',
					response_type: 'code',
					override_default_response_type: true,
					// Add explicit redirect_uri to match server-side
					redirect_uri: redirectUri,
					display: 'page', // Force full page redirect mode for OAuth
					auth_type: 'rerequest', // Force fresh authentication
					extras: {
						setup: {},
						feature_type: 'whatsapp_embedded_signup',
						flow_info: { flow: 'whatsapp_embedded_signup' },
						session_info_version: '3'
					}
				}
			);
		} catch (error) {
			console.error('[WhatsApp Setup] Setup error:', error);
			toast.error('Failed to initiate WhatsApp Business setup');
			isConnectingWhatsApp = false;
			window.removeEventListener('message', handleWhatsAppCallback);
		}
	}

	// Separate async function to handle the auth code
	async function handleAuthCode(code: string, state: string) {
		try {
			const exchangeResponse = await fetch(
				`/api/whatsapp/callback?code=${encodeURIComponent(code)}&state=${encodeURIComponent(state)}`,
				{
					method: 'GET',
					headers: {
						'Content-Type': 'application/json'
					},
					credentials: 'include'
				}
			);

			if (!exchangeResponse.ok) {
				const errorData = await exchangeResponse.json();
				throw new Error(errorData.error || 'Failed to exchange token');
			}

			// Parse the response data
			const responseData = await exchangeResponse.json();
			console.log('[WhatsApp Setup] Exchange response:', responseData);

			// Update UI with the real phone number from the response
			if (responseData.phoneNumber) {
				whatsappNumber = responseData.phoneNumber;
			} else {
				// Use a default or placeholder if no phone number is returned
				whatsappNumber = 'Connected';
			}

			whatsappStatus = 'connected';
			toast.success('WhatsApp Business account connected successfully');
		} catch (error) {
			console.error('[WhatsApp Setup] Token exchange error:', error);
			toast.error(
				'Failed to complete WhatsApp setup: ' +
					(error instanceof Error ? error.message : 'Unknown error')
			);
			whatsappStatus = 'disconnected';
		} finally {
			isConnectingWhatsApp = false;
			window.removeEventListener('message', handleWhatsAppCallback);
		}
	}

	function handleWhatsAppCallback(event: MessageEvent) {
		console.log('[WhatsApp Callback] Received message event:', event);

		// Verify origin for security
		if (event.origin !== window.location.origin) {
			console.warn('[WhatsApp Callback] Origin mismatch:', {
				received: event.origin,
				expected: window.location.origin
			});
			return;
		}

		const { type, data, error } = event.data;
		console.log('[WhatsApp Callback] Message data:', { type, data, error });

		if (type === 'whatsapp_setup_complete') {
			console.log('[WhatsApp Callback] Setup complete:', data);
			whatsappNumber = data.phoneNumber;
			whatsappStatus = 'connected';
			toast.success('WhatsApp Business account connected successfully');
			isConnectingWhatsApp = false;
			// Remove event listener
			window.removeEventListener('message', handleWhatsAppCallback);
		} else if (type === 'whatsapp_setup_error') {
			console.error('[WhatsApp Callback] Setup error:', error);
			toast.error(`WhatsApp setup failed: ${error}`);
			whatsappStatus = 'disconnected';
			isConnectingWhatsApp = false;
			window.removeEventListener('message', handleWhatsAppCallback);
		}
	}

	async function disconnectWhatsApp() {
		try {
			const response = await fetch(`/api/whatsapp/disconnect?companyId=${companyId}`, {
				method: 'POST'
			});

			const data = await response.json();

			if (data.success) {
				whatsappNumber = '';
				whatsappStatus = 'disconnected';
				toast.success('WhatsApp Business account disconnected successfully');
			} else {
				throw new Error(data.error || 'Failed to disconnect WhatsApp');
			}
		} catch (error) {
			console.error('WhatsApp disconnect error:', error);
			toast.error('Failed to disconnect WhatsApp Business account');
		}
	}
</script>

<div class="container mx-auto p-6 space-y-8">
	<!-- Page Header with Breadcrumbs -->
	<div class="flex flex-col space-y-2">
		<nav class="flex items-center text-sm text-muted-foreground">
			<a href="/company" class="hover:text-foreground">Companies</a>
			<svg
				xmlns="http://www.w3.org/2000/svg"
				width="16"
				height="16"
				viewBox="0 0 24 24"
				fill="none"
				stroke="currentColor"
				stroke-width="2"
				stroke-linecap="round"
				stroke-linejoin="round"
				class="mx-2 h-4 w-4"
			>
				<polyline points="9 18 15 12 9 6" />
			</svg>
			<span class="font-medium text-foreground"
				>{$page.params.companyId ? 'Company Settings' : 'Company Settings'}</span
			>
		</nav>
		<div class="flex items-center justify-between">
			<h1 class="text-3xl font-bold tracking-tight">Company Settings</h1>
			<div class="flex items-center gap-2">
				<Button href="/company/{companyId}" variant="outline">Back to Dashboard</Button>
			</div>
		</div>
		<p class="text-muted-foreground max-w-2xl">
			Manage your company configuration, integrations, and branding. Enable products, connect
			communication channels, and customize your company appearance.
		</p>
		<Separator class="my-2" />
	</div>

	<!-- Quick Actions Panel -->
	<div class="grid gap-4 md:grid-cols-3">
		<Card class="flex flex-col">
			<CardHeader class="pb-3">
				<CardTitle class="text-lg">Products & Integrations</CardTitle>
				<CardDescription>
					Enable document storage, communication platforms, and AI tools.
				</CardDescription>
			</CardHeader>
			<CardContent class="pt-0 pb-0 flex-grow"></CardContent>
			<CardFooter class="mt-auto pt-4">
				<Button href="/company/{companyId}/products" variant="default" class="w-full">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						width="16"
						height="16"
						viewBox="0 0 24 24"
						fill="none"
						stroke="currentColor"
						stroke-width="2"
						stroke-linecap="round"
						stroke-linejoin="round"
						class="mr-2"
					>
						<rect x="2" y="4" width="20" height="16" rx="2" />
						<path d="M7 15h0M7 11h0M11 15h0M11 11h0M15 15h0M15 11h0" />
					</svg>
					Manage Products
				</Button>
			</CardFooter>
		</Card>

		<Card class="flex flex-col">
			<CardHeader class="pb-3">
				<CardTitle class="text-lg">Document Vault</CardTitle>
				<CardDescription>
					Access and manage your connected document storage solutions.
				</CardDescription>
			</CardHeader>
			<CardContent class="pt-0 pb-0 flex-grow"></CardContent>
			<CardFooter class="mt-auto pt-4">
				<Button href="/company/{companyId}/vault" variant="default" class="w-full">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						width="16"
						height="16"
						viewBox="0 0 24 24"
						fill="none"
						stroke="currentColor"
						stroke-width="2"
						stroke-linecap="round"
						stroke-linejoin="round"
						class="mr-2"
					>
						<path d="M21 8V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v3" />
						<path d="M21 16v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-3" />
						<path d="M4 12a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1v-1Z" />
					</svg>
					Go to Vault
				</Button>
			</CardFooter>
		</Card>

		<Card class="flex flex-col">
			<CardHeader class="pb-3">
				<CardTitle class="text-lg">User Management</CardTitle>
				<CardDescription>Manage company members, roles and permissions.</CardDescription>
			</CardHeader>
			<CardContent class="pt-0 pb-0 flex-grow"></CardContent>
			<CardFooter class="mt-auto pt-4">
				<Button href="/company/{companyId}/users" variant="default" class="w-full">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						width="16"
						height="16"
						viewBox="0 0 24 24"
						fill="none"
						stroke="currentColor"
						stroke-width="2"
						stroke-linecap="round"
						stroke-linejoin="round"
						class="mr-2"
					>
						<path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
						<circle cx="9" cy="7" r="4" />
						<path d="M22 21v-2a4 4 0 0 0-3-3.87" />
						<path d="M16 3.13a4 4 0 0 1 0 7.75" />
					</svg>
					Manage Users
				</Button>
			</CardFooter>
		</Card>
	</div>

	<!-- Main Settings Content with Tabs -->
	<Tabs.Root
		value="branding"
		onValueChange={(value) => {
			// Ensure value is a string, using defaultTab as fallback
			activeTab = typeof value === 'string' ? value : defaultTab;
		}}
	>
		<Tabs.List class="grid grid-cols-2 w-full max-w-lg">
			<Tabs.Trigger value="branding" class="flex-1">
				<svg
					xmlns="http://www.w3.org/2000/svg"
					width="16"
					height="16"
					viewBox="0 0 24 24"
					fill="none"
					stroke="currentColor"
					stroke-width="2"
					stroke-linecap="round"
					stroke-linejoin="round"
					class="mr-2"
				>
					<path d="M9 3H3v18h18V9L9 3z" />
					<path d="M9 3v6H3" />
					<circle cx="9" cy="15" r="2" />
					<path d="m15 12-2 3 2 3 2-3-2-3Z" />
				</svg>
				Branding
			</Tabs.Trigger>
			<Tabs.Trigger value="communication" class="flex-1">
				<svg
					xmlns="http://www.w3.org/2000/svg"
					width="16"
					height="16"
					viewBox="0 0 24 24"
					fill="none"
					stroke="currentColor"
					stroke-width="2"
					stroke-linecap="round"
					stroke-linejoin="round"
					class="mr-2"
				>
					<path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
				</svg>
				Communication
			</Tabs.Trigger>
		</Tabs.List>

		<!-- Branding Tab -->
		<Tabs.Content value="branding" class="mt-6">
			<div class="max-w-3xl">
				<Card>
					<CardHeader>
						<CardTitle>Company Branding</CardTitle>
						<CardDescription>
							Upload and manage your company's visual identity. The main logo will be used for
							primary branding, while the small logo appears in navigation and compact displays.
						</CardDescription>
					</CardHeader>
					<CardContent>
						<form
							method="POST"
							action="?/updateLogos"
							enctype="multipart/form-data"
							on:submit|preventDefault={handleLogoSubmit}
							class="space-y-6"
						>
							<!-- Branding Logo -->
							<div class="space-y-4">
								<div>
									<Label for="brandingLogo" class="text-base font-medium">Main Logo</Label>
									<p class="text-sm text-muted-foreground mb-3">
										Used in headers and main branding areas. Recommended size: 300x100px.
									</p>
								</div>

								<div class="flex items-center gap-4 p-5 border rounded-lg bg-card">
									{#if brandingLogoPreview}
										<div class="relative w-48">
											<img
												src={brandingLogoPreview}
												alt="Branding Logo Preview"
												class="object-contain w-full h-20"
											/>
											<Button
												variant="destructive"
												size="icon"
												class="absolute w-6 h-6 -top-2 -right-2"
												on:click={() => {
													brandingLogoPreview = '';
													if (brandingLogoInputElement) brandingLogoInputElement.value = '';
													// Submit form with null value to clear the logo
													const formData = new FormData();
													formData.append('brandingLogo', '');
													fetch('?/updateLogos', {
														method: 'POST',
														body: formData
													});
												}}
											>
												<svg
													xmlns="http://www.w3.org/2000/svg"
													width="14"
													height="14"
													viewBox="0 0 24 24"
													fill="none"
													stroke="currentColor"
													stroke-width="2"
													stroke-linecap="round"
													stroke-linejoin="round"
													><path d="M18 6 6 18" /><path d="m6 6 12 12" /></svg
												>
											</Button>
										</div>
									{:else}
										<div class="flex items-center justify-center w-48 h-20 rounded bg-muted">
											<svg
												xmlns="http://www.w3.org/2000/svg"
												width="24"
												height="24"
												viewBox="0 0 24 24"
												fill="none"
												stroke="currentColor"
												stroke-width="2"
												stroke-linecap="round"
												stroke-linejoin="round"
												class="text-muted-foreground"
												><rect width="18" height="18" x="3" y="3" rx="2" ry="2" /><circle
													cx="9"
													cy="9"
													r="2"
												/><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21" /></svg
											>
										</div>
									{/if}
									<div class="flex-1 space-y-2">
										<input
											type="file"
											name="brandingLogo"
											id="brandingLogo"
											accept="image/jpeg,image/png,image/gif,image/webp"
											on:change={(e) => handleLogoFileSelect(e, 'branding')}
											class="block w-full border border-gray-300 rounded-md text-sm file:mr-4 file:py-2 file:px-4
												file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary
												file:text-white hover:file:bg-primary/90"
											bind:this={brandingLogoInputElement}
										/>
										<p class="text-xs text-muted-foreground">
											Max size: 5MB. Supported formats: JPEG, PNG, GIF, WEBP
										</p>
									</div>
								</div>

								<div>
									<Label for="smallLogo" class="text-base font-medium">Icon Logo</Label>
									<p class="text-sm text-muted-foreground mb-3">
										Used in navigation and compact displays. Recommended size: 50x50px.
									</p>
								</div>

								<div class="flex items-center gap-4 p-5 border rounded-lg bg-card">
									{#if smallLogoPreview}
										<div class="relative w-24">
											<img
												src={smallLogoPreview}
												alt="Small Logo Preview"
												class="object-contain w-full h-10"
											/>
											<Button
												variant="destructive"
												size="icon"
												class="absolute w-6 h-6 -top-2 -right-2"
												on:click={() => {
													smallLogoPreview = '';
													if (smallLogoInputElement) smallLogoInputElement.value = '';
													// Submit form with null value to clear the logo
													const formData = new FormData();
													formData.append('smallLogo', '');
													fetch('?/updateLogos', {
														method: 'POST',
														body: formData
													});
												}}
											>
												<svg
													xmlns="http://www.w3.org/2000/svg"
													width="14"
													height="14"
													viewBox="0 0 24 24"
													fill="none"
													stroke="currentColor"
													stroke-width="2"
													stroke-linecap="round"
													stroke-linejoin="round"
													><path d="M18 6 6 18" /><path d="m6 6 12 12" /></svg
												>
											</Button>
										</div>
									{:else}
										<div class="flex items-center justify-center w-24 h-10 rounded bg-muted">
											<svg
												xmlns="http://www.w3.org/2000/svg"
												width="20"
												height="20"
												viewBox="0 0 24 24"
												fill="none"
												stroke="currentColor"
												stroke-width="2"
												stroke-linecap="round"
												stroke-linejoin="round"
												class="text-muted-foreground"
												><rect width="18" height="18" x="3" y="3" rx="2" ry="2" /><circle
													cx="9"
													cy="9"
													r="2"
												/><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21" /></svg
											>
										</div>
									{/if}
									<div class="flex-1 space-y-2">
										<input
											type="file"
											name="smallLogo"
											id="smallLogo"
											accept="image/jpeg,image/png,image/gif,image/webp"
											on:change={(e) => handleLogoFileSelect(e, 'small')}
											class="block w-full border border-gray-300 rounded-md text-sm file:mr-4 file:py-2 file:px-4
												file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary
												file:text-white hover:file:bg-primary/90"
											bind:this={smallLogoInputElement}
										/>
										<p class="text-xs text-muted-foreground">
											Max size: 5MB. Supported formats: JPEG, PNG, GIF, WEBP
										</p>
									</div>
								</div>
							</div>

							<div class="flex justify-end">
								<Button type="submit" disabled={isUploadingLogos}>
									{#if isUploadingLogos}
										<svg
											class="w-4 h-4 mr-2 animate-spin"
											xmlns="http://www.w3.org/2000/svg"
											fill="none"
											viewBox="0 0 24 24"
										>
											<circle
												class="opacity-25"
												cx="12"
												cy="12"
												r="10"
												stroke="currentColor"
												stroke-width="4"
											></circle>
											<path
												class="opacity-75"
												fill="currentColor"
												d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
											></path>
										</svg>
										Uploading...
									{:else}
										<svg
											xmlns="http://www.w3.org/2000/svg"
											width="16"
											height="16"
											viewBox="0 0 24 24"
											fill="none"
											stroke="currentColor"
											stroke-width="2"
											stroke-linecap="round"
											stroke-linejoin="round"
											class="mr-2"
										>
											<path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
											<polyline points="17 8 12 3 7 8" />
											<line x1="12" x2="12" y1="3" y2="15" />
										</svg>
										Save Logos
									{/if}
								</Button>
							</div>
						</form>
					</CardContent>
				</Card>
			</div>
		</Tabs.Content>

		<!-- Communication Tab -->
		<Tabs.Content value="communication" class="mt-6">
			<div class="grid gap-6 md:grid-cols-2 max-w-4xl">
				<!-- Show message if no communication products are enabled at all -->
				{#if !isWhatsAppEnabled && !isSlackEnabled && !enabledProducts?.webchat}
					<div
						class="md:col-span-2 p-6 rounded-lg border border-dashed flex flex-col items-center justify-center"
					>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							width="24"
							height="24"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							stroke-width="2"
							stroke-linecap="round"
							stroke-linejoin="round"
							class="text-muted-foreground mb-2"
						>
							<path d="M16 16v2a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
							<path d="M11.25 9h.01"></path>
							<path d="M13.5 9h.01"></path>
							<path d="M15.75 9h.01"></path>
							<path d="M12 13a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1v-3Z"
							></path>
						</svg>
						<h3 class="font-medium text-lg mb-1">Communication Products Not Enabled</h3>
						<p class="text-sm text-muted-foreground text-center mb-4">
							You don't have any communication products enabled for this company.
						</p>
						<Button href="/company/{companyId}/products" variant="outline">
							<svg
								xmlns="http://www.w3.org/2000/svg"
								width="16"
								height="16"
								viewBox="0 0 24 24"
								fill="none"
								stroke="currentColor"
								stroke-width="2"
								stroke-linecap="round"
								stroke-linejoin="round"
								class="mr-2"
							>
								<path d="M17 14V4"></path>
								<path d="M11 4v16"></path>
								<path d="M17 20v-2"></path>
								<path d="M4 12a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v6a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1v-6Z"
								></path>
								<path d="M14 12a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1v-2Z"
								></path>
								<path d="M4 6a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V6Z"
								></path>
							</svg>
							Manage Products
						</Button>
					</div>
				{/if}

				<!-- Webchat Integration - always displayed -->
				<Card class="md:col-span-1">
					<CardHeader>
						<div class="flex items-center gap-2">
							<svg
								xmlns="http://www.w3.org/2000/svg"
								width="20"
								height="20"
								viewBox="0 0 24 24"
								fill="none"
								stroke="currentColor"
								stroke-width="2"
								stroke-linecap="round"
								stroke-linejoin="round"
								class="text-blue-500"
							>
								<path d="M14 9a2 2 0 0 1-2 2H6l-4 4V4c0-1.1.9-2 2-2h8a2 2 0 0 1 2 2v5Z" />
								<path d="M18 9h2a2 2 0 0 1 2 2v11l-4-4h-6a2 2 0 0 1-2-2v-1" />
							</svg>
							<CardTitle>Webchat</CardTitle>
						</div>
						<CardDescription>
							Configure your website chat for customer support and messages
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div class="space-y-4">
							<div class="p-4 border rounded-lg bg-muted/50">
								<div class="flex items-center justify-between">
									<div>
										<p class="font-medium">Status</p>
									</div>
									<div class="flex items-center gap-2">
										<span class="inline-block w-2 h-2 bg-green-500 rounded-full"></span>
										<span class="text-sm text-muted-foreground">Active</span>
									</div>
								</div>
							</div>

							<div class="p-4 border rounded-lg bg-muted/50">
								<h3 class="mb-2 font-medium">Features</h3>
								<ul class="space-y-2 text-sm text-muted-foreground">
									<li class="flex items-center gap-2">
										<svg
											xmlns="http://www.w3.org/2000/svg"
											width="16"
											height="16"
											viewBox="0 0 24 24"
											fill="none"
											stroke="currentColor"
											stroke-width="2"
											stroke-linecap="round"
											stroke-linejoin="round"
											class="text-green-500"
										>
											<polyline points="20 6 9 17 4 12" />
										</svg>
										Live customer support
									</li>
									<li class="flex items-center gap-2">
										<svg
											xmlns="http://www.w3.org/2000/svg"
											width="16"
											height="16"
											viewBox="0 0 24 24"
											fill="none"
											stroke="currentColor"
											stroke-width="2"
											stroke-linecap="round"
											stroke-linejoin="round"
											class="text-green-500"
										>
											<polyline points="20 6 9 17 4 12" />
										</svg>
										File sharing
									</li>
									<li class="flex items-center gap-2">
										<svg
											xmlns="http://www.w3.org/2000/svg"
											width="16"
											height="16"
											viewBox="0 0 24 24"
											fill="none"
											stroke="currentColor"
											stroke-width="2"
											stroke-linecap="round"
											stroke-linejoin="round"
											class="text-green-500"
										>
											<polyline points="20 6 9 17 4 12" />
										</svg>
										Automated responses
									</li>
								</ul>
							</div>

							<Button variant="default" class="w-full">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="16"
									height="16"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									stroke-width="2"
									stroke-linecap="round"
									stroke-linejoin="round"
									class="mr-2"
								>
									<path d="M12 20h9" />
									<path d="M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z" />
								</svg>
								Configure Chat
							</Button>
						</div>
					</CardContent>
				</Card>

				<!-- WhatsApp Integration - only show if enabled -->
				{#if isWhatsAppEnabled}
					<Card class="md:col-span-1">
						<CardHeader>
							<div class="flex items-center gap-2">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="20"
									height="20"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									stroke-width="2"
									stroke-linecap="round"
									stroke-linejoin="round"
									class="text-green-500"
								>
									<path d="M3 21l1.65-3.8a9 9 0 1 1 3.4 2.9L3 21" />
									<path d="M9 10a.5.5 0 0 0 1 0V9a.5.5 0 0 0-1 0v1Z" />
									<path d="M14 10a.5.5 0 0 0 1 0V9a.5.5 0 0 0-1 0v1Z" />
									<path d="M9.5 13.5c.5 1 1.5 2.5 2.5 2.5s2-1.5 2.5-2.5" />
								</svg>
								<CardTitle>WhatsApp Business</CardTitle>
							</div>
							<CardDescription>
								Connect your WhatsApp Business account to enable customer messaging
							</CardDescription>
						</CardHeader>
						<CardContent>
							<div class="space-y-4">
								{#if whatsappStatus === 'connected'}
									<div class="p-4 border rounded-lg bg-muted/50">
										<div class="flex items-center justify-between">
											<div>
												<p class="font-medium">Connected Number</p>
												<p class="text-sm text-muted-foreground">{whatsappNumber}</p>
											</div>
											<div class="flex items-center gap-2">
												<span class="inline-block w-2 h-2 bg-green-500 rounded-full"></span>
												<span class="text-sm text-muted-foreground">Active</span>
											</div>
										</div>
									</div>
									<div class="space-y-2">
										<Button variant="destructive" class="w-full" on:click={disconnectWhatsApp}>
											<svg
												xmlns="http://www.w3.org/2000/svg"
												width="16"
												height="16"
												viewBox="0 0 24 24"
												fill="none"
												stroke="currentColor"
												stroke-width="2"
												stroke-linecap="round"
												stroke-linejoin="round"
												class="mr-2"
											>
												<path d="m18 6-12 12" />
												<path d="m6 6 12 12" />
											</svg>
											Disconnect WhatsApp
										</Button>
										<p class="text-sm text-center text-muted-foreground">
											Disconnecting will stop all WhatsApp communications
										</p>
									</div>
								{:else}
									<div class="space-y-4">
										<div class="p-4 border rounded-lg bg-muted/50">
											<h3 class="mb-2 font-medium">Benefits of WhatsApp Business</h3>
											<ul class="space-y-2 text-sm text-muted-foreground">
												<li class="flex items-center gap-2">
													<svg
														xmlns="http://www.w3.org/2000/svg"
														width="16"
														height="16"
														viewBox="0 0 24 24"
														fill="none"
														stroke="currentColor"
														stroke-width="2"
														stroke-linecap="round"
														stroke-linejoin="round"
														class="text-green-500"><polyline points="20 6 9 17 4 12" /></svg
													>
													Direct customer messaging
												</li>
												<li class="flex items-center gap-2">
													<svg
														xmlns="http://www.w3.org/2000/svg"
														width="16"
														height="16"
														viewBox="0 0 24 24"
														fill="none"
														stroke="currentColor"
														stroke-width="2"
														stroke-linecap="round"
														stroke-linejoin="round"
														class="text-green-500"><polyline points="20 6 9 17 4 12" /></svg
													>
													Automated responses
												</li>
												<li class="flex items-center gap-2">
													<svg
														xmlns="http://www.w3.org/2000/svg"
														width="16"
														height="16"
														viewBox="0 0 24 24"
														fill="none"
														stroke="currentColor"
														stroke-width="2"
														stroke-linecap="round"
														stroke-linejoin="round"
														class="text-green-500"><polyline points="20 6 9 17 4 12" /></svg
													>
													Business profile & catalog
												</li>
											</ul>
										</div>
										<Button
											variant="default"
											class="w-full"
											disabled={isConnectingWhatsApp}
											on:click={initiateWhatsAppSetup}
										>
											{#if isConnectingWhatsApp}
												<svg
													class="w-5 h-5 mr-3 -ml-1 text-white animate-spin"
													xmlns="http://www.w3.org/2000/svg"
													fill="none"
													viewBox="0 0 24 24"
												>
													<circle
														class="opacity-25"
														cx="12"
														cy="12"
														r="10"
														stroke="currentColor"
														stroke-width="4"
													></circle>
													<path
														class="opacity-75"
														fill="currentColor"
														d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
													></path>
												</svg>
												Connecting...
											{:else}
												<svg
													xmlns="http://www.w3.org/2000/svg"
													width="16"
													height="16"
													viewBox="0 0 24 24"
													fill="none"
													stroke="currentColor"
													stroke-width="2"
													stroke-linecap="round"
													stroke-linejoin="round"
													class="mr-2 text-white"
												>
													<path d="M3 21l1.65-3.8a9 9 0 1 1 3.4 2.9L3 21" />
													<path d="M9 10a.5.5 0 0 0 1 0V9a.5.5 0 0 0-1 0v1Z" />
													<path d="M14 10a.5.5 0 0 0 1 0V9a.5.5 0 0 0-1 0v1Z" />
													<path d="M9.5 13.5c.5 1 1.5 2.5 2.5 2.5s2-1.5 2.5-2.5" />
												</svg>
												Connect WhatsApp Business
											{/if}
										</Button>
										<p class="text-sm text-center text-muted-foreground">
											You'll be redirected to WhatsApp Business to complete setup
										</p>
									</div>
								{/if}
							</div>
						</CardContent>
					</Card>
				{/if}

				<!-- Slack Integration - only show if enabled -->
				{#if isSlackEnabled}
					<Card class="md:col-span-1">
						<CardHeader>
							<div class="flex items-center gap-2">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="20"
									height="20"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									stroke-width="2"
									stroke-linecap="round"
									stroke-linejoin="round"
									class="text-purple-500"
								>
									<rect width="3" height="8" x="13" y="2" rx="1.5" />
									<path d="M19 8.5V10h1.5A1.5 1.5 0 1 0 19 8.5" />
									<rect width="3" height="8" x="8" y="14" rx="1.5" />
									<path d="M5 15.5V14H3.5A1.5 1.5 0 1 0 5 15.5" />
									<rect width="8" height="3" x="14" y="13" rx="1.5" />
									<path d="M15.5 19H14v1.5a1.5 1.5 0 1 0 1.5-1.5" />
									<rect width="8" height="3" x="2" y="8" rx="1.5" />
									<path d="M8.5 5H10V3.5A1.5 1.5 0 1 0 8.5 5" />
								</svg>
								<CardTitle>Slack Integration</CardTitle>
							</div>
							<CardDescription>
								Manage your connected Slack workspace for team messaging
							</CardDescription>
						</CardHeader>
						<CardContent>
							<div class="space-y-4">
								<div class="p-4 border rounded-lg bg-muted/50">
									<div class="flex items-center justify-between mb-3">
										<div>
											<p class="font-medium">Workspace Status</p>
										</div>
										<div class="flex items-center gap-2">
											{#if slackWorkspace}
												<span class="inline-block w-2 h-2 bg-green-500 rounded-full"></span>
												<span class="text-sm text-muted-foreground">Connected</span>
											{:else}
												<span class="inline-block w-2 h-2 bg-yellow-500 rounded-full"></span>
												<span class="text-sm text-muted-foreground">Not Connected</span>
											{/if}
										</div>
									</div>

									<Label for="workspaceId" class="mb-2 block">Slack Workspace</Label>
									<Input
										id="workspaceId"
										type="text"
										value={slackWorkspace || 'No workspace connected'}
										disabled
										title="Please contact support if you need to change your Slack workspace"
										class={`cursor-not-allowed ${!slackWorkspace ? 'text-muted-foreground' : ''}`}
									/>
								</div>

								<p class="text-sm text-muted-foreground">
									Your Slack workspace integration is managed by administrators. Please contact
									support to update your Slack integration.
								</p>

								<Button variant="outline" class="w-full" disabled>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										width="16"
										height="16"
										viewBox="0 0 24 24"
										fill="none"
										stroke="currentColor"
										stroke-width="2"
										stroke-linecap="round"
										stroke-linejoin="round"
										class="mr-2"
									>
										<rect width="16" height="16" x="4" y="4" rx="2" />
										<rect width="4" height="4" x="10" y="10" />
										<path d="M4 10h4" />
										<path d="M16 10h4" />
										<path d="M10 4v4" />
										<path d="M10 16v4" />
									</svg>
									Contact Support
								</Button>
							</div>
						</CardContent>
					</Card>
				{/if}
			</div>
		</Tabs.Content>
	</Tabs.Root>
</div>
