import type { PageServerLoad } from './$types';
import { generateCsrfToken } from '$lib/utils/security';
import { error, fail, json } from '@sveltejs/kit';
import type { Actions, RequestEvent } from './$types';

export const load: PageServerLoad = async ({ locals: { supabase, user }, params }) => {
	const { companyId } = params;

	// Fetch existing communication channels for the company
	const { data: channels, error: channelsError } = await supabase
		.from('communication_channels')
		.select('*')
		.eq('company_id', companyId);
	console.log('[Channels]', channels);
	if (channelsError) {
		console.error('Error fetching channels:', channelsError);
		return { user, channels: [] };
	}

	// Fetch user's platform profiles
	const { data: platformProfiles, error: profilesError } = await supabase
		.from('platform_profiles')
		.select('*')
		.eq('user_id', user?.id);

	if (profilesError) {
		console.error('Error fetching platform profiles:', profilesError);
	}

	// Fetch company personalization data
	const { data: personalization, error: personalizationError } = await supabase
		.from('company_personalization')
		.select('*')
		.eq('company_id', companyId)
		.single();

	if (personalizationError && personalizationError.code !== 'PGRST116') {
		console.error('Error fetching company personalization:', personalizationError);
	}

	// Fetch enabled company products
	const { data: companyProducts, error: productsError } = await supabase
		.from('company_products')
		.select('*, products(*)')
		.eq('company_id', companyId)
		.eq('is_enabled', true);

	if (productsError) {
		console.error('Error fetching company products:', productsError);
	}

	// Create a map of enabled products by name for easy lookup
	const enabledProducts = new Map();
	companyProducts?.forEach((product) => {
		if (product.products) {
			enabledProducts.set(product.products.name.toLowerCase(), true);
		}
	});

	return {
		user,
		channels: channels || [],
		platformProfiles: platformProfiles || [],
		companyId,
		personalization: personalization || null,
		enabledProducts: Object.fromEntries(enabledProducts) || {}
	};
};

export const actions: Actions = {
	initWhatsAppAuth: async ({ cookies, params }) => {
		try {
			const { companyId } = params;

			// Generate CSRF token
			const csrfToken = generateCsrfToken();
			console.log('[WhatsApp Auth] Generated CSRF token:', csrfToken);

			// Set cookies with proper security settings
			cookies.set('fb_redirect_company_id', companyId, {
				path: '/',
				secure: true,
				sameSite: 'lax',
				maxAge: 3600, // 1 hour
				httpOnly: false // Need to access this in client-side code
			});

			cookies.set('fb_csrf_token', csrfToken, {
				path: '/',
				secure: true,
				sameSite: 'lax',
				maxAge: 3600,
				httpOnly: false // Need to access this in client-side code
			});

			console.log('[WhatsApp Auth] Cookies set:', {
				companyId,
				csrfToken,
				timestamp: new Date().toISOString()
			});

			return {
				success: true,
				csrfToken
			};
		} catch (error) {
			console.error('[WhatsApp Auth] Error initializing:', error);
			return fail(500, {
				success: false,
				error: 'Failed to initialize WhatsApp authentication'
			});
		}
	},

	updateWhatsApp: async ({ request, locals: { supabase }, params }) => {
		const formData = await request.formData();
		const phoneNumber = formData.get('phoneNumber')?.toString();
		const { companyId } = params;

		if (!phoneNumber) {
			return fail(400, { success: false, error: 'Phone number is required' });
		}

		const { error: updateError } = await supabase
			.from('communication_channels')
			.upsert({
				company_id: companyId,
				type: 'whatsapp',
				number: phoneNumber,
				default_number: true
			})
			.eq('company_id', companyId)
			.eq('type', 'whatsapp');

		if (updateError) {
			console.error('Error updating WhatsApp:', updateError);
			return fail(500, { success: false, error: updateError.message });
		}

		return { success: true };
	},

	updateSlack: async ({ request, locals: { supabase }, params }) => {
		const formData = await request.formData();
		const workspaceId = formData.get('workspaceId')?.toString();
		const { companyId } = params;

		if (!workspaceId) {
			return fail(400, { success: false, error: 'Workspace ID is required' });
		}

		const { error: updateError } = await supabase
			.from('communication_channels')
			.upsert({
				company_id: companyId,
				type: 'slack',
				slack_workspace_id: workspaceId
			})
			.eq('company_id', companyId)
			.eq('type', 'slack');

		if (updateError) {
			console.error('Error updating Slack:', updateError);
			return fail(500, { success: false, error: updateError.message });
		}

		return { success: true };
	},

	updateWhatsAppDetails: async ({ request, locals: { supabase }, params }: RequestEvent) => {
		try {
			const formData = await request.formData();
			const phoneNumberId = formData.get('phoneNumberId')?.toString();
			const wabaId = formData.get('wabaId')?.toString();
			const { companyId } = params;

			if (!phoneNumberId || !wabaId) {
				return fail(400, {
					success: false,
					error: 'Phone number ID and WABA ID are required'
				});
			}

			const { error: updateError } = await supabase
				.from('communication_channels')
				.upsert({
					company_id: companyId,
					type: 'whatsapp',
					phone_number_id: phoneNumberId,
					waba_id: wabaId,
					status: 'active'
				})
				.eq('company_id', companyId)
				.eq('type', 'whatsapp');

			if (updateError) {
				console.error('Error updating WhatsApp details:', updateError);
				return fail(500, { success: false, error: updateError.message });
			}

			return { success: true };
		} catch (error) {
			console.error('Error in updateWhatsAppDetails:', error);
			return fail(500, {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error'
			});
		}
	},

	updateLogos: async ({ request, locals: { supabaseAdmin }, params }) => {
		try {
			const formData = await request.formData();
			const brandingLogo = formData.get('brandingLogo') as File | null;
			const smallLogo = formData.get('smallLogo') as File | null;
			const { companyId } = params;

			// Validate file types only for provided files
			const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
			const maxSize = 5 * 1024 * 1024; // 5MB

			if (
				brandingLogo?.size > 0 &&
				(!allowedTypes.includes(brandingLogo.type) || brandingLogo.size > maxSize)
			) {
				return fail(400, {
					success: false,
					error: 'Branding logo must be an image file (JPEG, PNG, GIF, WEBP) under 5MB'
				});
			}

			if (
				smallLogo?.size > 0 &&
				(!allowedTypes.includes(smallLogo.type) || smallLogo.size > maxSize)
			) {
				return fail(400, {
					success: false,
					error: 'Small logo must be an image file (JPEG, PNG, GIF, WEBP) under 5MB'
				});
			}

			let brandingLogoPath = null;
			let smallLogoPath = null;

			// Upload branding logo if provided
			if (brandingLogo?.size > 0) {
				console.log('[Logo Upload] Starting branding logo upload:', {
					size: brandingLogo.size,
					type: brandingLogo.type,
					name: brandingLogo.name
				});

				const brandingLogoBuffer = await brandingLogo.arrayBuffer();
				const brandingLogoExt = brandingLogo.type.split('/')[1];
				const brandingLogoFilename = `${companyId}/branding-logo.${brandingLogoExt}`;

				console.log('[Logo Upload] Uploading branding logo:', {
					filename: brandingLogoFilename,
					bufferSize: brandingLogoBuffer.byteLength
				});

				const { data: uploadData, error: uploadError } = await supabaseAdmin.storage
					.from('company-assets')
					.upload(brandingLogoFilename, brandingLogoBuffer, {
						contentType: brandingLogo.type,
						upsert: true
					});

				console.log('[Logo Upload] Upload result:', {
					uploadData,
					uploadError
				});

				if (uploadError) {
					console.error('Error uploading branding logo:', uploadError);
					return fail(500, {
						success: false,
						error: 'Failed to upload branding logo'
					});
				}

				const { data: urlData } = supabaseAdmin.storage
					.from('company-assets')
					.getPublicUrl(brandingLogoFilename);

				console.log('[Logo Upload] Generated public URL:', urlData);

				// Add timestamp to prevent caching
				const timestamp = Date.now();
				brandingLogoPath = `${urlData.publicUrl}?t=${timestamp}`;
			}

			// Upload small logo if provided
			if (smallLogo?.size > 0) {
				console.log('[Logo Upload] Starting small logo upload:', {
					size: smallLogo.size,
					type: smallLogo.type,
					name: smallLogo.name
				});

				const smallLogoBuffer = await smallLogo.arrayBuffer();
				const smallLogoExt = smallLogo.type.split('/')[1];
				const smallLogoFilename = `${companyId}/small-logo.${smallLogoExt}`;

				console.log('[Logo Upload] Uploading small logo:', {
					filename: smallLogoFilename,
					bufferSize: smallLogoBuffer.byteLength
				});

				const { data: uploadData, error: uploadError } = await supabaseAdmin.storage
					.from('company-assets')
					.upload(smallLogoFilename, smallLogoBuffer, {
						contentType: smallLogo.type,
						upsert: true
					});

				console.log('[Logo Upload] Upload result:', {
					uploadData,
					uploadError
				});

				if (uploadError) {
					console.error('Error uploading small logo:', uploadError);
					return fail(500, {
						success: false,
						error: 'Failed to upload small logo'
					});
				}

				const { data: urlData } = supabaseAdmin.storage
					.from('company-assets')
					.getPublicUrl(smallLogoFilename);

				console.log('[Logo Upload] Generated public URL:', urlData);

				// Add timestamp to prevent caching
				const timestamp = Date.now();
				smallLogoPath = `${urlData.publicUrl}?t=${timestamp}`;
			}

			// Only include paths in updateData if they were actually uploaded or clearing was requested
			const updateData: any = {
				p_company_id: companyId,
				p_branding_logo:
					brandingLogo === '' ? null : brandingLogo?.size > 0 ? brandingLogoPath : undefined,
				p_small_logo: smallLogo === '' ? null : smallLogo?.size > 0 ? smallLogoPath : undefined
			};

			console.log('[Logo Update] Calling RPC with data:', updateData);

			const { data: rpcResult, error: updateError } = await supabaseAdmin.rpc(
				'handle_company_personalization_upsert',
				updateData
			);

			console.log('[Logo Update] RPC result:', {
				rpcResult,
				error: updateError
			});

			if (updateError) {
				console.error('Error updating company personalization:', updateError);
				return fail(500, { success: false, error: updateError.message });
			}

			// Return in the format SvelteKit expects for form actions
			return {
				type: 'success',
				status: 200,
				data: {
					success: true,
					brandingLogo: brandingLogoPath,
					smallLogo: smallLogoPath
				}
			};
		} catch (error) {
			console.error('Error in updateLogos:', error);
			return fail(500, {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error'
			});
		}
	},

	toggleProduct: async ({ request, locals: { supabase }, params }) => {
		const { productId, enabled } = await request.json();
		const { companyId } = params;

		if (!productId || typeof enabled !== 'boolean') {
			return fail(400, { success: false, error: 'Invalid request data' });
		}

		const { error: updateError } = await supabase
			.from('company_products')
			.upsert({
				company_id: companyId,
				product_id: productId,
				is_enabled: enabled
			})
			.eq('company_id', companyId)
			.eq('product_id', productId);

		if (updateError) {
			console.error('Error updating product status:', updateError);
			return fail(500, { success: false, error: updateError.message });
		}

		return { success: true };
	}
};
