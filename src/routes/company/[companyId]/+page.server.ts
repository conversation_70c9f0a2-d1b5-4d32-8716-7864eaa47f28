import { redirect } from "@sveltejs/kit";
import type { PageServerLoad } from "./$types";
import { supabaseAdmin } from "$lib/server/supabaseAdmin";

export const load: PageServerLoad = async ({
	depends,
	params,
	locals: { supabase, user, userCompanies },
	url,
}) => {
	depends("supabase:db:companies");
	const { companyId } = params;

	// Verify user is authenticated
	if (!user) {
		throw redirect(303, "/auth");
	}

	// Verify user has access to this company
	const hasAccess = userCompanies.some((company) => company.id === companyId);
	if (!hasAccess) {
		console.error("Access denied: User not a member of this company", {
			userId: user.id,
			companyId,
		});
		throw redirect(303, "/company");
	}

	// Fetch company personalization data
	const { data: personalization, error: personalizationError } = await supabase
		.from("company_personalization")
		.select("*")
		.eq("company_id", companyId)
		.single();

	console.log("[Logo Retrieval] Personalization data:", {
		personalization,
		error: personalizationError,
	});

	// Get TOTM logo URL using admin client for storage operations
	const totmLogoUrl = supabaseAdmin.storage
		.from("public")
		.getPublicUrl("totm-logo.png")
		.data.publicUrl;

	console.log("[Logo Retrieval] TOTM logo URL:", totmLogoUrl);
	console.log(
		"[Logo Retrieval] Final logo to use:",
		personalization?.branding_logo || totmLogoUrl,
	);

	// Check for message parameter
	const message = url.searchParams.get("message");
	console.log("Company page load:", {
		message,
		userId: user?.id,
		companyId,
	});

	if (message && user) {
		// Create new session
		const { data: newSession, error: insertError } = await supabase
			.from("user_sessions")
			.insert({
				user_id: user.id,
				platform: "email",
				session_id: crypto.randomUUID(),
			})
			.select()
			.single();

		console.log("New session created:", newSession);

		if (insertError) {
			console.error("Session creation error:", insertError);
			throw new Error(`Failed to create session: ${insertError.message}`);
		}

		// Redirect to chat with message
		const redirectUrl =
			`/company/${companyId}/chat/${user.id}/${newSession.session_id}?message=${
				encodeURIComponent(message)
			}`;
		console.log("Redirecting to:", redirectUrl);
		throw redirect(303, redirectUrl);
	}

	return {
		user,
		companies: Array.isArray(userCompanies) ? userCompanies : [],
		companyId,
		personalization,
		totmLogoUrl,
	};
};
