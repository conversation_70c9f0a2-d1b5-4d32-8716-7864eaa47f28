import { json } from "@sveltejs/kit";
import type { RequestHand<PERSON> } from "./$types";

export const POST: RequestHandler = async (
  { request, locals: { supabase } },
) => {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return json({ error: "Unauthorized" }, { status: 401 });
    }

    const { questionId, responseText } = await request.json();

    if (!questionId || !responseText?.trim()) {
      return json({ error: "Missing required fields" }, { status: 400 });
    }

    // Insert the response
    const { error: insertError } = await supabase
      .from("totm_inbox_responses")
      .insert({
        question_id: questionId,
        expert_id: session.user.id,
        response_text: responseText,
      });

    if (insertError) {
      console.error("Error inserting response:", insertError);
      return json({ error: "Failed to save response" }, { status: 500 });
    }

    // Update the question status
    const { error: updateError } = await supabase
      .from("totm_inbox_questions")
      .update({ status: "responded" })
      .eq("id", questionId);

    if (updateError) {
      console.error("Error updating question status:", updateError);
      // Don't return error since response was saved successfully
    }

    return json({ success: true });
  } catch (error) {
    console.error("Error in response endpoint:", error);
    return json({ error: "Internal server error" }, { status: 500 });
  }
};
