import { json } from "@sveltejs/kit";
import { generateEmbedding } from "$lib/server/openai";
import type { RequestHandler } from "./$types";

/**
 * POST endpoint to generate an embedding for the provided text
 *
 * Request body:
 * {
 *   text: string; // The text to generate an embedding for
 * }
 *
 * Response:
 * {
 *   embedding: number[]; // The embedding vector
 * }
 */
export const POST: RequestHandler = async ({ request }) => {
  try {
    const body = await request.json();
    const { text } = body;

    if (!text) {
      return json({ error: "Text is required" }, { status: 400 });
    }

    const embedding = await generateEmbedding(text);

    return json({ embedding });
  } catch (error) {
    console.error("Error generating embedding:", error);
    const message = error instanceof Error
      ? error.message
      : "An unknown error occurred";
    return json({ error: message }, { status: 500 });
  }
};
