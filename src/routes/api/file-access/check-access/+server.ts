import { json } from "@sveltejs/kit";
import { filterFilesByUserAccess } from "$lib/server/file_access_functions";
import type { RequestHandler } from "./$types";

/**
 * POST endpoint to check if a user has access to a list of files
 *
 * Request body:
 * {
 *   userEmail: string; // The email of the user to check access for
 *   fileIds: string[]; // Array of file IDs to check access for
 * }
 *
 * Response:
 * {
 *   accessibleFileIds: string[]; // Array of file IDs the user has access to
 * }
 */
export const POST: RequestHandler = async ({ request, locals }) => {
  try {
    const body = await request.json();
    const { userEmail, fileIds } = body;

    // Validate required parameters
    if (!userEmail) {
      return json({ error: "userEmail is required" }, { status: 400 });
    }

    if (!fileIds || !Array.isArray(fileIds) || fileIds.length === 0) {
      return json({ error: "fileIds must be a non-empty array" }, {
        status: 400,
      });
    }

    // Get files that the user has access to
    const accessibleFileIds = await filterFilesByUserAccess(userEmail, fileIds);

    return json({
      accessibleFileIds,
      total: {
        checked: fileIds.length,
        accessible: accessibleFileIds.length,
      },
    });
  } catch (error) {
    console.error("Error checking file access:", error);
    return json({ error: "Failed to check file access" }, { status: 500 });
  }
};
