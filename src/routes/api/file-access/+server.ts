import { json } from "@sveltejs/kit";
import {
  getCompanyFiles,
  getFileAccessUsers,
} from "$lib/server/file_access_functions";
import type { RequestHandler } from "./$types";

// GET endpoint to retrieve all files for a company
export const GET: RequestHandler = async ({ url, locals }) => {
  try {
    // Extract company_id from query parameters
    const companyId = url.searchParams.get("company_id");

    if (!companyId) {
      return json({ error: "company_id is required" }, { status: 400 });
    }

    // Get files for the company
    const files = await getCompanyFiles(companyId);

    return json({ files });
  } catch (error) {
    console.error("Error fetching company files:", error);
    return json({ error: "Failed to fetch company files" }, { status: 500 });
  }
};

// POST endpoint to retrieve user access for a specific file
export const POST: RequestHandler = async ({ request, locals }) => {
  try {
    const body = await request.json();
    const { fileExternalId, driveId, dataSourceId } = body;

    // Validate required parameters
    if (!fileExternalId) {
      return json({ error: "fileExternalId is required" }, { status: 400 });
    }

    if (!driveId) {
      return json({ error: "driveId is required" }, { status: 400 });
    }

    if (!dataSourceId) {
      return json({ error: "dataSourceId is required" }, { status: 400 });
    }

    // Get users with access to the file
    const users = await getFileAccessUsers(
      fileExternalId,
      driveId,
      dataSourceId,
    );

    return json({ users });
  } catch (error) {
    const errorMessage = error instanceof Error
      ? error.message
      : "Unknown error";
    console.error("Error fetching file access users:", error);
    return json({ error: errorMessage }, { status: 500 });
  }
};
