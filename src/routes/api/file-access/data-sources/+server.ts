import { json } from "@sveltejs/kit";
import { supabaseAdmin } from "$lib/server/supabaseAdmin";
import type { Request<PERSON><PERSON><PERSON> } from "./$types";

// GET endpoint to retrieve the data source for a file
export const GET: RequestHandler = async ({ url, locals }) => {
  try {
    // Extract file_id from query parameters
    const fileId = url.searchParams.get("file_id");

    if (!fileId) {
      return json({ error: "file_id is required" }, { status: 400 });
    }

    // Get the file to find its data_source_id
    const { data: file, error: fileError } = await supabaseAdmin
      .from("files")
      .select("data_source_id")
      .eq("id", fileId)
      .single();

    if (fileError) {
      console.error("Error fetching file:", fileError);
      return json({ error: "Failed to fetch file information" }, {
        status: 500,
      });
    }

    if (!file?.data_source_id) {
      return json({ error: "File has no associated data source" }, {
        status: 404,
      });
    }

    // Get the data source details
    const { data: dataSource, error: dsError } = await supabaseAdmin
      .from("data_sources")
      .select("*")
      .eq("id", file.data_source_id)
      .single();

    if (dsError) {
      console.error("Error fetching data source:", dsError);
      return json({ error: "Failed to fetch data source information" }, {
        status: 500,
      });
    }

    return json({ dataSource });
  } catch (error) {
    console.error("Error fetching data source:", error);
    return json({ error: "Failed to fetch data source" }, { status: 500 });
  }
};
