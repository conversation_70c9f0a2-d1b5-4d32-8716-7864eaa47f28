import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { generateEmbedding } from '$lib/server/openai';
import { baseUrl } from '$lib/utils';

export const POST: RequestHandler = async ({ request, locals: { supabase, user }, fetch }) => {
	console.log('📥 [POST] Starting request handling');
	try {
		// Validate user authentication
		if (!user?.id) {
			console.warn('⚠️ [POST] Unauthorized request - no user ID');
			return json({ error: 'Unauthorized' }, { status: 401 });
		}
		console.log('👤 [POST] Authenticated user:', {
			id: user.id,
			email: user.email,
			role: user.role
		});

		// Parse and validate request body
		const body = await request.json();
		console.log('📋 [POST] Request body:', body);
		const { query, companyId, sourceId, userEmail } = body;

		if (!query || !companyId) {
			console.warn('⚠️ [POST] Missing required fields:', {
				hasQuery: !!query,
				hasCompanyId: !!companyId
			});
			return json(
				{
					error: 'Missing required fields',
					details: { query: !query, companyId: !companyId }
				},
				{ status: 400 }
			);
		}
		console.log('✅ [POST] Required fields validated');

		// If userEmail is provided, we'll use it for permission checks
		// This is for testing purposes only and admin users can check permissions for other users
		const targetUserId = user.id;
		let targetUserEmail = user.email;
		let isTestingAsUser = false;

		if (userEmail && userEmail !== user.email) {
			console.log('🔄 [POST] Testing as another user:', userEmail);

			// Verify if the current user is an admin or has sufficient privileges
			const { data: adminCheck, error: adminCheckError } = await supabase
				.from('company_members')
				.select('role')
				.eq('company_id', companyId)
				.eq('user_id', user.id)
				.eq('is_active', true)
				.single();

			if (adminCheckError || !adminCheck || adminCheck.role !== 'admin') {
				console.warn('⚠️ [POST] Non-admin user attempting to test as another user');
				return json(
					{
						error: 'Permission denied: Only admins can test as other users'
					},
					{ status: 403 }
				);
			}

			// Look up the target user by email
			const { data: targetUser, error: targetUserError } = await supabase
				.from('users')
				.select('id, email')
				.eq('email', userEmail)
				.single();

			if (targetUserError || !targetUser) {
				console.warn('⚠️ [POST] Target user not found:', userEmail);
				return json(
					{
						error: 'Target user not found',
						details: 'The provided email does not match any user in the system'
					},
					{ status: 404 }
				);
			}

			// Verify if target user has access to this company
			const { data: memberAccess, error: memberError } = await supabase
				.from('company_members')
				.select('id, is_active')
				.eq('company_id', companyId)
				.eq('user_id', targetUser.id)
				.eq('is_active', true)
				.single();

			if (memberError || !memberAccess) {
				console.warn('⚠️ [POST] Target user has no access to company:', {
					email: userEmail,
					companyId
				});
				return json(
					{
						error: 'Target user has no active membership in this company'
					},
					{ status: 403 }
				);
			}

			targetUserEmail = targetUser.email;
			isTestingAsUser = true;
			console.log('✅ [POST] Successfully switched context to target user:', {
				email: targetUserEmail
			});
		} else {
			// Separately check if user has access through company_members
			const { data: memberAccess, error: memberError } = await supabase
				.from('company_members')
				.select('id, is_active')
				.eq('company_id', companyId)
				.eq('user_id', user.id)
				.eq('is_active', true)
				.single();

			if (memberError) {
				console.error('❌ [POST] Error checking member access:', memberError);
				return json(
					{
						error: 'Failed to verify company access',
						details: memberError.message
					},
					{ status: 403 }
				);
			}

			if (!memberAccess) {
				console.log('⚠️ [POST] User does not have active access to company');
				return json(
					{
						error: 'No active membership in company'
					},
					{ status: 403 }
				);
			}
		}

		// First verify if documents exist and their embeddings
		console.log('🔍 [POST] Checking for documents in company');
		const { data: documents, error: documentsError } = await supabase
			.from('documents')
			.select(
				`
        id, 
        embedding,
        files!inner (
          id,
          company_id
        )
      `
			)
			.eq('files.company_id', companyId);

		if (documentsError) {
			console.error('❌ [POST] Error checking documents:', documentsError);
			return json(
				{
					error: 'Failed to check documents',
					details: documentsError.message
				},
				{ status: 500 }
			);
		}

		console.log('📊 [POST] Document check result:', {
			count: documents?.length,
			hasEmbeddings: documents?.some((d) => d.embedding !== null),
			firstDoc: documents?.[0]?.id,
			sampleDoc: documents?.[0] // Log a sample doc to verify structure
		});

		if (!documents?.length) {
			console.log('ℹ️ [POST] No documents found for company');
			return json({
				query,
				documents: [],
				message: 'No documents found for this company'
			});
		}

		if (!documents.some((d) => d.embedding !== null)) {
			console.log('⚠️ [POST] No documents have embeddings');
			return json({
				query,
				documents: [],
				message: 'Documents exist but have no embeddings'
			});
		}

		// Sample some document content to understand what we're searching
		console.log('📑 [POST] Sampling document content');
		const { data: sampleContent } = await supabase
			.from('documents')
			.select(
				`
        id,
        content,
        metadata,
        embedding,
        file_id,
        files!inner (
          id,
          company_id,
          name,
          path
        )
      `
			)
			.eq('files.company_id', companyId)
			.limit(3);

		console.log('📄 [POST] Sample documents:', {
			samples: sampleContent?.map((d) => ({
				id: d.id,
				preview: d.content?.slice(0, 200) + '...',
				metadata: d.metadata,
				fileName: d.files && d.files.length > 0 ? d.files[0].name : null,
				filePath: d.files && d.files.length > 0 ? d.files[0].path : null,
				hasEmbedding: !!d.embedding,
				embeddingLength: d.embedding?.length
			}))
		});

		// Generate embedding
		console.log('🔄 [POST] Generating embedding for query:', query);
		const embedding = await generateEmbedding(query);
		console.log('✅ [POST] Embedding generated, length:', embedding.length);

		// Check if user has a SharePoint profile in the same company
		console.log('🔍 [POST] Checking for SharePoint platform profiles in the company');
		const { data: companyMembers, error: companyMembersError } = await supabase
			.from('company_members')
			.select('id')
			.eq('company_id', companyId)
			.eq('is_active', true);

		if (companyMembersError) {
			console.error('❌ [POST] Error fetching company members:', companyMembersError);
		} else {
			console.log(`✅ [POST] Found ${companyMembers.length} company members to check for profiles`);

			// Get the list of company member IDs
			const companyMemberIds = companyMembers.map((member) => member.id);

			// Find SharePoint profiles for any company member
			const { data: sharePointProfiles, error: profilesError } = await supabase
				.from('user_platform_profiles')
				.select('id, platform_user_id, company_member_id, metadata')
				.eq('platform_type', 'sharepoint')
				.in('company_member_id', companyMemberIds);

			if (profilesError) {
				console.log('❌ [POST] Error fetching SharePoint profiles:', profilesError.message);
			} else if (!sharePointProfiles || sharePointProfiles.length === 0) {
				console.log('ℹ️ [POST] No SharePoint profiles found for any company members');
			} else {
				console.log(
					`✅ [POST] Found ${sharePointProfiles.length} SharePoint profiles for company members`
				);
			}
		}

		// Check if the specific user has a SharePoint profile
		console.log('🔍 [POST] Checking for SharePoint platform profile for the target user');
		const { data: sharePointProfile, error: profileError } = await supabase
			.from('user_platform_profiles')
			.select('id, platform_user_id, company_member_id, metadata')
			.eq('platform_type', 'sharepoint')
			.eq('platform_user_id', targetUserEmail)
			.single();

		if (profileError) {
			console.log('ℹ️ [POST] No SharePoint profile found for user:', profileError.message);
		} else {
			console.log('✅ [POST] Found SharePoint profile for user:', {
				id: sharePointProfile.id,
				platformUserId: sharePointProfile.platform_user_id,
				companyMemberId: sharePointProfile.company_member_id,
				hasMetadata: !!sharePointProfile.metadata
			});
		}

		// URL for the Edge Function
		// Use baseUrl utility instead of trying to get URL from Supabase client
		let supabaseUrl =
			import.meta.env.VITE_SUPABASE_URL ||
			process.env.PUBLIC_SUPABASE_URL ||
			process.env.SUPABASE_URL;

		// If still undefined, use request origin or default
		if (!supabaseUrl) {
			const requestUrl = request.headers.get('origin') || '';
			if (requestUrl.includes('localhost')) {
				supabaseUrl = 'https://localhost:54321';
			} else {
				// Extract Supabase URL from baseUrl if possible
				const baseUrlHost = new URL(baseUrl).hostname;
				if (baseUrlHost.includes('vercel')) {
					// For Vercel deployments, use the project URL pattern for Supabase
					supabaseUrl = `https://${baseUrlHost.split('.')[0]}.supabase.co`;
				}
			}
		}

		console.log('🔗 [POST] Using Supabase URL for Edge Function:', supabaseUrl);
		const apiUrl = `${supabaseUrl}/functions/v1/match_documents`;

		// Get auth token from Supabase session instead of request headers
		const { data: sessionData } = await supabase.auth.getSession();
		const accessToken = sessionData?.session?.access_token;

		if (!accessToken) {
			console.error('Supabase access token not available');
			return json(
				{ error: 'Supabase access token not available' },
				{
					status: 401
				}
			);
		}

		console.log(`Authorization header available: ${accessToken ? 'Yes' : 'No'}`);

		// Create request body
		const requestBody = {
			embedding,
			userEmail,
			companyId,
			matchCount: 3,
			similarityThreshold: 0.7,
			filter: {},
			debug: true // Enable debugging in the edge function
		};

		console.log(
			`Request payload: embedding array length=${embedding.length}, userEmail=${userEmail}, companyId=${companyId}`
		);

		// Call edge function
		const response = await fetch(apiUrl, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${accessToken}`
			},
			body: JSON.stringify(requestBody)
		});

		console.log(`Edge function response status: ${response.status}`);
		console.log(`Edge function response headers:`, Object.fromEntries(response.headers.entries()));

		// Check if the response is ok first
		if (!response.ok) {
			// Try to get error details from the response
			let errorDetails;
			const contentType = response.headers.get('content-type');

			if (contentType && contentType.includes('application/json')) {
				// It's JSON, we can parse it
				errorDetails = await response.json();
			} else {
				// It's not JSON, get the text instead
				const text = await response.text();
				errorDetails = {
					message: `Edge Function returned non-JSON response: ${text.substring(0, 100)}...`,
					statusCode: response.status,
					statusText: response.statusText
				};
			}

			console.error('❌ [POST] Edge Function returned error:', {
				status: response.status,
				details: errorDetails
			});

			return json(
				{
					error: 'Edge Function call failed',
					details: errorDetails,
					edgeFunctionError: {
						message: errorDetails.message || response.statusText,
						statusCode: response.status,
						details: errorDetails,
						cause: 'edge_function_error'
					}
				},
				{ status: 500 } // Return 500 instead of the edge function status to ensure client gets the error details
			);
		}

		// Response is OK, try to parse the JSON
		const contentType = response.headers.get('content-type');
		if (!contentType || !contentType.includes('application/json')) {
			console.warn('⚠️ [POST] Edge Function returned non-JSON content type:', contentType);
			const text = await response.text();
			throw new Error(`Edge Function returned non-JSON response with content type: ${contentType}`);
		}

		const edgeFunctionResult = await response.json();

		console.log('✅ [POST] Edge Function success:', {
			matchCount: edgeFunctionResult.matches?.length || 0,
			timing: edgeFunctionResult.timing,
			counts: edgeFunctionResult.counts
		});

		const edgeFunctionMatches = edgeFunctionResult.matches || [];
		const edgeFunctionTiming = edgeFunctionResult.timing;
		const edgeFunctionCounts = edgeFunctionResult.counts;
		const edgeFunctionDebug = edgeFunctionResult.debug;

		// First try with permissions check - without source filter
		console.log('🔍 [POST] Attempting search with permissions check');
		let { data: permissionMatches, error: permissionError } = await supabase.rpc(
			'match_documents_with_permissions',
			{
				user_id: targetUserId,
				query_embedding: embedding,
				match_count: 10,
				filter: { company_id: companyId }, // Remove source filter
				similarity_threshold: 0.001
			}
		);

		// NEW FUNCTION: Try with the new user_permissions function
		console.log('🔍 [POST] Attempting search with user permissions function');
		let { data: userPermissionMatches, error: userPermissionError } = await supabase.rpc(
			'match_documents_with_user_permissions',
			{
				user_id: targetUserId,
				query_embedding: embedding,
				match_count: 10,
				filter: { company_id: companyId },
				similarity_threshold: 0.001
			}
		);

		console.log('📊 [POST] User permissions function result:', {
			success: !!userPermissionMatches,
			error: userPermissionError?.message,
			documentCount: userPermissionMatches?.length,
			topMatches: userPermissionMatches?.slice(0, 3).map((d: any) => ({
				id: d.id,
				similarity: d.similarity,
				content: d.content?.slice(0, 100) + '...'
			}))
		});

		// If needed, refresh user permissions via Edge Function
		if (userPermissionError || (!userPermissionMatches?.length && !permissionMatches?.length)) {
			console.log('🔄 [POST] Attempting to refresh user permissions');
			try {
				// If testing as another user, we need a different approach for refresh
				if (isTestingAsUser) {
					console.log('⚠️ [POST] Testing as another user, skipping permission refresh');
				} else {
					const { data: memberAccess } = await supabase
						.from('company_members')
						.select('id')
						.eq('company_id', companyId)
						.eq('user_id', targetUserId)
						.eq('is_active', true)
						.single();

					if (memberAccess) {
						const { data: refreshResult, error: refreshError } = await supabase.rpc(
							'refresh_user_permissions',
							{
								member_id: memberAccess.id
							}
						);

						console.log('📊 [POST] Permissions refresh result:', {
							success: refreshResult,
							error: refreshError?.message
						});

						if (refreshResult) {
							// Try the search again after refreshing permissions
							const { data: refreshedMatches, error: refreshedError } = await supabase.rpc(
								'match_documents_with_user_permissions',
								{
									user_id: targetUserId,
									query_embedding: embedding,
									match_count: 10,
									filter: { company_id: companyId },
									similarity_threshold: 0.001
								}
							);

							console.log('📊 [POST] Search results after refresh:', {
								success: !!refreshedMatches,
								error: refreshedError?.message,
								documentCount: refreshedMatches?.length
							});

							// Update the results if successful
							if (refreshedMatches?.length) {
								userPermissionMatches = refreshedMatches;
								userPermissionError = null;
							}
						}
					}
				}
			} catch (refreshException) {
				console.error('❌ [POST] Error refreshing permissions:', refreshException);
			}
		}

		// Get raw similarity scores even if below threshold
		const { data: rawScores } = await supabase.rpc('match_documents_with_permissions', {
			user_id: targetUserId,
			query_embedding: embedding,
			match_count: 5,
			filter: { company_id: companyId }, // Remove source filter
			similarity_threshold: 0
		});

		console.log('📊 [POST] Raw similarity scores:', {
			scores: rawScores?.map((d: any) => ({
				similarity: d.similarity,
				preview: d.content?.slice(0, 100)
			}))
		});

		console.log('📊 [POST] Permission check result:', {
			success: !!permissionMatches,
			error: permissionError?.message,
			documentCount: permissionMatches?.length,
			topMatches: permissionMatches?.slice(0, 3).map((d: any) => ({
				id: d.id,
				similarity: d.similarity,
				content: d.content?.slice(0, 100) + '...'
			}))
		});

		// Always try public function as well
		console.log('🔍 [POST] Attempting public search with filter:', {
			company_id: companyId
		});
		const { data: publicMatches, error: publicError } = await supabase.rpc(
			'match_documents_public',
			{
				query_embedding: embedding,
				match_count: 10,
				similarity_threshold: 0.001,
				filter: { company_id: companyId } // Pass as object directly, PostgreSQL will handle the conversion
			}
		);

		console.log('📊 [POST] Public function result:', {
			success: !!publicMatches,
			error: publicError?.message,
			documentCount: publicMatches?.length,
			topMatches: publicMatches?.slice(0, 3).map((d: any) => ({
				id: d.id,
				similarity: d.similarity,
				content: d.content?.slice(0, 100) + '...'
			}))
		});

		// Use the new function results if available
		const finalPermissionMatches = userPermissionMatches || permissionMatches;
		const finalPermissionError =
			userPermissionError && permissionError
				? {
						userPermissions: userPermissionError,
						standardPermissions: permissionError
					}
				: null;

		// Handle errors from both attempts
		if (finalPermissionError && publicError) {
			console.error('❌ [POST] All search attempts failed:', {
				permissionErrors: finalPermissionError,
				publicError: {
					code: publicError.code,
					message: publicError.message
				}
			});

			// Handle specific error cases
			if (
				(typeof finalPermissionError === 'object' &&
					(finalPermissionError.userPermissions?.message?.includes('similarity_threshold') ||
						finalPermissionError.standardPermissions?.message?.includes('similarity_threshold'))) ||
				publicError.message?.includes('similarity_threshold')
			) {
				return json({ error: 'Invalid similarity threshold' }, { status: 400 });
			}

			if (publicError.message?.includes('company_id is required')) {
				return json({ error: 'Company ID is required' }, { status: 400 });
			}

			const permissionDenied =
				typeof finalPermissionError === 'object' &&
				(finalPermissionError.userPermissions?.code === 'PGRST301' ||
					finalPermissionError.userPermissions?.message?.includes('permission denied')) &&
				(finalPermissionError.standardPermissions?.code === 'PGRST301' ||
					finalPermissionError.standardPermissions?.message?.includes('permission denied')) &&
				(publicError.code === 'PGRST301' || publicError.message?.includes('permission denied'));

			if (permissionDenied) {
				return json({ error: 'Unauthorized access' }, { status: 403 });
			}

			return json(
				{
					error: 'Failed to retrieve documents',
					details: {
						permissionError: finalPermissionError,
						publicError: publicError.message
					}
				},
				{ status: 500 }
			);
		}

		return json({
			query,
			embedding_length: embedding.length,
			permissionMatches: finalPermissionMatches || [],
			publicMatches: publicMatches || [],
			userPermissionsUsed: !!userPermissionMatches,
			testingAsUser: isTestingAsUser ? userEmail : null,
			edgeFunctionMatches,
			edgeFunctionError: null,
			edgeFunctionTiming,
			edgeFunctionCounts,
			edgeFunctionDebug,
			sharePointProfile: profileError
				? null
				: {
						id: sharePointProfile?.id,
						platformUserId: sharePointProfile?.platform_user_id,
						companyMemberId: sharePointProfile?.company_member_id
					}
		});
	} catch (error: unknown) {
		console.error('💥 [POST] Unhandled error:', error);
		return json(
			{
				error: 'Server error',
				details: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
};
