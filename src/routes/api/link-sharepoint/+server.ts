//src/routes/api/link-sharepoint/+server.ts
import { env } from '$env/dynamic/public';
import { sharepointScopes } from '$lib/server/microsoft';
import { baseUrl, generateCodeChallenge, generateCodeVerifier, generateState } from '$lib/utils.js';
import type { RequestHandler } from '@sveltejs/kit';

export const POST: RequestHandler = async ({ cookies, locals: { supabase, user } }) => {
	const startTime = new Date().toISOString();
	console.log('[Link SharePoint] Starting OAuth flow', {
		timestamp: startTime,
		hasUser: !!user,
		userId: user?.id,
		baseUrl
	});

	try {
		const codeVerifier = generateCodeVerifier();
		const codeChallenge = generateCodeChallenge(codeVerifier);
		const companyId = cookies.get('companyId');

		console.log('[Link SharePoint] Initial parameters', {
			hasCodeVerifier: !!codeVerifier,
			hasCodeChallenge: !!codeChallenge,
			companyId,
			timestamp: new Date().toISOString()
		});

		if (!companyId || !user?.id) {
			console.error('[Link SharePoint] Missing required parameters', {
				hasCompanyId: !!companyId,
				hasUserId: !!user?.id,
				timestamp: new Date().toISOString()
			});
			return new Response(
				JSON.stringify({
					success: false,
					error: 'Company ID and user ID are required.'
				}),
				{ status: 400, headers: { 'Content-Type': 'application/json' } }
			);
		}

		// Get the company member ID for the current user
		console.log('[Link SharePoint] Fetching company member', {
			companyId,
			userId: user.id,
			timestamp: new Date().toISOString()
		});

		const { data: companyMember, error: memberError } = await supabase
			.from('company_members')
			.select('id')
			.eq('company_id', companyId)
			.eq('user_id', user.id)
			.single();

		if (memberError || !companyMember) {
			console.error('[Link SharePoint] Failed to get company member:', {
				error: memberError,
				companyId,
				userId: user.id,
				timestamp: new Date().toISOString()
			});
			return new Response(
				JSON.stringify({
					success: false,
					error: 'Failed to get company member information.'
				}),
				{ status: 400, headers: { 'Content-Type': 'application/json' } }
			);
		}

		console.log('[Link SharePoint] Successfully retrieved company member', {
			companyMemberId: companyMember.id,
			timestamp: new Date().toISOString()
		});

		const statePayload = {
			companyId,
			companyMemberId: companyMember.id,
			purpose: 'addDataSource'
		};
		const state = generateState(statePayload);

		console.log('[Link SharePoint] Generated state data', {
			statePayload,
			encodedState: state,
			timestamp: new Date().toISOString()
		});

		// Log cookie settings
		console.log('[Link SharePoint] Setting cookies', {
			stateCookie: {
				value: 'REDACTED',
				path: '/',
				httpOnly: true,
				secure: true,
				sameSite: 'lax'
			},
			codeVerifierCookie: {
				value: 'REDACTED',
				path: '/',
				httpOnly: true,
				secure: true,
				sameSite: 'lax'
			},
			timestamp: new Date().toISOString()
		});

		cookies.set('state', state, {
			path: '/',
			httpOnly: true,
			secure: true,
			sameSite: 'lax'
		});
		cookies.set('code_verifier', codeVerifier, {
			path: '/',
			httpOnly: true,
			secure: true,
			sameSite: 'lax'
		});

		const clientId = env.PUBLIC_AZURE_CLIENT_ID;
		const redirectUri = `${baseUrl}/auth/add-data-source`;

		console.log('[Link SharePoint] Preparing OAuth URL', {
			clientId,
			redirectUri,
			scopes: sharepointScopes,
			timestamp: new Date().toISOString()
		});

		const authUrl =
			`https://login.microsoftonline.com/common/oauth2/v2.0/authorize?` +
			`client_id=${encodeURIComponent(clientId)}` +
			`&response_type=code` +
			`&redirect_uri=${encodeURIComponent(redirectUri)}` +
			`&scope=${encodeURIComponent(sharepointScopes.join(' '))}` +
			`&state=${encodeURIComponent(state)}` +
			`&prompt=select_account` +
			`&code_challenge=${encodeURIComponent(await codeChallenge)}` +
			`&code_challenge_method=S256`;

		console.log('[Link SharePoint] Redirecting to Microsoft login', {
			authUrl,
			timestamp: new Date().toISOString()
		});

		return new Response(null, { status: 302, headers: { location: authUrl } });
	} catch (error) {
		console.error('[Link SharePoint] Error during OAuth flow preparation:', {
			error,
			stack: error instanceof Error ? error.stack : undefined,
			timestamp: new Date().toISOString()
		});
		return new Response(JSON.stringify({ success: false, error: 'Internal Server Error' }), {
			status: 500,
			headers: { 'Content-Type': 'application/json' }
		});
	}
};
