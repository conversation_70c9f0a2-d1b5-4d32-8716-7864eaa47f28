import { json } from "@sveltejs/kit";
import type { Request<PERSON>and<PERSON> } from "./$types";
import { createClient } from "@supabase/supabase-js";

export const POST: RequestHandler = async (
  { request, locals: { supabase } },
) => {
  const session = await supabase.auth.getSession();
  if (!session.data.session) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const {
      siteData,
      companyId,
      tokens,
      userId,
    } = await request.json();

    // Create vault secrets for tokens
    const [idTokenSecret, refreshTokenSecret, accessTokenSecret] = await Promise
      .all([
        supabase.rpc("create_secret", {
          secret: JSON.stringify(tokens.id_token),
        }),
        supabase.rpc("create_secret", { secret: tokens.refresh_token }),
        supabase.rpc("create_secret", { secret: tokens.access_token }),
      ]);

    if (
      !idTokenSecret.data || !refreshTokenSecret.data || !accessTokenSecret.data
    ) {
      throw new Error("Failed to create secrets");
    }

    // Get site info using access token
    const siteResponse = await fetch(
      `https://graph.microsoft.com/v1.0/sites/${siteData.value}`,
      {
        headers: {
          "Authorization": `Bearer ${tokens.access_token}`,
        },
      },
    );
    const siteInfo = await siteResponse.json();

    // Get drive info
    const driveResponse = await fetch(
      `https://graph.microsoft.com/v1.0/sites/${siteData.value}/drive`,
      {
        headers: {
          "Authorization": `Bearer ${tokens.access_token}`,
        },
      },
    );
    const driveInfo = await driveResponse.json();

    // Get root folder
    const rootFolderResponse = await fetch(
      `https://graph.microsoft.com/v1.0/sites/${siteInfo.id}/drives/${driveInfo.id}/root/children`,
      {
        headers: {
          "Authorization": `Bearer ${tokens.access_token}`,
        },
      },
    );
    const rootFolder = await rootFolderResponse.json();

    // Insert data source
    const { data: dataSource, error: dataSourceError } = await supabase
      .from("data_sources")
      .upsert({
        company_id: companyId,
        display_name: siteInfo.displayName,
        type: "sharepoint",
        tenant_id: tokens.tid,
        secret_oauth: refreshTokenSecret.data,
        id_token: idTokenSecret.data,
        access_token: accessTokenSecret.data,
        scope: tokens.scope,
        site_id: siteData.value,
        drive_id: rootFolder.value[0]?.parentReference?.driveId,
        platform: "sharepoint",
      })
      .select()
      .single();

    if (dataSourceError) {
      throw new Error(
        `Failed to create data source: ${dataSourceError.message}`,
      );
    }

    // Update user profile if needed
    const { error: profileError } = await supabase
      .from("user_profiles")
      .upsert({
        user_id: userId,
        company_id: companyId,
        platform: "sharepoint",
        platform_id: tokens.oid,
        role: "admin",
      });

    if (profileError) {
      throw new Error(`Failed to update user profile: ${profileError.message}`);
    }

    return json({ success: true, dataSource });
  } catch (error) {
    console.error("Error creating data source:", error);
    const err = error as Error;
    return json({ error: err.message }, { status: 500 });
  }
};
