import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ locals: { supabase }, params }) => {
	const { companyId } = params;

	// Fetch all products
	const { data: products, error: productsError } = await supabase
		.from('products')
		.select('*')
		.order('name');

	if (productsError) {
		console.error('Error fetching products:', productsError);
		return json({ success: false, error: productsError.message }, { status: 500 });
	}

	// Fetch company products
	const { data: companyProducts, error: companyProductsError } = await supabase
		.from('company_products')
		.select('*')
		.eq('company_id', companyId);

	if (companyProductsError) {
		console.error('Error fetching company products:', companyProductsError);
		return json({ success: false, error: companyProductsError.message }, { status: 500 });
	}

	// If a product is marked as coming soon, make sure it's not enabled in company_products
	// This ensures consistency in case database state is inconsistent
	const cleanedCompanyProducts =
		companyProducts?.map((cp) => {
			const product = products?.find((p) => p.id === cp.product_id);
			if (product?.is_coming_soon && cp.is_enabled) {
				// Return a copy with is_enabled set to false
				return { ...cp, is_enabled: false };
			}
			return cp;
		}) || [];

	return json({
		success: true,
		products: products || [],
		companyProducts: cleanedCompanyProducts
	});
};
