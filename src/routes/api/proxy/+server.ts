import { json } from '@sveltejs/kit';
import { getN8nWebhookUrl } from '$lib/utils';

export async function POST({ request }) {
	try {
		const body = await request.json();

		const response = await fetch(getN8nWebhookUrl('totm-hook-3223', {}, body.supabaseDb), {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify(body)
		});

		const data = await response.json();

		return json(data, { status: response.status });
	} catch (error) {
		console.error('Proxy error:', error);
		return json({ error: 'Proxy error' }, { status: 500 });
	}
}
