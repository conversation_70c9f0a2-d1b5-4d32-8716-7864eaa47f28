import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';

export const GET: RequestHandler = async () => {
	// Only show if environment variables exist, not their actual values
	const environmentStatus = {
		PUBLIC_FB_APP_ID_EXISTS: !!process.env.PUBLIC_FB_APP_ID,
		PRIVATE_FB_APP_SECRET_EXISTS: !!process.env.PRIVATE_FB_APP_SECRET,
		PUBLIC_FB_CONFIG_ID_EXISTS: !!process.env.PUBLIC_FB_CONFIG_ID,
		PUBLIC_BASE_URL_EXISTS: !!process.env.PUBLIC_BASE_URL
	};

	return json(environmentStatus);
};
