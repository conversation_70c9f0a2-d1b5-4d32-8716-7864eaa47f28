import { json } from "@sveltejs/kit";
import type { Request<PERSON>and<PERSON> } from "./$types";
import { generateEmbedding } from "$lib/server/openai";

interface Document {
  id: string;
  content: string;
  metadata: Record<string, unknown>;
  similarity: number;
  update_date: string;
}

export const POST: RequestHandler = async (
  { request, locals: { supabase } },
) => {
  console.log("📥 [POST] Starting WhatsApp document search request");
  try {
    const body = await request.json();
    const { userPhone, companyPhone, query } = body;

    if (!userPhone || !companyPhone || !query) {
      console.warn("⚠️ [POST] Missing required fields:", {
        hasUserPhone: !!userPhone,
        hasCompanyPhone: !!companyPhone,
        hasQuery: !!query,
      });
      return json(
        {
          error: "Missing required fields",
          details: {
            userPhone: !userPhone,
            companyPhone: !companyPhone,
            query: !query,
          },
        },
        { status: 400 },
      );
    }

    console.log("✅ [POST] Required fields validated");
    console.log("🔄 [POST] Generating embedding for query:", query);

    const embeddingResponse = await generateEmbedding(query);
    console.log(
      "✅ [POST] Embedding generated, length:",
      embeddingResponse.length,
    );

    const { data: documents, error } = await supabase
      .rpc("match_documents_by_phone", {
        user_phone: userPhone,
        company_phone: companyPhone,
        query_embedding: embeddingResponse,
        match_count: 5, // Limiting results for WhatsApp
        similarity_threshold: 0.1,
      });

    if (error) {
      console.error("❌ [POST] Document matching error:", {
        code: error.code,
        message: error.message,
        details: error.details,
      });

      // Handle specific error cases
      if (error.message.includes("Invalid company phone number")) {
        return json({ error: "Invalid company WhatsApp number" }, {
          status: 400,
        });
      }

      if (error.message.includes("User not found or not authorized")) {
        return json({ error: "User not authorized" }, { status: 403 });
      }

      return json(
        { error: "Failed to retrieve documents", details: error.message },
        { status: 500 },
      );
    }

    console.log("📊 [POST] Documents retrieved:", {
      count: documents?.length ?? 0,
      firstDocumentId: documents?.[0]?.id,
    });

    if (!documents || documents.length === 0) {
      console.log("ℹ️ [POST] No matching documents found");
      return json({
        query,
        documents: [],
        message: "No matching documents found",
      });
    }

    // Format documents for WhatsApp response
    const formattedDocuments = (documents as Document[]).map((doc) => ({
      id: doc.id,
      content: doc.content,
      metadata: doc.metadata,
      similarity: Math.round(doc.similarity * 100) / 100,
    }));

    console.log("✅ [POST] Successfully returning documents");
    return json({
      query,
      documents: formattedDocuments,
    });
  } catch (err) {
    console.error("❌ [POST] Error in WhatsApp document search:", {
      error: err,
      message: err instanceof Error ? err.message : "Unknown error",
      stack: err instanceof Error ? err.stack : undefined,
    });
    return json(
      {
        error: "Internal server error",
        details: err instanceof Error ? err.message : "Unknown error",
      },
      { status: 500 },
    );
  }
};
