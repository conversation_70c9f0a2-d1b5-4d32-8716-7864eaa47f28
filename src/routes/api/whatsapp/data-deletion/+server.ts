import { json } from '@sveltejs/kit';
import type { Request<PERSON><PERSON><PERSON> } from './$types';
import { PRIVATE_FB_APP_SECRET } from '$env/static/private';
import { createHmac } from 'crypto';
import { supabase } from '$lib/supabaseClient';

// Helper to get the base URL
function getBaseUrl(request: Request) {
	const host = request.headers.get('host') || 'localhost:3010';
	const protocol = host.includes('localhost') ? 'https://' : 'https://';
	return `${protocol}${host}`;
}

export const POST: RequestHandler = async ({ request }) => {
	try {
		// Get raw body for signature verification
		const rawBody = await request.text();
		const body = JSON.parse(rawBody);

		// Verify signature
		const signature = request.headers.get('x-hub-signature-256');
		if (!signature) {
			console.error('Missing signature in data deletion callback');
			return json({ error: 'Missing signature' }, { status: 401 });
		}

		// Calculate expected signature
		const hmac = createHmac('sha256', PRIVATE_FB_APP_SECRET);
		hmac.update(rawBody);
		const expectedSignature = `sha256=${hmac.digest('hex')}`;

		if (signature !== expectedSignature) {
			console.error('Invalid signature in data deletion callback');
			return json({ error: 'Invalid signature' }, { status: 401 });
		}

		const { user_id, business_id } = body;
		if (!business_id) {
			console.error('Missing business_id in data deletion callback', body);
			return json({ error: 'Missing business_id' }, { status: 400 });
		}

		// Start a transaction to handle data deletion
		const { error: transactionError } = await supabase.rpc('handle_whatsapp_data_deletion', {
			p_business_id: business_id,
			p_user_id: user_id,
			p_metadata: body
		});

		if (transactionError) {
			console.error('Error in data deletion transaction:', transactionError);
			return json({ error: 'Database error' }, { status: 500 });
		}

		// Return confirmation URL as required by Meta
		const confirmationCode = body.confirmation_code;
		const baseUrl = getBaseUrl(request);
		return json({
			url: `${baseUrl}/api/whatsapp/data-deletion/confirm?code=${confirmationCode}`,
			confirmation_code: confirmationCode
		});
	} catch (error) {
		console.error('Error in data deletion callback:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};

// Confirmation endpoint
export const GET: RequestHandler = async ({ url }) => {
	const code = url.searchParams.get('code');
	if (!code) {
		return json({ error: 'Missing confirmation code' }, { status: 400 });
	}

	return json({
		status: 'completed',
		message: 'Data deletion request completed successfully'
	});
};
