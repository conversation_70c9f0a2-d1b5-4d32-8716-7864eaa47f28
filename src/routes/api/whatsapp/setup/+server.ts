import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';

export const POST: RequestHandler = async ({ url, locals }) => {
	try {
		const companyId = url.searchParams.get('companyId');
		if (!companyId) {
			throw new Error('Company ID is required');
		}

		// Get the Facebook App credentials
		const appId = process.env.PUBLIC_FB_APP_ID;
		const configId = process.env.PUBLIC_FB_CONFIG_ID;

		console.log('Facebook credentials:', {
			appId,
			configId
		});

		if (!appId || !configId) {
			throw new Error('Facebook app credentials are not configured');
		}

		// Generate state parameter for security (prevent CSRF)
		const state = Buffer.from(JSON.stringify({ companyId })).toString('base64');

		// Construct WhatsApp Business Platform OAuth URL
		const redirectUri = `${url.origin}/api/whatsapp/callback`;
		const setupUrl =
			`https://www.facebook.com/v22.0/dialog/oauth?` +
			`client_id=${appId}` +
			`&redirect_uri=${encodeURIComponent(redirectUri)}` +
			`&state=${state}` +
			`&scope=whatsapp_business_management,whatsapp_business_messaging` +
			`&config_id=${configId}` +
			`&response_type=code` +
			`&display=popup`;

		console.log('Setup URL:', setupUrl);

		return json({
			success: true,
			setupUrl
		});
	} catch (error) {
		console.error('WhatsApp setup error:', error);
		return json(
			{
				success: false,
				error: error instanceof Error ? error.message : 'Failed to initiate WhatsApp setup'
			},
			{ status: 500 }
		);
	}
};
