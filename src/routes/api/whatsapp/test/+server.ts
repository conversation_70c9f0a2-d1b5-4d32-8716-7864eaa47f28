import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async () => {
	try {
		const systemUserToken = process.env.PRIVATE_FB_SYSTEM_USER_TOKEN;

		if (!systemUserToken) {
			throw new Error('Facebook system user token is not configured');
		}

		// Test the token with a simple me/accounts call
		const response = await fetch('https://graph.facebook.com/v22.0/me', {
			headers: {
				Authorization: `Bearer ${systemUserToken}`
			}
		});

		const data = await response.json();
		console.log('Test API Response:', {
			status: response.status,
			statusText: response.statusText,
			headers: Object.fromEntries(response.headers.entries()),
			body: data
		});

		return json({
			success: true,
			data
		});
	} catch (error) {
		console.error('Test endpoint error:', error);
		return json(
			{
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
};
