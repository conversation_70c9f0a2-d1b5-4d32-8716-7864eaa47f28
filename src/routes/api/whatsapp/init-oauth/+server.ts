import { json } from "@sveltejs/kit";
import type { RequestHand<PERSON> } from "./$types";
import { generateCsrfToken } from "$lib/utils/security";

export const POST: RequestHandler = async ({ cookies, request }) => {
  try {
    const { companyId } = await request.json();

    // Generate a new CSRF token
    const csrfToken = generateCsrfToken();

    // Set the cookies
    cookies.set("fb_csrf_token", csrfToken, {
      path: "/",
      secure: true,
      sameSite: "lax",
      maxAge: 3600, // 1 hour
    });

    cookies.set("fb_redirect_company_id", companyId, {
      path: "/",
      secure: true,
      sameSite: "lax",
      maxAge: 3600,
    });

    return json({
      success: true,
      csrfToken, // Send it back to client for immediate use
    });
  } catch (error) {
    console.error("Failed to initialize OAuth:", error);
    return json({ error: "Failed to initialize OAuth" }, { status: 500 });
  }
};
