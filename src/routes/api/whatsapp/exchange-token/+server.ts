import { json } from "@sveltejs/kit";
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./$types";
import type {
  WhatsAppTokenExchangeResponse,
} from "$lib/types/api";
import { PRIVATE_FB_APP_SECRET } from "$env/static/private";
import { PUBLIC_FB_APP_ID } from "$env/static/public";
import { baseUrl } from "$lib/utils";
export const POST: RequestHandler = async ({ request, cookies, locals }) => {
  let requestData;
  try {
    requestData = await request.json();
  } catch (error) {
    console.error("[WhatsApp Token Exchange] Failed to parse request body:", {
      error,
      contentType: request.headers.get("content-type"),
      timestamp: new Date().toISOString(),
    });

    // Try to read as URL-encoded data if JSON parsing fails
    const text = await request.text();
    console.log("[WhatsApp Token Exchange] Raw request body:", text);

    try {
      // Parse URL-encoded data
      const urlParams = new URLSearchParams(text);
      requestData = Object.fromEntries(urlParams.entries());
      console.log("[WhatsApp Token Exchange] Parsed URL params:", requestData);
    } catch (urlError) {
      console.error(
        "[WhatsApp Token Exchange] Failed to parse URL params:",
        urlError,
      );
      return json({
        error: "Invalid request format",
        details: { message: "Could not parse request body" },
      }, { status: 400 });
    }
  }

  const { code } = requestData;
  const companyId = cookies.get("fb_redirect_company_id");
  const csrfToken = cookies.get("fb_csrf_token");
  const requestCsrfToken = request.headers.get("x-csrf-token");

  // Log initial request data
  console.log("[WhatsApp Token Exchange] Request details:", {
    hasCode: !!code,
    companyId,
    hasCsrfToken: !!csrfToken,
    hasRequestCsrfToken: !!requestCsrfToken,
    timestamp: new Date().toISOString(),
  });

  // Validate CSRF token
  if (!csrfToken || !requestCsrfToken || csrfToken !== requestCsrfToken) {
    console.warn("[WhatsApp Token Exchange] CSRF validation failed:", {
      csrfToken: !!csrfToken,
      requestCsrfToken: !!requestCsrfToken,
      match: csrfToken === requestCsrfToken,
    });
    return json({
      error: "Invalid request",
    }, {
      status: 403,
    });
  }

  if (!code || !companyId) {
    console.warn("[WhatsApp Token Exchange] Missing parameters:", {
      hasCode: !!code,
      hasCompanyId: !!companyId,
    });
    return json({
      error: "Missing required parameters",
    }, {
      status: 400,
    });
  }

  try {
    console.log("[WhatsApp Token Exchange] Initiating Facebook API request...");

    // Exchange code for token using Facebook Graph API
    const redirectUri = new URL("/auth/facebook/callback", baseUrl)
      .toString();

    const fbUrl = `https://graph.facebook.com/v21.0/oauth/access_token?` +
      new URLSearchParams({
        client_id: PUBLIC_FB_APP_ID,
        client_secret: PRIVATE_FB_APP_SECRET,
        redirect_uri: redirectUri,
        code,
      });

    console.log("[WhatsApp Token Exchange] Facebook API URL:", fbUrl);

    try {
      const response = await fetch(fbUrl, {
        method: "GET",
        headers: {
          "Accept": "application/json",
        },
      });

      if (!response.ok) {
        const error = await response.json();
        console.error("[WhatsApp Token Exchange] Facebook API error:", {
          status: response.status,
          statusText: response.statusText,
          error,
          url: fbUrl,
        });
        throw new Error(error.error?.message || "Failed to exchange token");
      }

      const tokenData: WhatsAppTokenExchangeResponse = await response.json();
      console.log("[WhatsApp Token Exchange] Received token data:", {
        expiresIn: tokenData.expires_in,
        hasAccessToken: !!tokenData.access_token,
      });

      // Store token in database
      console.log("[WhatsApp Token Exchange] Updating Supabase record...");
      const { error: supabaseError } = await locals.supabase
        .from("communication_channels")
        .update({
          access_token: tokenData.access_token,
          token_expires_at: new Date(Date.now() + tokenData.expires_in * 1000)
            .toISOString(),
        })
        .eq("company_id", companyId)
        .eq("type", "whatsapp");

      if (supabaseError) {
        console.error(
          "[WhatsApp Token Exchange] Supabase error:",
          supabaseError,
        );
        throw supabaseError;
      }

      console.log("[WhatsApp Token Exchange] Successfully stored token");

      // Clear sensitive cookies
      cookies.delete("fb_redirect_company_id", {
        path: "/",
        secure: true,
        sameSite: "lax",
      });
      cookies.delete("fb_csrf_token", {
        path: "/",
        secure: true,
        sameSite: "lax",
      });

      return json({
        success: true,
        data: { expires_in: tokenData.expires_in },
      });
    } catch (fetchError) {
      console.error("[WhatsApp Token Exchange] Fetch error:", {
        error: fetchError,
        message: fetchError instanceof Error
          ? fetchError.message
          : "Unknown fetch error",
        cause: fetchError instanceof Error ? fetchError.cause : undefined,
      });
      throw new Error("Failed to connect to Facebook API");
    }
  } catch (error) {
    console.error("[WhatsApp Token Exchange] Error:", {
      error,
      message: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
    });
    return json({
      error: "Failed to exchange token",
      details: {
        message: error instanceof Error ? error.message : "Unknown error",
      },
    }, {
      status: 500,
    });
  }
};
