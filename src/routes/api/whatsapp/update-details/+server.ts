import { json } from "@sveltejs/kit";
import type { RequestHandler } from "./$types";

export const POST: RequestHandler = async (
  { request, locals: { supabase } },
) => {
  const { companyId, phoneNumberId, wabaId } = await request.json();

  // Add request data logging
  console.log("[WhatsApp Update Details] Request payload:", {
    companyId,
    phoneNumberId,
    wabaId,
    timestamp: new Date().toISOString(),
  });

  try {
    console.log("[WhatsApp Update Details] Attempting Supabase upsert...");

    const { error, data } = await supabase
      .from("communication_channels")
      .upsert({
        company_id: companyId,
        type: "whatsapp",
        phone_number_id: phoneNumberId,
        waba_id: wabaId,
        status: "active",
      })
      .eq("company_id", companyId)
      .eq("type", "whatsapp");

    if (error) {
      console.error("[WhatsApp Update Details] Supabase error:", {
        error,
        errorCode: error.code,
        details: error.details,
        hint: error.hint,
      });
      throw error;
    }

    console.log("[WhatsApp Update Details] Success:", { data });
    return json({ success: true });
  } catch (error) {
    console.error("[WhatsApp Update Details] Error:", {
      error,
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
    });
    return json({ error: "Failed to update WhatsApp details" }, {
      status: 500,
    });
  }
};
