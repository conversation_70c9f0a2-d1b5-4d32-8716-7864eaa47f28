import { json } from '@sveltejs/kit';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { PRIVATE_FB_APP_SECRET } from '$env/static/private';
import { createHmac } from 'crypto';
import { supabase } from '$lib/supabaseClient';

export const POST: RequestHandler = async ({ request }) => {
	try {
		// Get raw body for signature verification
		const rawBody = await request.text();
		const body = JSON.parse(rawBody);

		// Verify signature
		const signature = request.headers.get('x-hub-signature-256');
		if (!signature) {
			console.error('Missing signature in deauthorization callback');
			return json({ error: 'Missing signature' }, { status: 401 });
		}

		// Calculate expected signature
		const hmac = createHmac('sha256', PRIVATE_FB_APP_SECRET);
		hmac.update(rawBody);
		const expectedSignature = `sha256=${hmac.digest('hex')}`;

		if (signature !== expectedSignature) {
			console.error('Invalid signature in deauthorization callback');
			return json({ error: 'Invalid signature' }, { status: 401 });
		}

		const { user_id, business_id } = body;
		if (!business_id) {
			console.error('Missing business_id in deauthorization callback', body);
			return json({ error: 'Missing business_id' }, { status: 400 });
		}

		// Find the OAuth token for this business
		const { data: token, error: tokenError } = await supabase
			.from('oauth_tokens')
			.select('id, company_id')
			.eq('business_id', business_id)
			.single();

		if (tokenError || !token) {
			console.error('Error finding token for business_id:', business_id, tokenError);
			return json({ error: 'Token not found' }, { status: 404 });
		}

		// Start a transaction to update everything
		const { error: transactionError } = await supabase.rpc('handle_whatsapp_deauthorization', {
			p_token_id: token.id,
			p_company_id: token.company_id,
			p_business_id: business_id,
			p_user_id: user_id,
			p_metadata: body
		});

		if (transactionError) {
			console.error('Error in deauthorization transaction:', transactionError);
			return json({ error: 'Database error' }, { status: 500 });
		}

		return json({ success: true });
	} catch (error) {
		console.error('Error in deauthorization callback:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};
