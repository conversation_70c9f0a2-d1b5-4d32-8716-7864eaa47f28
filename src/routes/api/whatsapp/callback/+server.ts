import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ url, locals }) => {
	try {
		const code = url.searchParams.get('code');
		const state = url.searchParams.get('state');
		const error = url.searchParams.get('error');
		const errorReason = url.searchParams.get('error_reason');

		console.log('Callback params:', { code, state, error, errorReason });

		if (error) {
			throw new Error(`WhatsApp setup failed: ${error} - ${errorReason}`);
		}

		if (!code || !state) {
			throw new Error('Invalid callback parameters');
		}

		// Get Facebook App credentials
		const appId = process.env.PUBLIC_FB_APP_ID;
		// In development, we might not have access to the private env var through process.env
		// so we'll use a fallback for testing purposes
		const appSecret = process.env.PRIVATE_FB_APP_SECRET || '357e6e5ad8ad3f9c069a4b3690d0f3a3'; // Fallback for dev only

		if (!appId) {
			console.error('Facebook app ID missing', {
				appIdExists: !!process.env.PUBLIC_FB_APP_ID
			});
			throw new Error('Facebook app ID is not configured');
		}

		console.log('Facebook credentials:', {
			appIdExists: !!appId,
			appSecretExists: !!appSecret,
			redirectUri: 'https://localhost:5173/api/whatsapp/callback'
		});

		// Decode state parameter to get companyId
		let companyId: string;
		try {
			const { companyId: decodedCompanyId } = JSON.parse(Buffer.from(state, 'base64').toString());
			companyId = decodedCompanyId;
		} catch (parseError) {
			console.error('Failed to parse state parameter:', parseError);
			throw new Error('Invalid state parameter');
		}

		// Exchange code for access token
		// Based on forum suggestions, let's try NOT including the redirect_uri in the token exchange
		// as this sometimes works for localhost environments
		console.log('Attempting token exchange WITHOUT redirect_uri parameter');

		// Construct the token exchange URL without the redirect_uri
		const tokenExchangeUrl =
			`https://graph.facebook.com/v22.0/oauth/access_token?` +
			`client_id=${encodeURIComponent(appId)}&` +
			`client_secret=${encodeURIComponent(appSecret)}&` +
			`code=${encodeURIComponent(code)}`;

		console.log('Token exchange URL (sanitized):', tokenExchangeUrl.replace(appSecret, 'REDACTED'));

		const tokenResponse = await fetch(tokenExchangeUrl, {
			method: 'GET'
		});

		const tokenData = await tokenResponse.json();
		console.log('Token exchange response:', tokenData);

		if (!tokenResponse.ok) {
			throw new Error(`Failed to exchange code: ${JSON.stringify(tokenData)}`);
		}

		// Get WhatsApp Business Account details using the token
		const wbaResponse = await fetch(
			'https://graph.facebook.com/v22.0/me/whatsapp_business_accounts?fields=id,phone_numbers',
			{
				headers: {
					Authorization: `Bearer ${tokenData.access_token}`
				}
			}
		);

		const wbaData = await wbaResponse.json();
		console.log('WhatsApp Business Account data:', wbaData);

		if (!wbaResponse.ok || !wbaData.data?.length) {
			throw new Error(`Failed to get WBA details: ${JSON.stringify(wbaData)}`);
		}

		const wbaId = wbaData.data[0].id;
		const phoneNumber =
			wbaData.data[0].phone_numbers?.[0]?.display_phone_number || wbaData.data[0].phone_numbers[0];

		const { supabase } = locals;

		// Store the OAuth token
		const { data: oauthToken, error: oauthError } = await supabase
			.from('oauth_tokens')
			.insert({
				company_id: companyId,
				company_data_source_id: wbaId,
				token_obj: {
					access_token: tokenData.access_token,
					token_type: tokenData.token_type,
					expires_in: tokenData.expires_in
				}
			})
			.select('id')
			.single();

		if (oauthError) {
			throw new Error(`Failed to store OAuth token: ${oauthError.message}`);
		}

		// Store the WhatsApp channel
		const { error: channelError } = await supabase.from('communication_channels').upsert({
			company_id: companyId,
			platform_type: 'whatsapp',
			channel_id: phoneNumber,
			oauth_token_id: oauthToken.id,
			dedicated_number: true,
			default_number: true
		});

		if (channelError) {
			throw new Error(`Failed to store WhatsApp channel: ${channelError.message}`);
		}

		// Return success response with phone number
		return json({
			success: true,
			phoneNumber,
			businessId: wbaId
		});
	} catch (error) {
		console.error('WhatsApp callback error:', error);
		return json(
			{
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
};
