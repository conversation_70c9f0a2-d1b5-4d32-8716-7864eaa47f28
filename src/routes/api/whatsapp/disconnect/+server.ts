import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async ({ url, locals }) => {
	try {
		const companyId = url.searchParams.get('companyId');
		if (!companyId) {
			throw new Error('Company ID is required');
		}

		const { supabase } = locals;

		// Get the current WhatsApp channel and its OAuth token
		const { data: channel, error: fetchError } = await supabase
			.from('communication_channels')
			.select(
				`
        channel_id,
        oauth_tokens (
          token_obj,
          company_data_source_id
        )
      `
			)
			.eq('company_id', companyId)
			.eq('platform_type', 'whatsapp')
			.single();

		if (fetchError) {
			throw new Error(`Failed to fetch channel: ${fetchError.message}`);
		}

		if (channel?.oauth_tokens) {
			const token = channel.oauth_tokens.token_obj;
			const businessId = channel.oauth_tokens.company_data_source_id;

			// Deauthorize the app from WhatsApp Business Platform
			const deauthResponse = await fetch(
				`https://graph.facebook.com/v19.0/${businessId}/deauthorize`,
				{
					method: 'DELETE',
					headers: {
						Authorization: `Bearer ${token.access_token}`
					}
				}
			);

			if (!deauthResponse.ok) {
				console.error('Failed to deauthorize from WhatsApp:', await deauthResponse.text());
			}

			// Delete the OAuth token
			const { error: tokenDeleteError } = await supabase
				.from('oauth_tokens')
				.delete()
				.eq('company_data_source_id', businessId);

			if (tokenDeleteError) {
				throw new Error(`Failed to delete OAuth token: ${tokenDeleteError.message}`);
			}

			// Delete the channel from database
			const { error: channelDeleteError } = await supabase
				.from('communication_channels')
				.delete()
				.eq('company_id', companyId)
				.eq('platform_type', 'whatsapp');

			if (channelDeleteError) {
				throw new Error(`Failed to delete channel: ${channelDeleteError.message}`);
			}
		}

		return json({ success: true });
	} catch (error) {
		console.error('WhatsApp disconnect error:', error);
		return json(
			{
				success: false,
				error: error instanceof Error ? error.message : 'Failed to disconnect WhatsApp'
			},
			{ status: 500 }
		);
	}
};
