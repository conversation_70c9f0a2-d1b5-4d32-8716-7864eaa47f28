import type { LayoutServerLoad } from "./$types";

export const load: LayoutServerLoad = async (
	{ depends, locals: { safeGetSession, supabase }, cookies },
) => {
	const { session, user, userCompanies } = await safeGetSession();
	depends("supabase:db:companies");

	const { data: profile } = await supabase.from("base_users").select("*").eq(
		"id",
		user?.id,
	).single();

	return {
		session,
		user,
		userCompanies,
		profile,
		cookies: cookies.getAll(),
	};
};
