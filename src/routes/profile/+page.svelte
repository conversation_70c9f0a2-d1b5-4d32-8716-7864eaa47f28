<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Avatar, AvatarImage, AvatarFallback } from '$lib/components/ui/avatar';
	import { Separator } from '$lib/components/ui/separator';
	import { supabase } from '$lib/supabaseClient';
	import type { PageData } from './$types';
	import type { Database } from '../../../database.types';
	import { toast } from 'svelte-sonner';
	import type { Json } from '../../../database.types';

	type BaseUser = Database['public']['Tables']['base_users']['Row'];
	type CompanyMember = Database['public']['Tables']['company_members']['Row'] & {
		companies: {
			id: string;
			name: string;
			data_sources: Array<{
				id: string;
				platform_type: Database['public']['Enums']['platform_type'];
				site_id: string | null;
				display_name: string | null;
			}>;
			communication_channels: Array<{
				id: string;
				platform_type: Database['public']['Enums']['platform_type'];
			}>;
		};
		user_platform_profiles: Array<{
			id: string;
			platform_type: Database['public']['Enums']['platform_type'];
			platform_user_id: string;
			is_primary: boolean | null;
			metadata: Json | null;
		}>;
	};

	export let data: PageData;

	let { user, userData, companyMemberships, errorMessage } = data;

	console.log('[Profile Client] Received data:', {
		user,
		userData,
		companyMemberships,
		errorMessage
	});

	if (companyMemberships) {
		console.log(
			'[Profile Client] Company memberships details:',
			companyMemberships.map((membership) => ({
				id: membership.id,
				companyName: membership.companies?.name,
				role: membership.role,
				isActive: membership.is_active,
				dataSources: membership.companies?.data_sources,
				communicationChannels: membership.companies?.communication_channels,
				platformProfiles: membership.user_platform_profiles
			}))
		);
	}

	let name = userData?.display_name || '';
	let email = userData?.email || '';
	let avatarUrl = userData?.avatar_url || '';
	let bio = userData?.bio || '';
	let isUploading = false;

	console.log('[Profile Client] Initialized user fields:', {
		name,
		email,
		avatarUrl,
		bio
	});

	let formError = '';
	let formSuccess = '';

	let whatsappNumberInputs: { [key: string]: string } = {};
	let savingWhatsapp: { [key: string]: boolean } = {};
	let editingWhatsapp: { [key: string]: boolean } = {};

	function handleError(message: string) {
		formError = message;
		formSuccess = '';
	}

	function handleSuccess(message: string) {
		formSuccess = message;
		formError = '';
	}

	async function handleAvatarChange(event: Event) {
		const input = event.target as HTMLInputElement;
		if (!input.files || !input.files[0]) return;

		try {
			isUploading = true;
			const file = input.files[0];

			if (avatarUrl) {
				const fileName = avatarUrl.split('/').pop();
				if (fileName) {
					await supabase.storage.from('avatars').remove([fileName]);
				}
			}

			const fileExt = file.name.split('.').pop();
			const fileName = `${Math.random()}.${fileExt}`;

			const { error: uploadError } = await supabase.storage.from('avatars').upload(fileName, file);

			if (uploadError) {
				throw uploadError;
			}

			const {
				data: { publicUrl }
			} = supabase.storage.from('avatars').getPublicUrl(fileName);

			const avatarUrlInput = document.createElement('input');
			avatarUrlInput.type = 'hidden';
			avatarUrlInput.name = 'avatar_url';
			avatarUrlInput.value = publicUrl;

			const form = document.querySelector('form');
			if (form) {
				const existingInput = form.querySelector('input[name="avatar_url"]') as HTMLInputElement;
				if (existingInput) {
					existingInput.value = publicUrl;
				} else {
					form.appendChild(avatarUrlInput);
				}
			}

			avatarUrl = publicUrl;
			handleSuccess('Profile picture uploaded. Click Save Changes to update your profile.');
		} catch (error) {
			console.error('Avatar upload error:', error);
			handleError(error instanceof Error ? error.message : 'Error updating profile picture');
		} finally {
			isUploading = false;
		}
	}

	async function handlePlatformProfileUpdate(
		companyMemberId: string,
		platformType: Database['public']['Enums']['platform_type'],
		value: string
	) {
		try {
			const formData = new FormData();
			formData.append('company_member_id', companyMemberId);
			formData.append('platform_type', platformType);
			formData.append('platform_user_id', value);

			const response = await fetch('?/updatePlatformProfile', {
				method: 'POST',
				body: formData
			});

			const result = await response.json();
			if (result.error) throw new Error(result.error);
			handleSuccess('Platform profile updated successfully');
		} catch (error) {
			console.error('Error updating platform profile:', error);
			handleError(error instanceof Error ? error.message : 'Error updating platform profile');
		}
	}

	function getPlatformProfile(
		companyMember: CompanyMember,
		platformType: Database['public']['Enums']['platform_type']
	) {
		return (
			companyMember.user_platform_profiles?.find(
				(profile) => profile.platform_type === platformType
			)?.platform_user_id || ''
		);
	}

	// Initialize WhatsApp numbers and edit states when component mounts
	$: {
		if (companyMemberships) {
			companyMemberships.forEach((membership) => {
				const whatsappProfile = membership.user_platform_profiles?.find(
					(profile) => profile.platform_type === 'whatsapp'
				);

				// Initialize the input value with existing number or empty string
				whatsappNumberInputs[membership.id] = whatsappProfile?.platform_user_id || '';

				// Start in edit mode only if there's no number
				editingWhatsapp[membership.id] = !whatsappProfile?.platform_user_id;
			});
		}
	}

	function hasDataSourceType(
		companyMember: CompanyMember,
		type: Database['public']['Enums']['platform_type']
	) {
		return companyMember.companies?.data_sources?.some((ds) => ds.platform_type === type) || false;
	}

	function hasCommunicationChannel(
		companyMember: CompanyMember,
		type: Database['public']['Enums']['platform_type']
	) {
		return (
			companyMember.companies?.communication_channels?.some((cc) => cc.platform_type === type) ||
			false
		);
	}

	function getSharePointDataSource(companyMember: CompanyMember) {
		return companyMember.companies?.data_sources?.find((ds) => ds.platform_type === 'sharepoint');
	}

	function getSharePointProfile(companyMember: CompanyMember) {
		return companyMember.user_platform_profiles?.find(
			(profile) => profile.platform_type === 'sharepoint'
		);
	}

	async function handleSharePointSignIn(companyId: string, companyMemberId: string) {
		try {
			console.log('[SharePoint Connect] Initiating SharePoint connection', {
				companyId: companyId || 'No company ID (personal connection)',
				companyMemberId,
				timestamp: new Date().toISOString()
			});

			// Encode state with both companyId and companyMemberId using btoa
			const state = btoa(
				JSON.stringify({
					companyId,
					companyMemberId,
					returnUrl: '/profile'
				})
			);

			// Redirect to the add-data-source endpoint
			window.location.href = `/auth/add-data-source?state=${state}`;
		} catch (error) {
			console.error('[SharePoint Connect] Connection error', {
				error: error instanceof Error ? error.message : 'Unknown error',
				stack: error instanceof Error ? error.stack : undefined,
				timestamp: new Date().toISOString()
			});

			toast.error(
				error instanceof Error
					? error.message
					: 'Failed to connect SharePoint account. Please try again.'
			);
		}
	}

	async function handleWhatsAppUpdate(
		companyMemberId: string,
		platformType: Database['public']['Enums']['platform_type'],
		value: string
	) {
		try {
			console.log('[WhatsApp Update] Starting update:', { companyMemberId, platformType, value });
			savingWhatsapp[companyMemberId] = true;

			// Validate phone number format
			if (!/^\d{11,}$/.test(value)) {
				throw new Error(
					'Phone number must be at least 11 digits with country code (e.g. ************)'
				);
			}

			const formData = new FormData();
			formData.append('company_member_id', companyMemberId);
			formData.append('platform_type', platformType);
			formData.append('platform_user_id', value);

			console.log('[WhatsApp Update] Sending request with data:', {
				company_member_id: companyMemberId,
				platform_type: platformType,
				platform_user_id: value
			});

			const response = await fetch('?/updatePlatformProfile', {
				method: 'POST',
				body: formData
			});

			const result = await response.json();
			console.log('[WhatsApp Update] Received response:', result);

			if (!response.ok) {
				throw new Error(result.error || 'Failed to update WhatsApp number');
			}

			if (result.type === 'failure') {
				throw new Error(result.data?.[1] || 'Failed to update WhatsApp number');
			}

			toast.success('WhatsApp number updated successfully');

			// Update the stored platform profile
			if (companyMemberships) {
				const membership = companyMemberships.find((m) => m.id === companyMemberId);
				if (membership) {
					const profile = membership.user_platform_profiles?.find(
						(p) => p.platform_type === 'whatsapp'
					);
					if (profile) {
						profile.platform_user_id = value;
					} else {
						membership.user_platform_profiles = [
							...(membership.user_platform_profiles || []),
							{
								id: crypto.randomUUID(), // Add an ID for the new profile
								platform_type: 'whatsapp',
								platform_user_id: value,
								is_primary: true,
								metadata: null
							}
						];
					}
				}
			}

			// Exit edit mode after successful save
			editingWhatsapp[companyMemberId] = false;
		} catch (error) {
			console.error('[WhatsApp Update] Error:', error);
			toast.error(error instanceof Error ? error.message : 'Error updating WhatsApp number');
			// Stay in edit mode if there was an error
			editingWhatsapp[companyMemberId] = true;
		} finally {
			savingWhatsapp[companyMemberId] = false;
		}
	}

	function toggleWhatsAppEdit(memberId: string) {
		if (editingWhatsapp[memberId]) {
			// If we're currently editing, this is a save action
			handleWhatsAppUpdate(memberId, 'whatsapp', whatsappNumberInputs[memberId]);
		} else {
			// Just toggle edit mode without saving
			editingWhatsapp[memberId] = true;
		}
	}
</script>

<main class="container max-w-3xl py-6">
	{#if userData}
		<div class="space-y-6">
			<!-- Base Profile -->
			<Card>
				<CardHeader>
					<CardTitle>Personal Profile</CardTitle>
					<CardDescription>
						Manage your personal information that applies across all companies.
					</CardDescription>
				</CardHeader>
				<CardContent>
					{#if errorMessage}
						<div class="p-4 mb-4 rounded bg-destructive/15 text-destructive">
							{errorMessage}
						</div>
					{/if}
					{#if formSuccess}
						<div class="p-4 mb-4 text-green-500 rounded bg-green-500/15">
							{formSuccess}
						</div>
					{/if}

					<form method="POST" action="?/updateProfile" class="space-y-6">
						<div class="flex items-center gap-6">
							<Avatar class="w-20 h-20">
								<AvatarImage src={avatarUrl} alt={name} />
								<AvatarFallback>{name?.charAt(0)?.toUpperCase() ?? 'U'}</AvatarFallback>
							</Avatar>
							<div class="flex flex-col gap-2">
								<Label for="avatar">Profile Picture</Label>
								<Input
									id="avatar"
									type="file"
									accept="image/*"
									on:change={handleAvatarChange}
									disabled={isUploading}
								/>
								{#if isUploading}
									<p class="text-sm text-muted-foreground">Uploading...</p>
								{/if}
							</div>
						</div>

						<input type="hidden" name="avatar_url" value={avatarUrl} />

						<div class="grid gap-4">
							<div class="grid gap-2">
								<Label for="display_name">Name</Label>
								<Input
									type="text"
									id="display_name"
									name="display_name"
									bind:value={name}
									required
								/>
							</div>

							<div class="grid gap-2">
								<Label for="bio">Bio</Label>
								<Input type="text" id="bio" name="bio" bind:value={bio} />
							</div>

							<div class="grid gap-2">
								<Label>Email</Label>
								<p class="text-sm text-muted-foreground">{email}</p>
							</div>
						</div>

						<Button type="submit" class="w-full">Save Changes</Button>
					</form>
				</CardContent>
			</Card>

			<!-- Company Profiles -->
			{#if companyMemberships && companyMemberships.length > 0}
				<div class="space-y-4">
					<div>
						<h2 class="text-2xl font-semibold tracking-tight">Company Profiles</h2>
						<p class="text-muted-foreground">
							Manage your personal settings with regards to each company.
						</p>
					</div>

					<div class="grid gap-4">
						{#each companyMemberships as membership (membership.id)}
							<Card>
								<CardHeader>
									<CardTitle>{membership.companies?.name || 'Unknown Company'}</CardTitle>
									<CardDescription>
										{membership.role?.[0].toUpperCase()}{membership.role?.slice(1).toLowerCase()} · {membership.is_active
											? 'Active'
											: 'Inactive'}
									</CardDescription>
								</CardHeader>
								<CardContent class="space-y-6">
									<!-- Communication Channels Section -->
									<div class="space-y-4">
										<div class="flex items-center justify-between">
											<h4 class="text-sm font-medium">Communication Channels</h4>
											{#if membership.role === 'admin'}
												<Button
													variant="ghost"
													size="sm"
													href="/company/{membership.companies?.id}/manage-company"
												>
													Manage Channels
												</Button>
											{/if}
										</div>

										{#if membership.companies?.communication_channels?.length > 0}
											<!-- WhatsApp Integration -->
											{#if hasCommunicationChannel(membership, 'whatsapp')}
												<div class="grid gap-2">
													<Label>WhatsApp Number</Label>
													<div class="flex gap-2">
														<Input
															type="tel"
															bind:value={whatsappNumberInputs[membership.id]}
															placeholder="************"
															disabled={!editingWhatsapp[membership.id] ||
																savingWhatsapp[membership.id]}
														/>
														<Button
															variant="outline"
															size="default"
															disabled={savingWhatsapp[membership.id]}
															on:click={() => toggleWhatsAppEdit(membership.id)}
														>
															{#if savingWhatsapp[membership.id]}
																Saving...
															{:else if editingWhatsapp[membership.id]}
																Save
															{:else}
																Edit
															{/if}
														</Button>
													</div>
													<p class="text-sm text-muted-foreground">
														Enter your WhatsApp number with country code (e.g. ************)
													</p>
												</div>
											{:else}
												<div class="p-4 border rounded-md">
													<p class="text-sm text-muted-foreground">
														No communication channels configured yet.
													</p>
													<p class="mt-2 text-sm text-muted-foreground">
														All active users can use the company webchat for internal communication.
													</p>
													<div class="mt-4">
														<Button
															variant="outline"
															href="/company/{membership.companies?.id}/chat"
														>
															Open Webchat
														</Button>
													</div>
												</div>
											{/if}
										{/if}
									</div>

									<!-- Data Sources Section -->
									<div class="space-y-4">
										<div class="flex items-center justify-between">
											<h4 class="text-sm font-medium">Data Sources</h4>
											{#if membership.role === 'admin'}
												<Button
													variant="ghost"
													size="sm"
													href="/company/{membership.companies?.id}/vault"
												>
													Manage Sources
												</Button>
											{/if}
										</div>

										{#if membership.companies?.data_sources?.length > 0}
											<!-- SharePoint Integration -->
											{#if hasDataSourceType(membership, 'sharepoint')}
												<div class="p-4 border rounded-md">
													{#if getSharePointProfile(membership)}
														<div class="space-y-2">
															<div class="flex items-center gap-2">
																<div class="w-2 h-2 bg-green-500 rounded-full"></div>
																<p class="text-sm font-medium">SharePoint Connected</p>
															</div>
															<p class="text-sm text-muted-foreground">
																Account ID: {getSharePointProfile(membership)?.platform_user_id}
															</p>
															<Button
																variant="outline"
																size="sm"
																on:click={() =>
																	handleSharePointSignIn(membership.companies?.id, membership.id)}
															>
																Reconnect Account
															</Button>
														</div>
													{:else if getSharePointDataSource(membership)}
														<div class="space-y-2">
															<div class="flex items-center gap-2">
																<div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
																<p class="text-sm font-medium">SharePoint Not Connected</p>
															</div>
															<p class="text-sm text-muted-foreground">
																Connect your account to access company documents
															</p>
															<Button
																variant="outline"
																size="sm"
																on:click={() =>
																	handleSharePointSignIn(membership.companies?.id, membership.id)}
															>
																Connect Account
															</Button>
														</div>
													{/if}
												</div>
											{/if}
										{:else}
											<div class="p-4 border rounded-md">
												<p class="text-sm text-muted-foreground">
													No data sources have been configured for this company.
												</p>
												{#if membership.role === 'admin'}
													<div class="mt-4">
														<Button
															variant="outline"
															href="/company/{membership.companies?.id}/vault/new-data-source"
														>
															Add First Data Source
														</Button>
													</div>
												{/if}
											</div>
										{/if}
									</div>
								</CardContent>
							</Card>
						{/each}
					</div>
				</div>
			{/if}
		</div>
	{/if}
</main>
