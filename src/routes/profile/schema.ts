// src/routes/company/[companyId]/profile/schema.ts
import { z } from "zod";

// Define the form schema
export const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  phone_number: z.string()
    .regex(
      /^\d{11}$/,
      "Phone number must be in the format 00123123123(where 00 is the Country Code and the rest is the Phone Number)",
    ),
});

// Export the type for TypeScript
export type FormSchema = z.infer<typeof formSchema>;
