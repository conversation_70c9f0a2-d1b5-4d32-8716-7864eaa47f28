import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import type { Database } from '../../../database.types';

export const load: PageServerLoad = async ({ locals: { supabase, safeGetSession } }) => {
	const { session } = await safeGetSession();

	if (!session) {
		throw redirect(303, '/auth');
	}

	const { data: userData, error: userError } = await supabase
		.from('base_users')
		.select(
			`
			id, 
			display_name, 
			avatar_url, 
			bio,
			email
		`
		)
		.eq('id', session.user.id)
		.single();

	if (userError) {
		return {
			user: session.user,
			userData: null,
			errorMessage: 'Failed to load user data'
		};
	}

	// First, get the company memberships
	const { data: companyMemberships, error: membershipsError } = await supabase
		.from('company_members')
		.select(
			`
			id,
			user_id,
			company_id,
			role,
			display_name,
			avatar_url,
			is_active,
			created_at,
			updated_at
		`
		)
		.eq('user_id', session.user.id);

	if (membershipsError) {
		return {
			user: session.user,
			userData,
			companyMemberships: [],
			success: false
		};
	}

	// Then, for each membership, get the associated company data
	const enrichedMemberships = await Promise.all(
		(companyMemberships || []).map(async (membership) => {
			// Get company data
			const { data: companyData } = await supabase
				.from('companies')
				.select(
					`
					id,
					name
				`
				)
				.eq('id', membership.company_id)
				.single();

			// Get data sources
			const { data: dataSources } = await supabase
				.from('data_sources')
				.select(
					`
					id,
					platform_type,
					site_id,
					display_name
				`
				)
				.eq('company_id', membership.company_id);

			// Get communication channels
			const { data: communicationChannels } = await supabase
				.from('communication_channels')
				.select(
					`
					id,
					platform_type
				`
				)
				.eq('company_id', membership.company_id);

			// Get user platform profiles
			const { data: platformProfiles } = await supabase
				.from('user_platform_profiles')
				.select(
					`
					id,
					platform_type,
					platform_user_id,
					is_primary,
					metadata
				`
				)
				.eq('company_member_id', membership.id);

			return {
				...membership,
				companies: companyData
					? {
							...companyData,
							data_sources: dataSources || [],
							communication_channels: communicationChannels || []
						}
					: null,
				user_platform_profiles: platformProfiles || []
			};
		})
	);

	return {
		user: session.user,
		userData,
		companyMemberships: enrichedMemberships,
		success: true
	};
};

export const actions: Actions = {
	updateProfile: async ({ request, locals: { supabase, safeGetSession } }) => {
		const { session } = await safeGetSession();
		if (!session) {
			throw redirect(303, '/');
		}

		const formData = await request.formData();
		const display_name = formData.get('display_name') as string;
		const avatar_url = formData.get('avatar_url') as string;
		const bio = formData.get('bio') as string;

		// Update user metadata in auth
		const { error: authError } = await supabase.auth.updateUser({
			data: {
				avatar_url,
				full_name: display_name
			}
		});

		if (authError) {
			console.error('Error updating user metadata:', authError);
			return fail(500, {
				display_name,
				avatar_url,
				bio,
				error: 'Failed to update profile metadata'
			});
		}

		// Update user profile
		const { error } = await supabase
			.from('base_users')
			.update({
				display_name,
				avatar_url,
				bio,
				updated_at: new Date().toISOString()
			})
			.eq('id', session.user.id);

		if (error) {
			return fail(500, {
				display_name,
				avatar_url,
				bio,
				error: 'Failed to update profile'
			});
		}

		return {
			success: true,
			message: 'Profile updated successfully'
		};
	},

	updatePlatformProfile: async ({ request, locals: { supabase, safeGetSession } }) => {
		try {
			console.log('[Platform Profile Update] Starting update');
			const { session } = await safeGetSession();
			if (!session) {
				console.error('[Platform Profile Update] No session found');
				throw redirect(303, '/');
			}

			const formData = await request.formData();
			const company_member_id = formData.get('company_member_id') as string;
			const platform_type = formData.get(
				'platform_type'
			) as Database['public']['Enums']['platform_type'];
			const platform_user_id = formData.get('platform_user_id') as string;

			console.log('[Platform Profile Update] Received data:', {
				company_member_id,
				platform_type,
				platform_user_id,
				user_id: session.user.id
			});

			// Validate required fields
			if (!company_member_id || !platform_type || !platform_user_id) {
				console.error('[Platform Profile Update] Missing required fields');
				return fail(400, {
					error: 'Missing required fields',
					data: ['Missing required fields']
				});
			}

			// Validate that the company_member_id belongs to the current user
			const { data: membershipCheck, error: membershipError } = await supabase
				.from('company_members')
				.select('id')
				.eq('id', company_member_id)
				.eq('user_id', session.user.id)
				.single();

			console.log('[Platform Profile Update] Membership check:', {
				membershipCheck,
				membershipError
			});

			if (membershipError || !membershipCheck) {
				console.error('[Platform Profile Update] Invalid company member:', membershipError);
				return fail(403, {
					error: "You don't have permission to update this profile",
					data: ['Invalid company member']
				});
			}

			// Perform the upsert
			const { data: upsertData, error: upsertError } = await supabase
				.from('user_platform_profiles')
				.upsert(
					{
						company_member_id,
						platform_type,
						platform_user_id,
						is_primary: true,
						updated_at: new Date().toISOString()
					},
					{
						onConflict: 'company_member_id,platform_type'
					}
				);

			console.log('[Platform Profile Update] Upsert result:', {
				upsertData,
				upsertError
			});

			if (upsertError) {
				console.error('[Platform Profile Update] Upsert error:', upsertError);
				return fail(500, {
					error: 'Failed to update platform profile',
					data: [upsertError, 'Failed to update platform profile']
				});
			}

			return {
				type: 'success',
				status: 200,
				data: {
					success: true,
					message: 'Platform profile updated successfully'
				}
			};
		} catch (error) {
			console.error('[Platform Profile Update] Unexpected error:', error);
			return fail(500, {
				error: error instanceof Error ? error.message : 'An unexpected error occurred',
				data: [{ error: 1 }, 'An unexpected error occurred']
			});
		}
	},

	signout: async ({ locals: { supabase, safeGetSession } }) => {
		const { session } = await safeGetSession();
		if (session) {
			await supabase.auth.signOut();
			throw redirect(303, '/');
		}
	}
};
