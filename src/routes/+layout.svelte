<script lang="ts">
	import '../app.css';
	import { goto, invalidate } from '$app/navigation';
	import { onMount, onDestroy } from 'svelte';
	import Nav from './Nav.svelte';
	import { companyStore, setCompanyId } from '$lib/stores/companyStore';
	import { Toaster } from '$lib/components/ui/sonner';
	import { ModeWatcher, mode } from 'mode-watcher';
	import { page } from '$app/stores';

	$: themeName = $mode === 'dark' ? 'theme-totm-dark' : 'theme-totm-light';

	export let data;

	$: ({ session, supabase, user, userCompanies, profile } = data);
	$: navData = { user, userCompanies, profile };

	let cleanup: (() => void) | undefined;

	onMount(() => {
		if (!session) {
			goto('/auth');
		}
		const {
			data: { subscription }
		} = supabase.auth.onAuthStateChange((event, newSession) => {
			if (event === 'SIGNED_OUT') {
				setCompanyId(null);
			}
			if (newSession?.expires_at !== session?.expires_at) {
				invalidate('supabase:auth');
			}
		});

		// Listen for cookie changes and update the store accordingly
		const updateCompanyIdFromCookies = () => {
			const match = document.cookie.match(new RegExp('(?:^|; )companyId=([^;]*)'));
			const companyId = match ? decodeURIComponent(match[1]) : null;
			companyStore.set(companyId);
		};

		// Initial sync
		updateCompanyIdFromCookies();

		// Set up interval for cookie sync
		const interval = setInterval(updateCompanyIdFromCookies, 5000);

		// Store cleanup function
		cleanup = () => {
			subscription.unsubscribe();
			clearInterval(interval);
		};

		return cleanup;
	});

	onDestroy(() => {
		if (cleanup) {
			cleanup();
		}
	});

	// Reactive statement to update companyStore when companyId changes in the route
	$: {
		const companyId = $page.params.companyId;
		if (companyId) {
			setCompanyId(companyId);
		}
	}
</script>

<ModeWatcher defaultMode="dark" />

<html class={themeName} lang="en">
	<body class="flex flex-col min-h-screen">
		<Nav {navData} />
		<slot />
		<Toaster />
	</body>
</html>
