import { createBrowserC<PERSON>, createServer<PERSON><PERSON>, isBrowser } from '@supabase/ssr';
import { PUBLIC_SUPABASE_ANON_KEY, PUBLIC_SUPABASE_URL } from '$env/static/public';
import type { LayoutLoad } from './$types';

export const load: LayoutLoad = async ({ url, data, depends, fetch }) => {
	/**
	 * Declare a dependency so the layout can be invalidated, for example, on
	 * session refresh.
	 */
	depends('supabase:auth');
	depends('company:db');

	const supabase = isBrowser()
		? createBrowserClient(PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY, {
				global: {
					fetch
				}
			})
		: createServerClient(PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY, {
				global: {
					fetch
				},
				cookies: {
					getAll() {
						return data.cookies;
					}
				}
			});

	/**
	 * It's fine to use `getSession` here, because on the client, `getSession` is
	 * safe, and on the server, it reads `session` from the `LayoutData`, which
	 * safely checked the session using `safeGetSession`.
	 */
	const {
		data: { session }
	} = await supabase.auth.getSession();

	const {
		data: { user }
	} = await supabase.auth.getUser();

	const { data: profile } = await supabase
		.from('base_users')
		.select('*')
		.eq('id', user?.id)
		.single();

	// Retrieve companyId from cookies
	const companyId =
		url.searchParams.get('company') ||
		(typeof document !== 'undefined'
			? document.cookie.match(new RegExp('(?:^|; )companyId=([^;]*)'))?.[1]
			: null);

	return {
		companyId: companyId ? decodeURIComponent(companyId) : null,
		session,
		supabase,
		user,
		userCompanies: data.userCompanies,
		profile
	};
};
