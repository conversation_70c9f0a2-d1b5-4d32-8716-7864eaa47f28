<script lang="ts">
  import { onMount } from 'svelte';

  let companyId = '';
  let fileExternalId = '';
  let driveId = '';
  let dataSourceId = '';
  
  let files: any[] = [];
  let users: any[] = [];
  
  let filesLoading = false;
  let usersLoading = false;
  let error = '';

  // Function to fetch company files
  async function fetchCompanyFiles() {
    if (!companyId) {
      error = 'Company ID is required';
      return;
    }
    
    error = '';
    filesLoading = true;
    
    try {
      const response = await fetch(`/api/file-access?company_id=${encodeURIComponent(companyId)}`);
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch company files');
      }
      
      files = data.files;
    } catch (err) {
      error = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('Error fetching files:', err);
    } finally {
      filesLoading = false;
    }
  }

  // Function to fetch file access users
  async function fetchFileAccessUsers() {
    if (!fileExternalId) {
      error = 'File External ID is required';
      return;
    }
    
    if (!driveId) {
      error = 'Drive ID is required';
      return;
    }
    
    if (!dataSourceId) {
      error = 'Data Source ID is required';
      return;
    }
    
    error = '';
    usersLoading = true;
    
    try {
      const response = await fetch('/api/file-access', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          fileExternalId,
          driveId,
          dataSourceId
        })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch file access users');
      }
      
      users = data.users;
    } catch (err) {
      error = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('Error fetching users:', err);
    } finally {
      usersLoading = false;
    }
  }

  // When a file is selected, auto-fill its values for the file access form
  async function selectFile(file: any) {
    if (file.external_id && file.data_source_id) {
      fileExternalId = file.external_id;
      dataSourceId = file.data_source_id;
      
      // Fetch the data source to get its drive_id
      try {
        const response = await fetch(`/api/file-access/data-sources?file_id=${encodeURIComponent(file.id)}`);
        const data = await response.json();
        
        if (!response.ok) {
          throw new Error(data.error || 'Failed to fetch data source');
        }
        
        if (data.dataSource) {
          // Set drive_id from data source
          if (data.dataSource.drive_id) {
            driveId = data.dataSource.drive_id;
          } else {
            error = 'Data source has no drive ID';
          }
        } else {
          error = 'No data source information found';
        }
      } catch (err) {
        error = err instanceof Error ? err.message : 'Unknown error occurred';
        console.error('Error fetching data source:', err);
      }
    }
  }
</script>

<div class="container p-4 mx-auto">
  <h1 class="mb-6 text-2xl font-bold">File Access Management</h1>
  
  {#if error}
    <div class="px-4 py-3 mb-4 text-red-700 bg-red-100 border border-red-400 rounded">
      <p>{error}</p>
    </div>
  {/if}
  
  <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
    <!-- Company Files Section -->
    <div class="p-6 bg-white rounded-lg shadow">
      <h2 class="mb-4 text-xl font-semibold">Company Files</h2>
      
      <div class="mb-4">
        <label class="block mb-1 text-sm font-medium text-gray-700" for="companyId">
          Company ID
        </label>
        <input
          id="companyId"
          type="text"
          bind:value={companyId}
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
          placeholder="Enter company ID"
        />
      </div>
      
      <button
        on:click={fetchCompanyFiles}
        disabled={filesLoading}
        class="w-full px-4 py-2 font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
      >
        {filesLoading ? 'Loading...' : 'Get Company Files'}
      </button>
      
      {#if files.length > 0}
        <div class="mt-4">
          <h3 class="mb-2 text-lg font-medium">Files ({files.length})</h3>
          <div class="overflow-y-auto border rounded-md max-h-80">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="sticky top-0 bg-gray-50">
                <tr>
                  <th class="px-3 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Name</th>
                  <th class="px-3 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">External ID</th>
                  <th class="px-3 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                {#each files as file}
                  <tr>
                    <td class="px-3 py-2 text-sm text-gray-900 whitespace-nowrap">{file.name}</td>
                    <td class="px-3 py-2 text-sm text-gray-500 whitespace-nowrap">{file.external_id || 'N/A'}</td>
                    <td class="px-3 py-2 text-sm text-gray-500 whitespace-nowrap">
                      <button
                        on:click={() => selectFile(file)}
                        class="text-indigo-600 hover:text-indigo-900"
                        disabled={!file.external_id}
                      >
                        Check Access
                      </button>
                    </td>
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>
        </div>
      {/if}
    </div>
    
    <!-- File Access Users Section -->
    <div class="p-6 bg-white rounded-lg shadow">
      <h2 class="mb-4 text-xl font-semibold">File Access Users</h2>
      
      <div class="mb-4">
        <label class="block mb-1 text-sm font-medium text-gray-700" for="fileExternalId">
          File External ID
        </label>
        <input
          id="fileExternalId"
          type="text"
          bind:value={fileExternalId}
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
          placeholder="Enter file external ID"
        />
      </div>
      
      <div class="mb-4">
        <label class="block mb-1 text-sm font-medium text-gray-700" for="driveId">
          Drive ID <span class="text-xs text-gray-500">(from data source)</span>
        </label>
        <input
          id="driveId"
          type="text"
          bind:value={driveId}
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
          placeholder="Enter drive ID"
        />
      </div>
      
      <div class="mb-4">
        <label class="block mb-1 text-sm font-medium text-gray-700" for="dataSourceId">
          Data Source ID <span class="text-xs text-gray-500">(from data source)</span>
        </label>
        <input
          id="dataSourceId"
          type="text"
          bind:value={dataSourceId}
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-gray-50"
          placeholder="Enter data source ID"
          readonly
        />
      </div>
      
      <button
        on:click={fetchFileAccessUsers}
        disabled={usersLoading}
        class="w-full px-4 py-2 font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
      >
        {usersLoading ? 'Loading...' : 'Get File Access Users'}
      </button>
      
      {#if users.length > 0}
        <div class="mt-4">
          <h3 class="mb-2 text-lg font-medium">Users with Access ({users.length})</h3>
          <div class="overflow-y-auto border rounded-md max-h-80">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="sticky top-0 bg-gray-50">
                <tr>
                  <th class="px-3 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Name</th>
                  <th class="px-3 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Email</th>
                  <th class="px-3 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Access Type</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                {#each users as user}
                  <tr>
                    <td class="px-3 py-2 text-sm text-gray-900 whitespace-nowrap">{user.displayName}</td>
                    <td class="px-3 py-2 text-sm text-gray-500 whitespace-nowrap">{user.email}</td>
                    <td class="px-3 py-2 text-sm text-gray-500 whitespace-nowrap">
                      {user.accessType === 'group' ? `Via ${user.groupName} group` : 'Direct'}
                    </td>
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>
        </div>
      {/if}
    </div>
  </div>
</div> 