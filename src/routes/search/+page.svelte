<!-- Example of integrating the filterSearchResultsByAccess function -->
<script lang="ts">
  import { onMount } from 'svelte';
  import { filterSearchResultsByAccess } from '$lib/utils/searchUtils';
  import { supabase } from '$lib/supabaseClient';
  
  let searchQuery = '';
  let searchResults: any[] = [];
  let filteredResults: any[] = [];
  let userEmail = '';
  let isLoading = false;
  let isFiltering = false;
  let error = '';
  let limit = 20;
  let companyId = '';
  let similarityThreshold = 0.1;
  let additionalFilters = '{}';
  
  // Example filter for placeholder
  const filterExample = '{"tag": "important"}';
  
  // Perform search using Supabase RPC
  async function performSearch() {
    if (!searchQuery.trim()) {
      error = 'Please enter a search query';
      return;
    }
    
    if (!companyId) {
      error = 'Company ID is required';
      return;
    }
    
    error = '';
    isLoading = true;
    
    try {
      // First get the embedding for the search query from our API
      const embeddingResponse = await fetch('/api/embeddings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ text: searchQuery })
      });
      
      if (!embeddingResponse.ok) {
        const errorData = await embeddingResponse.json();
        throw new Error(errorData.error || 'Failed to generate embeddings');
      }
      
      const { embedding } = await embeddingResponse.json();
      
      if (!embedding) {
        throw new Error('No embedding was generated for the query');
      }

      // Parse additional filters if provided
      let filterJson = {};
      try {
        if (additionalFilters.trim() !== '{}' && additionalFilters.trim() !== '') {
          filterJson = JSON.parse(additionalFilters);
        }
      } catch (e) {
        console.warn('Invalid filter JSON, using empty filter');
      }

      // Call the document matching function with the generated embedding
      const { data, error: rpcError } = await supabase.rpc('search_documents_by_company', {
        query_embedding: embedding,
        company_id: companyId,
        match_count: limit,
        similarity_threshold: similarityThreshold,
        filter: filterJson
      });
      
      if (rpcError) {
        throw new Error(rpcError.message || 'Failed to search');
      }
      
      searchResults = data || [];
      console.log('Search results:', searchResults);
      
      // If we have a user email, filter results by access
      if (userEmail) {
        await filterResultsByUserAccess();
      } else {
        filteredResults = searchResults;
      }
      
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while searching';
      error = errorMessage;
      console.error('Search error:', err);
    } finally {
      isLoading = false;
    }
  }
  
  // Function to filter results by user access
  async function filterResultsByUserAccess() {
    if (!userEmail || !searchResults.length) return;
    
    isFiltering = true;
    
    try {
      // Extract file IDs from search results - use metadata.file_id which contains the external_id
      const fileIds = searchResults
        .filter(result => result.metadata?.file_id)
        .map(result => result.metadata.file_id);
      
      console.log(`Found ${fileIds.length} files to check access for`);
      
      if (fileIds.length === 0) {
        console.warn('No valid file IDs found in search results metadata');
        filteredResults = [];
        return;
      }
      
      filteredResults = await filterSearchResultsByAccess(searchResults, userEmail);
    } catch (err: unknown) {
      console.error('Error filtering results:', err);
      filteredResults = searchResults; // Fallback to unfiltered results
    } finally {
      isFiltering = false;
    }
  }
  
  // Update filtered results when user email changes
  $: if (userEmail && searchResults.length) {
    filterResultsByUserAccess();
  }
</script>

<div class="container p-4 mx-auto">
  <h1 class="mb-4 text-2xl font-bold">Search with Access Control</h1>
  
  <div class="mb-4">
    <input
      type="text"
      bind:value={searchQuery}
      placeholder="Enter search query..."
      class="w-full p-2 mb-2 border rounded"
    />
    
    <input
      type="text"
      bind:value={companyId}
      placeholder="Enter company ID (required)..."
      class="w-full p-2 mb-2 border rounded"
    />
    
    <input
      type="email"
      bind:value={userEmail}
      placeholder="Enter user email to filter by access..."
      class="w-full p-2 mb-2 border rounded"
    />
    
    <div class="flex items-center mb-2">
      <label for="limit" class="mr-2">Results limit:</label>
      <input
        id="limit"
        type="number"
        bind:value={limit}
        min="1"
        max="100"
        class="w-24 p-2 border rounded"
      />
    </div>
    
    <div class="flex items-center mb-2">
      <label for="threshold" class="mr-2">Similarity threshold:</label>
      <input
        id="threshold"
        type="number"
        bind:value={similarityThreshold}
        min="0.01"
        max="0.99"
        step="0.01"
        class="w-24 p-2 border rounded"
      />
    </div>
    
    <div class="mb-2">
      <label for="filters" class="block mb-1">Additional filters (JSON):</label>
      <textarea
        id="filters"
        bind:value={additionalFilters}
        placeholder="Enter JSON filter here"
        class="w-full h-20 p-2 font-mono text-sm border rounded"
      ></textarea>
      <p class="mt-1 text-xs text-gray-500">Example: {filterExample}</p>
    </div>
    
    <button
      on:click={performSearch}
      disabled={isLoading}
      class="p-2 text-white bg-blue-500 rounded hover:bg-blue-600 disabled:opacity-50"
    >
      {isLoading ? 'Searching...' : 'Search'}
    </button>
  </div>
  
  {#if error}
    <div class="p-2 mb-4 text-red-700 bg-red-100 border border-red-400 rounded">
      {error}
    </div>
  {/if}
  
  {#if isFiltering}
    <div class="p-4 text-center">
      <p>Filtering results by user access...</p>
    </div>
  {/if}
  
  <div class="results">
    {#if filteredResults.length}
      <p class="mb-2">
        Showing {filteredResults.length} {userEmail ? 'accessible ' : ''}results
        {#if userEmail && searchResults.length !== filteredResults.length}
          (filtered from {searchResults.length} total results)
        {/if}
      </p>
      
      <ul class="space-y-4">
        {#each filteredResults as result}
          <li class="p-4 border rounded shadow">
            <h2 class="font-bold">{result.file_name || result.metadata?.name || 'Unnamed document'}</h2>
            {#if result.content}
              <p class="mt-1 text-sm text-gray-700">{result.content.substring(0, 200)}...</p>
            {/if}
            <div class="flex flex-col mt-2">
              <div class="flex justify-between">
                <p class="text-sm text-gray-500">Score: {result.similarity?.toFixed(4) || 'N/A'}</p>
                <p class="text-sm text-gray-500">ID: {result.id}</p>
              </div>
              {#if result.file_path}
                <p class="mt-1 text-xs text-gray-500">Path: {result.file_path}</p>
              {/if}
              {#if result.file_type}
                <p class="text-xs text-gray-500">Type: {result.file_type}</p>
              {/if}
            </div>
          </li>
        {/each}
      </ul>
    {:else if !isLoading && !isFiltering}
      <p>No results found.</p>
    {/if}
  </div>
</div> 