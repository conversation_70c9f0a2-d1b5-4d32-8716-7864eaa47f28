import { redirect } from "@sveltejs/kit";
import type { PageServerLoad } from "./$types";
import { deleteMatchingCookies } from "$lib/utils";

export const load: PageServerLoad = async ({ cookies }) => {
  // Clear all Supabase auth cookies
  deleteMatchingCookies(cookies, "sb-");
  cookies.delete("code_verifier", { path: "/" });

  // Redirect to auth page after cleaning up
  throw redirect(303, "/auth");
};
