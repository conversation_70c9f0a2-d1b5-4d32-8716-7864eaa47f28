<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import Logo from '$lib/components/Logo.svelte';
	import { goto } from '$app/navigation';
	import * as Card from '$lib/components/ui/card';

	// Sample data based on the pitch
	const features = [
		{
			title: 'Instant Answers',
			description:
				'Get immediate responses to your queries about meetings, projects, and contracts.'
		},
		{
			title: 'Seamless Integration',
			description: 'Integrate with Slack or WhatsApp for a smooth communication experience.'
		},
		{
			title: 'Comprehensive Insights',
			description: 'Access reports, quotes, invoices, and training materials effortlessly.'
		}
	];

	const departments = [
		{ name: 'TOTM For HR', href: '/totm-for-hr' },
		{ name: 'TOTM For Sales', href: '/totm-for-sales' },
		{ name: 'TOTM For Product Marketing', href: '/totm-for-product-marketing' }
	];

	// Function to handle navigation to the auth page
	const handleGetStarted = () => {
		goto('/auth'); // Redirect to the authentication page
	};
</script>
<div class="flex flex-col items-center justify-center w-full gap-16">
<div class="flex flex-col items-center justify-center w-full">
	<Logo class="h-16 mx-auto mb-4" />
	<h1 class="mb-4 text-4xl font-bold">Welcome to TOTM</h1>
	<p class="mb-6 text-lg">
		Chat with your business’ meetings, projects, contracts, and more on Slack or WhatsApp!
	</p>
	<Button class="mt-4" variant="secondary" on:click={handleGetStarted}>Get Started</Button>
</div>

<section class="flex flex-row justify-around w-full gap-8 p-16 bg-accent">
	{#each features as feature}
		<Card.Root>
			<Card.Header>
				<h2 class="text-2xl font-semibold">{feature.title}</h2>
			</Card.Header>
			<Card.Content>
				<p>{feature.description}</p>
			</Card.Content>
		</Card.Root>
	{/each}
</section>

<div class="flex flex-col items-center justify-center w-full gap-4 p-4 bg-tertiary">
	<h2 class="text-xl font-semibold">Explore the Value of TOTM</h2>
	<DropdownMenu.Root>
		<DropdownMenu.Trigger asChild>
			<Button variant="secondary">Select Department</Button>
		</DropdownMenu.Trigger>
		<DropdownMenu.Content class="w-56">
			<DropdownMenu.Group>
				<DropdownMenu.Label>Departments</DropdownMenu.Label>
				<DropdownMenu.Separator />
				{#each departments as department}
					<DropdownMenu.Item>
						<a href={department.href} class="block px-4 py-2 hover:bg-gray-100">{department.name}</a
						>
					</DropdownMenu.Item>
				{/each}
			</DropdownMenu.Group>
		</DropdownMenu.Content>
	</DropdownMenu.Root>
</div>
</div>
