import type { PageServerLoad } from "./$types";

export const load: PageServerLoad = async ({ url }) => {
  // Get parameters from the URL
  const redirectUrl = url.searchParams.get("redirectUrl") || "/";
  const resourceType = url.searchParams.get("resourceType") || "resource";
  const requiredRole = url.searchParams.get("requiredRole") ||
    "appropriate permissions";
  const companyId = url.searchParams.get("companyId");

  return {
    redirectUrl,
    resourceType,
    requiredRole,
    companyId,
  };
};
