<script lang="ts">
  import { goto } from '$app/navigation';
  import { But<PERSON> } from '$lib/components/ui/button';
  
  function testAccessDenied() {
    const params = new URLSearchParams({
      redirectUrl: '/',
      resourceType: 'test resource',
      requiredRole: 'admin role',
      companyId: 'test-company-id'
    });
    goto(`/access-denied?${params.toString()}`);
  }
</script>

<div class="container py-10">
  <h1 class="text-2xl font-bold mb-6">Access Denied Test Page</h1>
  
  <p class="mb-4">Click the button below to test the access denied page:</p>
  
  <Button on:click={testAccessDenied}>
    Test Access Denied Page
  </Button>
</div> 