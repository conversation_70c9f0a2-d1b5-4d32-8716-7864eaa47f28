<script lang="ts">
  import { page } from '$app/stores';
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { Button } from '$lib/components/ui/button';
  import { Alert, AlertDescription, AlertTitle } from '$lib/components/ui/alert';
  import { <PERSON>Alert, ArrowLeft, Clock } from 'lucide-svelte';

  // Get the redirect URL and reason from the URL parameters
  const redirectUrl = $page.url.searchParams.get('redirectUrl') || '/';
  const resourceType = $page.url.searchParams.get('resourceType') || 'resource';
  const requiredRole = $page.url.searchParams.get('requiredRole') || 'appropriate permissions';
  const companyId = $page.url.searchParams.get('companyId');
  
  // Check if we're in test mode - don't redirect if accessed directly
  const testMode = $page.url.pathname === '/access-denied' && !$page.url.search;
  
  let secondsLeft = 5;
  let redirectTimer: ReturnType<typeof setInterval>;

  onMount(() => {
    // Only set up the redirect timer if not in test mode
    if (!testMode) {
      // Auto-redirect after 5 seconds
      redirectTimer = setInterval(() => {
        secondsLeft -= 1;
        if (secondsLeft <= 0) {
          if (redirectTimer) clearInterval(redirectTimer);
          goto(redirectUrl);
        }
      }, 1000);
    }

    // Clean up the timer when component is destroyed
    return () => {
      if (redirectTimer) clearInterval(redirectTimer);
    };
  });

  function handleManualRedirect() {
    if (redirectTimer) clearInterval(redirectTimer);
    goto(redirectUrl);
  }
</script>

<div class="container flex flex-col items-center justify-center min-h-[80vh] py-10">
  <div class="w-full max-w-md space-y-6">
    <div class="flex justify-center">
      <ShieldAlert class="h-16 w-16 text-red-500" />
    </div>

    <h1 class="text-2xl font-bold text-center">Access Restricted</h1>
    
    <Alert variant="destructive">
      <AlertTitle>Permission Denied</AlertTitle>
      <AlertDescription>
        You do not have access to the requested {resourceType}. 
        This area requires {requiredRole}.
      </AlertDescription>
    </Alert>

    <p class="text-center text-muted-foreground">
      You will be redirected to the dashboard in <span class="font-bold">{secondsLeft}</span> seconds.
    </p>

    <div class="flex flex-col sm:flex-row gap-3 justify-center mt-6">
      <Button variant="outline" on:click={handleManualRedirect}>
        <ArrowLeft class="mr-2 h-4 w-4" />
        Return Now
      </Button>
      
      <Button variant="default">
        <a href={companyId ? `/company/${companyId}` : '/'} class="w-full h-full flex items-center justify-center">
          Go to Dashboard
        </a>
      </Button>
    </div>

    <div class="mt-8 p-4 border rounded-lg bg-muted/50">
      <h3 class="font-medium mb-2">Need Access?</h3>
      <p class="text-sm text-muted-foreground">
        If you believe you should have access to this {resourceType}, please:
      </p>
      <ul class="text-sm text-muted-foreground list-disc list-inside mt-2 space-y-1">
        <li>Speak to your company administrator for permission updates</li>
        <li>Contact TOTM support if you're unable to resolve this issue</li>
      </ul>
    </div>
  </div>
</div> 