<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="%sveltekit.assets%/favicon.png" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		%sveltekit.head%
	</head>
	<body data-sveltekit-preload-data="hover">
		<div style="display: contents">%sveltekit.body%</div>

		<!-- Facebook SDK -->
		<script>
			window.fbAsyncInit = function() {
				console.log('[FB SDK] Initializing Facebook SDK');
				FB.init({
					appId: '1747964086028356',
					cookie: true,
					xfbml: true,
					version: 'v22.0'
				});
				console.log('[FB SDK] Facebook SDK initialized');
			};

			// Load the JavaScript SDK asynchronously
			(function(d, s, id) {
				console.log('[FB SDK] Loading Facebook SDK script');
				var js, fjs = d.getElementsByTagName(s)[0];
				if (d.getElementById(id)) {
					console.log('[FB SDK] SDK script already loaded');
					return;
				}
				js = d.createElement(s); js.id = id;
				js.src = "https://connect.facebook.net/en_US/sdk.js";
				js.onload = function() {
					console.log('[FB SDK] SDK script loaded successfully');
				};
				js.onerror = function(error) {
					console.error('[FB SDK] Error loading SDK script:', error);
				};
				fjs.parentNode.insertBefore(js, fjs);
				console.log('[FB SDK] SDK script injection initiated');
			}(document, 'script', 'facebook-jssdk'));
		</script>
	</body>
</html>
