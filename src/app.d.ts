/// <reference types="@sveltejs/kit" />

import type { Session, SupabaseClient, User } from '@supabase/supabase-js';
import type { Database } from '../database.types';

declare global {
	namespace App {
		// interface Error {}
		interface Locals {
			supabase: SupabaseClient;
			supabaseAdmin: SupabaseClient;
			safeGetSession: () => Promise<{
				session: Session | null;
				user: User | null;
				userCompanies: Database['public']['Tables']['companies']['Row'][];
			}>;
			session: Session | null;
			user: User | null;
			userCompanies: Database['public']['Tables']['companies']['Row'][];
		}
		interface PageData {
			session: Session | null;
		}
		// interface PageState {}
		// interface Platform {}
	}

	namespace NodeJS {
		interface ProcessEnv {
			PUBLIC_FB_APP_ID: string;
			PRIVATE_FB_APP_SECRET: string;
			PUBLIC_FB_CONFIG_ID: string;
			PRIVATE_FB_SYSTEM_USER_TOKEN: string;
			[key: string]: string | undefined;
		}
	}
}

export {};
