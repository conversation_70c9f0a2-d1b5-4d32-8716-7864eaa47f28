import { vi } from "vitest";

// Mock SvelteKit's environment modules
vi.mock("$env/static/private", () => ({
  PRIVATE_AZURE_CLIENT_SECRET: "mock-secret",
}));

vi.mock("$env/static/public", () => ({
  PUBLIC_AZURE_CLIENT_ID: "mock-client-id",
}));

// Mock Supabase Admin
vi.mock("$lib/server/supabaseAdmin", () => ({
  supabaseAdmin: {
    auth: {
      admin: {
        createUser: vi.fn(),
        generateLink: vi.fn(),
        inviteUserByEmail: vi.fn(),
      },
    },
  },
}));

// Mock any other global dependencies
vi.mock("$lib/server/vault", () => ({
  getSecretById: vi.fn(),
}));

// Set up any global test variables or configurations
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));
