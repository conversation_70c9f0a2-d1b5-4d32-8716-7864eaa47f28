@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
	:root {
		--background: 0 0% 100%;
		--foreground: 222.2 84% 4.9%;
		--text-color: #ffffff;

		--muted: 210 40% 96.1%;
		--muted-foreground: 215.4 16.3% 46.9%;

		--popover: 0 0% 100%;
		--popover-foreground: 222.2 84% 4.9%;

		--card: 0 0% 100%;
		--card-foreground: 222.2 84% 4.9%;

		--border: 214.3 31.8% 91.4%;
		--input: 214.3 31.8% 91.4%;

		--primary: 222.2 47.4% 11.2%;
		--primary-foreground: 210 40% 98%;

		--secondary: 210 40% 96.1%;
		--secondary-foreground: 222.2 47.4% 11.2%;

		--accent: 210 40% 96.1%;
		--accent-foreground: 222.2 47.4% 11.2%;

		--destructive: 0 72.2% 50.6%;
		--destructive-foreground: 210 40% 98%;

		--ring: 222.2 84% 4.9%;

		--radius: 0.5rem;

		--info: 210 50% 50%;
		--info-foreground: 210 40% 98%;

		--success: 142 72% 29%;
		--success-foreground: 144 61% 98%;

		--warning: 38 92% 50%;
		--warning-foreground: 48 96% 98%;
	}

	.theme-totm-light {
		--gradient: linear-gradient(135deg, #F0E6FF 0%, #B7F5FF 100%);
		--background: 267 30% 98%;     /* Light purple-tinted background */
		--foreground: 267 100% 10%;    /* Dark purple text */
		
		--muted: 267 30% 90%;
		--muted-foreground: 267 20% 40%;
		
		--popover: 267 30% 98%;
		--popover-foreground: 267 100% 10%;
		
		--card: 0 0% 100%;        /* Pure white background */
		--card-foreground: 220 13% 10%;  /* Neutral dark gray text */
		--card-hover: 220 13% 98%;   /* Very subtle gray tint on hover */
		--card-border: 220 13% 92%;  /* Neutral gray border */
		
		--border: 267 20% 92%;      /* Softer border */
		--input: 267 20% 92%;
		
		--primary: 187 85% 31%;        /* Keep the teal accent color */
		--primary-foreground: 0 0% 100%;
		
		--secondary: 267 30% 90%;
		--secondary-foreground: 267 50% 15%;
		
		--accent: 271 73% 35%;      /* TOTM purple #5a189a */
		--accent-foreground: 0 0% 100%;
		
		--destructive: 0 84.2% 60.2%;
		--destructive-foreground: 0 0% 98%;
		
		--ring: 267 30% 80%;
		
		--radius: 0.75rem;
	}

	.theme-totm-dark {
		--gradient: linear-gradient(135deg, #360033 0%, #0b8793 100%);
		--background: 267 25% 12%;    /* Swapped with card color - darker purple */
		--foreground: 0 0% 100%;      /* White text */
		
		--muted: 267 30% 20%;
		--muted-foreground: 267 10% 80%;
		
		--popover: 267 50% 15%;
		--popover-foreground: 0 0% 100%;
		
		--card: 220 13% 15%;        /* Dark neutral gray background */
		--card-foreground: 0 0% 100%;
		--card-hover: 220 13% 17%;    /* Slightly lighter on hover */
		--card-border: 220 13% 20%;   /* Subtle neutral border */
		
		--border: 267 20% 18%;      /* Softer border */
		--input: 267 20% 18%;
		
		--primary: 187 85% 31%;        /* Teal accent color */
		--primary-foreground: 0 0% 100%;
		
		--secondary: 267 50% 20%;
		--secondary-foreground: 0 0% 100%;
		
		--accent: 271 73% 35%;      /* TOTM purple #5a189a */
		--accent-foreground: 0 0% 100%;
		
		--destructive: 0 84.2% 60.2%;
		--destructive-foreground: 0 0% 98%;
		
		--ring: 267 30% 20%;
		
		--radius: 0.75rem;

		/* Custom colors for dark mode */
		--teal-glow: rgba(11, 135, 147, 0.15);  /* Teal color for glows */
		--teal-accent: #0b8793;                 /* Teal accent color */

		--info: 210 50% 50%;
		--info-foreground: 210 40% 98%;

		--success: 142 72% 29%;
		--success-foreground: 144 61% 98%;

		--warning: 38 92% 50%;
		--warning-foreground: 48 96% 98%;
	}

	/* Add gradient background utility */
	.bg-totm-gradient {
		background: var(--gradient);
	}

	/* Add these utility classes to your existing @layer base section */
	.auth-divider {
		@apply relative my-4;
	}

	.auth-divider-line {
		@apply absolute inset-0 flex items-center;
	}

	.auth-divider-text {
		@apply relative flex justify-center text-xs uppercase;
	}

	.auth-divider-text span {
		@apply bg-background px-2 text-muted-foreground;
	}

	.auth-card {
		@apply max-w-md w-full mx-auto rounded-lg border bg-card text-card-foreground shadow-sm;
	}

	.auth-button {
		@apply transition-colors hover:bg-primary/90;
	}

	.auth-social-button {
		@apply hover:bg-muted/50 transition-colors;
	}

	/* Card dark mode styles */
	.theme-totm-dark .company-card {
		background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--card-hover)) 100%);
		border-color: hsl(var(--card-border));
		transition: all 0.3s ease;
	}

	.theme-totm-dark .company-card:hover {
		border-color: var(--teal-accent);
		box-shadow: 0 0 20px var(--teal-glow);
		background: linear-gradient(135deg, hsl(var(--card-hover)) 0%, hsl(var(--card)) 100%);
	}

	/* Logo visibility rules */
	.theme-totm-dark .navbar-logo {
		filter: brightness(0) invert(1);
	}

	.theme-totm-light .navbar-logo {
		filter: brightness(0) saturate(100%);  /* Makes the logo black in light mode */
	}
}

/* Add navbar logo styling for dark mode */
.theme-totm-dark .navbar-logo {
	filter: brightness(0) invert(1);
}

@layer base {
	* {
		@apply border-border;
	}
	body {
		@apply bg-background text-foreground;
		background-color: hsl(var(--background));
		color: hsl(var(--foreground));
	}
}
