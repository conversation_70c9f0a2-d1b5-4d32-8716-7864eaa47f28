# TOTM Search

A SvelteKit + TypeScript application for complex document search with database relationships and authentication.

## 🚀 Features

- Advanced document search with vector embeddings
- Multi-platform data source integration (SharePoint, WhatsA<PERSON>, <PERSON>lack, Google)
- Role-based access control with Supabase RLS
- Company-based data isolation
- Real-time chat functionality
- Document processing and transcription
- OAuth2 integration for various platforms

## 📋 Prerequisites

- Node.js (v18+)
- pnpm
- Supabase CLI
- Docker (for local development)

## 🛠️ Setup

1. Clone the repository
2. Install dependencies:
   ```bash
   pnpm install
   ```
3. Set up environment variables:
   ```bash
   cp .env.example .env
   ```
4. Start Supabase locally:
   ```bash
   supabase start
   ```
5. Run the development server:
   ```bash
   pnpm dev
   ```

## 🏗️ Architecture

### Authentication Flow

1. Magic link confirmation uses server-side auth flow
2. Invitation confirmation uses server-side auth flow
3. Both flows redirect through `/auth/confirm` endpoint

### User Roles

- **Admin**: Full access to company resources and user management
- **Expert**: Can manage documents and perform advanced searches
- **Member**: Basic access to company resources

### Database Schema

#### Key Tables and Relationships

1. **base_users**

   - References: `auth.users(id)`
   - ON DELETE: CASCADE
   - ON UPDATE: CASCADE

2. **companies**

   - No direct foreign keys
   - Managed through company_members

3. **company_members**

   - References:
     - `companies(id)` (ON DELETE: CASCADE)
     - `users(id)` (ON DELETE: CASCADE)
   - ON UPDATE: CASCADE

4. **documents**

   - References: `files(id)` (ON DELETE: CASCADE)
   - ON UPDATE: CASCADE

5. **files**

   - References: `companies(id)` (ON DELETE: CASCADE)
   - ON UPDATE: CASCADE

6. **data_sources**
   - References:
     - `companies(id)` (ON DELETE: CASCADE)
     - `vault.secrets(id)` (ON DELETE: CASCADE)
   - ON UPDATE: CASCADE

For a complete list of database relationships, see [Database Schema](./docs/database-schema.md).

## 🔒 Security

### Row Level Security (RLS) Policies

#### Companies Table

- Members can view their companies
- Authenticated users can create companies
- Company owners can update/delete their companies

#### Documents Table

- Company members can read documents
- Admins/Experts can create/update/delete documents
- Access controlled through file permissions

#### Files Table

- Company members can read files
- Admins/Experts can create/update/delete files
- Company-based isolation enforced

For a complete list of RLS policies, see [Security Policies](./docs/security-policies.md).

## 🔄 Data Integration

### Supported Platforms

- SharePoint
- WhatsApp
- Slack
- Google Drive
- Email

### OAuth2 Flow

1. Platform authorization
2. Token storage in vault
3. Automatic refresh handling
4. Platform-specific metadata sync

## 🧪 Testing

```bash
# Run unit tests
pnpm test

# Run integration tests
pnpm test:integration

# Run e2e tests
pnpm test:e2e
```

## 📦 Deployment

1. Update database types:
   ```bash
   supabase gen types typescript --local > src/lib/types/database.types.ts
   ```
2. Run migrations:
   ```bash
   supabase db reset
   ```
3. Deploy to production:
   ```bash
   pnpm build
   ```

## 📝 Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📄 License

This project is proprietary and confidential.

---

## Appendix

### Changelog

See [CHANGELOG.md](./CHANGELOG.md) for a detailed list of changes.

### API Documentation

See [API.md](./docs/API.md) for detailed API documentation.

### Environment Variables

See [.env.example](./.env.example) for required environment variables.

# Supabase RLS Testing

This repository contains tests for Row-Level Security (RLS) policies in our Supabase database.

## Test Structure

The tests are organized in the following structure:

1. `supabase/tests/database/00-test-helpers-setup.sql` - Setup for Basejump test helpers
2. `supabase/tests/database/01-rls-enabled-check.sql` - Verify RLS is enabled on all tables
3. `supabase/tests/database/02-companies-rls-tests.sql` - Test RLS policies for companies table
4. `supabase/tests/database/03-company-members-rls-tests.sql` - Test RLS policies for company_members table

## Business Rules Tested

### Company Policies

1. Users should only see companies they are members of
2. Users should only be able to update companies where they are admins
3. Only authenticated users should be able to access any company data
4. Company creation should be allowed for authenticated users
5. Company deletion should only be allowed for company admins

### Company Members Policies

1. Company admins should be able to add members to their companies
2. Company admins should be able to update members of their companies (except themselves)
3. Company admins should not be able to update members of other companies
4. Company admins should not be able to update inactive members
5. Regular members should not be able to update any member records
6. Users should only see members from companies they belong to
7. Only admins should be able to delete members from their companies

## Running the Tests

You can run the tests using the following command:

```bash
./run_tests.sh
```

Or directly with the Supabase CLI:

```bash
supabase test db
```

## Testing Methodology

We use the [Basejump test helpers](https://github.com/usebasejump/supabase-test-helpers) to simplify testing RLS policies. This allows us to:

1. Create test users programmatically
2. Authenticate as different users to test permissions
3. Clear authentication to test anonymous access
4. Verify RLS is enabled across all tables

Each test creates its own data within a transaction and rolls back at the end, ensuring test isolation and clean state between test runs.
