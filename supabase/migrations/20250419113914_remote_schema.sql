

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE EXTENSION IF NOT EXISTS "pg_cron" WITH SCHEMA "pg_catalog";






CREATE EXTENSION IF NOT EXISTS "pg_net" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgsodium";






CREATE SCHEMA IF NOT EXISTS "private";


ALTER SCHEMA "private" OWNER TO "postgres";


COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "vector" WITH SCHEMA "public";






CREATE EXTENSION IF NOT EXISTS "wrappers" WITH SCHEMA "extensions";






CREATE TYPE "public"."company_status" AS ENUM (
    'trial',
    'paid',
    'suspended'
);


ALTER TYPE "public"."company_status" OWNER TO "postgres";


CREATE TYPE "public"."invitation_type" AS ENUM (
    'one_time',
    '24_hour'
);


ALTER TYPE "public"."invitation_type" OWNER TO "postgres";


CREATE TYPE "public"."invite_status" AS ENUM (
    'pending',
    'accepted',
    'expired'
);


ALTER TYPE "public"."invite_status" OWNER TO "postgres";


CREATE TYPE "public"."permission_level" AS ENUM (
    'read',
    'write',
    'admin'
);


ALTER TYPE "public"."permission_level" OWNER TO "postgres";


CREATE TYPE "public"."platform_type" AS ENUM (
    'sharepoint',
    'whatsapp',
    'slack',
    'email',
    'google'
);


ALTER TYPE "public"."platform_type" OWNER TO "postgres";


CREATE TYPE "public"."user_role" AS ENUM (
    'admin',
    'expert',
    'member'
);


ALTER TYPE "public"."user_role" OWNER TO "postgres";


CREATE TYPE "public"."vault_secret" AS (
	"id" "uuid",
	"secret" "text"
);


ALTER TYPE "public"."vault_secret" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."accept_company_invitation"("token" "text") RETURNS "jsonb"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  invitation_record public.company_invitations;
  result JSONB;
BEGIN
  -- Find the invitation
  SELECT * INTO invitation_record
  FROM public.company_invitations
  WHERE 
    company_invitations.token = accept_company_invitation.token
    AND expires_at > now();
    
  IF invitation_record.id IS NULL THEN
    RAISE EXCEPTION 'Invitation not found or expired';
  END IF;
  
  -- Check if user is already a member
  IF EXISTS (
    SELECT 1 
    FROM public.company_members 
    WHERE 
      company_id = invitation_record.company_id 
      AND user_id = auth.uid()
      AND is_active = true
  ) THEN
    RAISE EXCEPTION 'You are already a member of this company';
  END IF;
  
  -- Add user to company
  INSERT INTO public.company_members (company_id, user_id, role, is_active)
  VALUES (invitation_record.company_id, auth.uid(), invitation_record.role, true);
  
  -- Delete one-time invitations
  IF invitation_record.invitation_type = 'one_time' THEN
    DELETE FROM public.company_invitations WHERE id = invitation_record.id;
  END IF;
  
  -- Return company info
  SELECT jsonb_build_object(
    'company_id', c.id,
    'company_name', c.name,
    'role', invitation_record.role
  ) INTO result
  FROM public.companies c
  WHERE c.id = invitation_record.company_id;
  
  RETURN result;
END;
$$;


ALTER FUNCTION "public"."accept_company_invitation"("token" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."accept_company_invite"("invite_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
declare
    v_user_id uuid;
    v_company_id uuid;
    v_role public.user_role;
begin
    -- Get current user id
    v_user_id := auth.uid();

    -- Get and validate invite
    select company_id, role into v_company_id, v_role
    from public.company_invites
    where id = invite_id
    and status = 'pending'
    and expires_at > now();

    if not found then
        return false;
    end if;

    -- Create company member
    insert into public.company_members (company_id, user_id, role, is_active)
    values (v_company_id, v_user_id, v_role, true)
    on conflict (company_id, user_id) 
    do update set is_active = true, role = excluded.role;

    -- Update invite status
    update public.company_invites
    set status = 'accepted',
        updated_at = now()
    where id = invite_id;

    return true;
end;
$$;


ALTER FUNCTION "public"."accept_company_invite"("invite_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."add_creator_as_admin"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    INSERT INTO public.company_members (user_id, company_id, role, created_at, updated_at)
    VALUES (NEW.created_by, NEW.id, 'admin', now(), now());
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."add_creator_as_admin"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."add_question"("question_text" "text", "assigned_to" "uuid") RETURNS "uuid"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    question_id uuid;
BEGIN
    -- Insert the question using the user_id from the JWT
    INSERT INTO public.totm_inbox_questions (question_text, user_id, assigned_to, created_at)
    VALUES (question_text, auth.uid(), assigned_to, now())  -- Use auth.uid() to get the user_id
    RETURNING id INTO question_id;

    RETURN question_id;  -- Return the ID of the newly created question
END;
$$;


ALTER FUNCTION "public"."add_question"("question_text" "text", "assigned_to" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."add_question"("question_text" "text", "user_id" "uuid", "assigned_to" "uuid") RETURNS "uuid"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    question_id uuid;
BEGIN
    -- Insert the question
    INSERT INTO public.totm_inbox_questions (question_text, user_id, assigned_to, created_at)
    VALUES (question_text, user_id, assigned_to, now())
    RETURNING id INTO question_id;

    RETURN question_id;  -- Return the ID of the newly created question
END;
$$;


ALTER FUNCTION "public"."add_question"("question_text" "text", "user_id" "uuid", "assigned_to" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."add_question_providing_user_id"("question_text" "text", "user_id" "uuid", "assigned_to" "uuid") RETURNS "uuid"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    question_id uuid;
BEGIN
    -- Insert the question
    INSERT INTO public.totm_inbox_questions (question_text, user_id, assigned_to, created_at)
    VALUES (question_text, user_id, assigned_to, now())
    RETURNING id INTO question_id;

    RETURN question_id;  -- Return the ID of the newly created question
END;
$$;


ALTER FUNCTION "public"."add_question_providing_user_id"("question_text" "text", "user_id" "uuid", "assigned_to" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."add_response"("question_id" "uuid", "response_text" "text") RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    response_id uuid;
BEGIN
    -- Insert the response using the expert_id from the JWT
    INSERT INTO public.totm_inbox_responses (question_id, response_text, expert_id, created_at)
    VALUES (question_id, response_text, auth.uid(), now())  -- Use auth.uid() to get the expert_id
    RETURNING id INTO response_id;

    -- Create the mapping
    INSERT INTO public.map_question_to_responses (question_id, response_id)
    VALUES (question_id, response_id);
END;
$$;


ALTER FUNCTION "public"."add_response"("question_id" "uuid", "response_text" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."add_user_to_team"("team_uuid" "uuid", "user_uuid" "uuid") RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    INSERT INTO team_members (team_id, user_id)
    VALUES (team_uuid, user_uuid)
    ON CONFLICT (team_id, user_id) DO NOTHING;
EXCEPTION WHEN OTHERS THEN
    RAISE EXCEPTION 'Error adding user to team: %', SQLERRM;
END;
$$;


ALTER FUNCTION "public"."add_user_to_team"("team_uuid" "uuid", "user_uuid" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."check_and_fix_user_profile"() RETURNS "jsonb"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_user_id UUID;
    v_auth_email TEXT;
    v_profile_exists BOOLEAN;
    v_was_created BOOLEAN := FALSE;
    v_auth_data JSONB;
BEGIN
    -- Get current user ID
    v_user_id := auth.uid();
    
    IF v_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'message', 'Authentication required',
            'code', 'auth_required'
        );
    END IF;
    
    -- Check if user has a profile in base_users
    SELECT EXISTS(
        SELECT 1 FROM public.base_users 
        WHERE id = v_user_id
    ) INTO v_profile_exists;
    
    -- If profile already exists, just return the success message
    IF v_profile_exists THEN
        RETURN jsonb_build_object(
            'success', true,
            'message', 'User profile already exists',
            'was_created', false,
            'user_id', v_user_id
        );
    END IF;
    
    -- Profile doesn't exist, let's create it
    -- First, get the auth user's data
    SELECT 
        email,
        jsonb_build_object(
            'email', email,
            'raw_user_meta_data', raw_user_meta_data,
            'created_at', created_at
        ) INTO v_auth_email, v_auth_data
    FROM auth.users
    WHERE id = v_user_id;
    
    -- Create the base_users entry
    INSERT INTO public.base_users (
        id,
        display_name,
        email,
        avatar_url,
        created_at,
        updated_at
    )
    VALUES (
        v_user_id,
        COALESCE(
            v_auth_data->'raw_user_meta_data'->>'full_name',
            v_auth_data->'raw_user_meta_data'->>'name',
            v_auth_email,
            'User'
        ),
        v_auth_email,
        COALESCE(
            v_auth_data->'raw_user_meta_data'->>'avatar_url',
            v_auth_data->'raw_user_meta_data'->>'picture',
            NULL
        ),
        NOW(),
        NOW()
    )
    ON CONFLICT (id) DO NOTHING
    RETURNING TRUE INTO v_was_created;
    
    -- Check for existing company memberships that might be tied to this email
    UPDATE public.company_members cm
    SET 
        user_id = v_user_id,
        is_active = true
    FROM public.user_contact_info uci
    WHERE uci.company_member_id = cm.id
        AND uci.platform_type = 'email'
        AND uci.value = v_auth_email
        AND cm.user_id IS NULL;
    
    -- Sync SharePoint profiles
    PERFORM pg_sleep(0.5);
    PERFORM public.sync_user_sharepoint_profiles(v_user_id);
    
    -- Return the result
    RETURN jsonb_build_object(
        'success', true,
        'message', 'User profile created successfully',
        'was_created', COALESCE(v_was_created, true),
        'user_id', v_user_id,
        'email', v_auth_email
    );
EXCEPTION WHEN OTHERS THEN
    RETURN jsonb_build_object(
        'success', false,
        'message', 'Error: ' || SQLERRM,
        'error_code', SQLSTATE
    );
END;
$$;


ALTER FUNCTION "public"."check_and_fix_user_profile"() OWNER TO "postgres";


COMMENT ON FUNCTION "public"."check_and_fix_user_profile"() IS 'API endpoint to check for and fix missing base_users profiles for the current user';



CREATE OR REPLACE FUNCTION "public"."create_data_source"("company_uuid" "uuid", "platform_type" "public"."platform_type", "display_name" "text") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    new_data_source_id uuid;
BEGIN
    INSERT INTO data_sources (company_id, platform_type, display_name)
    VALUES (company_uuid, platform_type, display_name)
    RETURNING id INTO new_data_source_id;
    
    RETURN new_data_source_id;
END;
$$;


ALTER FUNCTION "public"."create_data_source"("company_uuid" "uuid", "platform_type" "public"."platform_type", "display_name" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_data_source"("company_uuid" "uuid", "platform_type" "public"."platform_type", "display_name" "text", "scope" "text" DEFAULT NULL::"text") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    new_data_source_id uuid;
BEGIN
    INSERT INTO data_sources (company_id, platform_type, display_name, scope)
    VALUES (company_uuid, platform_type, display_name, scope)
    RETURNING id INTO new_data_source_id;
    
    RETURN new_data_source_id;
END;
$$;


ALTER FUNCTION "public"."create_data_source"("company_uuid" "uuid", "platform_type" "public"."platform_type", "display_name" "text", "scope" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_secret"("secret" "text") RETURNS "text"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
declare
  secret_id text;
begin
  -- Generate a random UUID for the secret
  secret_id := gen_random_uuid()::text;
  
  -- Insert the secret into the vault.secrets table
  insert into vault.secrets (id, secret)
  values (secret_id, secret);
  
  -- Return the secret ID
  return secret_id;
end;
$$;


ALTER FUNCTION "public"."create_secret"("secret" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_team"("company_uuid" "uuid", "team_name" "text", "team_description" "text") RETURNS "uuid"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    new_team_uuid UUID;
BEGIN
    INSERT INTO teams (company_id, name, description)
    VALUES (company_uuid, team_name, team_description)
    RETURNING id INTO new_team_uuid;
    RETURN new_team_uuid;
EXCEPTION WHEN OTHERS THEN
    RAISE EXCEPTION 'Error creating team: %', SQLERRM;
END;
$$;


ALTER FUNCTION "public"."create_team"("company_uuid" "uuid", "team_name" "text", "team_description" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."current_user_email"() RETURNS "text"
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
  select email from auth.users where id = auth.uid();
$$;


ALTER FUNCTION "public"."current_user_email"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."gen_test_uuid"("seed" "text") RETURNS "uuid"
    LANGUAGE "sql" IMMUTABLE
    AS $$
    -- Create a deterministic UUID based on the seed string
    -- This uses MD5 to get a consistent hash from the seed
    SELECT uuid_generate_v5(
        '00000000-0000-0000-0000-000000000000'::uuid,
        seed
    );
$$;


ALTER FUNCTION "public"."gen_test_uuid"("seed" "text") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."gen_test_uuid"("seed" "text") IS 'Generates a deterministic UUID from a seed string for testing purposes';



CREATE OR REPLACE FUNCTION "public"."generate_file_metadata_text"("file_name" "text", "mime_type" "text", "summary" "text") RETURNS "text"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  RETURN COALESCE(file_name, '') || ' ' || 
         COALESCE(mime_type, '') || ' ' || 
         COALESCE(summary, '');
END;
$$;


ALTER FUNCTION "public"."generate_file_metadata_text"("file_name" "text", "mime_type" "text", "summary" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_invitation_details"("token" "text") RETURNS "jsonb"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  result JSONB;
BEGIN
  SELECT jsonb_build_object(
    'valid', TRUE,
    'company_name', c.name,
    'invited_by_email', u.email,
    'role', i.role,
    'expires_at', i.expires_at
  ) INTO result
  FROM public.company_invitations i
  JOIN public.companies c ON c.id = i.company_id
  JOIN auth.users u ON u.id = i.invited_by
  WHERE i.token = get_invitation_details.token
    AND i.expires_at > now();
    
  IF result IS NULL THEN
    RETURN jsonb_build_object('valid', FALSE);
  END IF;
  
  RETURN result;
END;
$$;


ALTER FUNCTION "public"."get_invitation_details"("token" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_vault_secret"("secret_id" "uuid") RETURNS "jsonb"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    result jsonb;
BEGIN
    -- Log the secret ID being requested
    RAISE NOTICE 'Secret requested with ID: %', secret_id;

    SELECT jsonb_build_object(
        'id', id,
        'decrypted_secret', decrypted_secret
    )
    INTO result
    FROM vault.decrypted_secrets
    WHERE id = secret_id;

    RETURN result;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'An error occurred while retrieving the secret: %', SQLERRM;
END;
$$;


ALTER FUNCTION "public"."get_vault_secret"("secret_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_vault_secrets"("secret_ids" "uuid"[]) RETURNS SETOF "public"."vault_secret"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
BEGIN
    -- Check if the calling user has access to these secrets
    -- You might want to add additional access control logic here
    
    RETURN QUERY
    SELECT 
        secrets.id,
        secrets.secret::text
    FROM vault.secrets
    WHERE secrets.id = ANY(secret_ids);
END;
$$;


ALTER FUNCTION "public"."get_vault_secrets"("secret_ids" "uuid"[]) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_whatsapp_data"("from_number" "text", "to_number" "text") RETURNS TABLE("company_data" "jsonb", "user_data" "jsonb")
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
BEGIN
    RETURN QUERY
    WITH company_match AS (
        SELECT 
            c.id,
            c.name,
            c.status,
            c.created_at,
            cp.branding_logo,
            cp.small_logo,
            cp.slug
        FROM companies c
        INNER JOIN communication_channels cc ON cc.company_id = c.id
        LEFT JOIN company_personalization cp ON cp.company_id = c.id
        WHERE cc.channel_id = to_number 
        AND cc.platform_type = 'whatsapp'
    ),
    user_match AS (
        SELECT 
            cm.user_id,
            cm.display_name,
            cm.avatar_url,
            cm.role,
            cm.is_active,
            bu.email AS base_email,
            -- Get SharePoint email from platform_user_id
            (
                SELECT upp_sp.platform_user_id
                FROM user_platform_profiles upp_sp
                WHERE upp_sp.company_member_id = cm.id
                AND upp_sp.platform_type = 'sharepoint'
                LIMIT 1
            ) AS sharepoint_email,
            -- Get all platform profiles
            (
                SELECT jsonb_object_agg(upp2.platform_type, upp2.metadata)
                FROM user_platform_profiles upp2
                WHERE upp2.company_member_id = cm.id
            ) AS platform_profiles
        FROM company_members cm
        INNER JOIN user_platform_profiles upp ON upp.company_member_id = cm.id
        LEFT JOIN base_users bu ON bu.id = cm.user_id -- Join with base_users to get email
        WHERE upp.platform_user_id = from_number
        AND upp.platform_type = 'whatsapp'
    )
    SELECT 
        -- Company data as JSON object
        CASE 
            WHEN cm.id IS NOT NULL THEN 
                jsonb_build_object(
                    'id', cm.id,
                    'name', cm.name,
                    'status', cm.status,
                    'createdAt', cm.created_at,
                    'branding', jsonb_build_object(
                        'logo', cm.branding_logo,
                        'smallLogo', cm.small_logo,
                        'slug', cm.slug
                    ),
                    'found', true
                )
            ELSE 
                jsonb_build_object('found', false)
        END as company_data,
        -- User data as JSON object with enhanced information
        CASE 
            WHEN um.user_id IS NOT NULL THEN 
                jsonb_build_object(
                    'id', um.user_id,
                    'displayName', um.display_name,
                    'avatarUrl', um.avatar_url,
                    'role', um.role,
                    'isActive', um.is_active,
                    'email', um.base_email,                 -- Add base email
                    'sharepointEmail', um.sharepoint_email, -- Add SharePoint email
                    'platformProfiles', um.platform_profiles, -- Add all platform profiles
                    'found', true
                )
            ELSE 
                jsonb_build_object('found', false)
        END as user_data
    FROM company_match cm
    FULL OUTER JOIN user_match um ON true;  -- Always join but keep all records from both sides
END;
$$;


ALTER FUNCTION "public"."get_whatsapp_data"("from_number" "text", "to_number" "text") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."get_whatsapp_data"("from_number" "text", "to_number" "text") IS 'Gets company and user data based on WhatsApp numbers. Returns data as two JSON objects (company_data and user_data). The user_data includes complete user information including base email, SharePoint email and other platform profile data.';



CREATE OR REPLACE FUNCTION "public"."handle_auth_user_signin"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    -- Sync SharePoint profiles on sign in
    PERFORM public.sync_user_sharepoint_profiles(auth.uid());
    
    RETURN NEW;
EXCEPTION WHEN OTHERS THEN
    RAISE LOG 'Error in handle_auth_user_signin: %, %', SQLERRM, SQLSTATE;
    RETURN NEW; -- Don't block sign in if there's an error
END;
$$;


ALTER FUNCTION "public"."handle_auth_user_signin"() OWNER TO "postgres";


COMMENT ON FUNCTION "public"."handle_auth_user_signin"() IS 'Syncs user SharePoint profiles when a user signs in';



CREATE OR REPLACE FUNCTION "public"."handle_company_personalization_upsert"("p_company_id" "uuid", "p_branding_logo" "text" DEFAULT NULL::"text", "p_small_logo" "text" DEFAULT NULL::"text") RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
begin
  insert into public.company_personalization (company_id, branding_logo, small_logo)
  values (p_company_id, p_branding_logo, p_small_logo)
  on conflict (company_id) do update
  set
    -- If the parameter is null, clear the field. If undefined (not provided), keep existing value
    branding_logo = case 
      when p_branding_logo is null then null 
      else coalesce(p_branding_logo, company_personalization.branding_logo)
    end,
    small_logo = case 
      when p_small_logo is null then null 
      else coalesce(p_small_logo, company_personalization.small_logo)
    end,
    updated_at = now();
end;
$$;


ALTER FUNCTION "public"."handle_company_personalization_upsert"("p_company_id" "uuid", "p_branding_logo" "text", "p_small_logo" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_data_source_user_profile"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_company_member_id uuid;
    v_platform_user_id text;
    v_display_name text;
BEGIN
    -- Log for debugging
    RAISE LOG 'Data source created: id=%, platform=%, company=%, user=%', 
               NEW.id, NEW.platform_type, NEW.company_id, auth.uid();
    
    -- Get the company member ID for the current user in this company
    SELECT id INTO v_company_member_id
    FROM public.company_members
    WHERE company_id = NEW.company_id
    AND user_id = auth.uid()
    LIMIT 1;
    
    -- Log company member ID
    RAISE LOG 'Found company member ID: %', v_company_member_id;

    -- For SharePoint, we need to handle platform_user_id differently
    IF NEW.platform_type = 'sharepoint'::public.platform_type THEN
        -- For SharePoint, we can construct a user ID from available information
        -- This could be the user's email or a combination of tenant and user info
        SELECT email INTO v_platform_user_id
        FROM public.base_users
        WHERE id = auth.uid();
        
        -- Set a meaningful display name for SharePoint
        v_display_name := COALESCE(NEW.display_name, 'SharePoint Integration');
    ELSE
        -- For other platforms, use id_token as before
        v_platform_user_id := COALESCE(NEW.id_token::text, 'unknown_user');
        v_display_name := NEW.display_name;
    END IF;
    
    -- Log platform user ID
    RAISE LOG 'Using platform user ID: %', v_platform_user_id;

    -- Create user platform profile if we have the required data
    IF v_company_member_id IS NOT NULL AND v_platform_user_id IS NOT NULL THEN
        -- First check if there's already a profile for this combination
        DECLARE
            existing_id uuid;
        BEGIN
            SELECT id INTO existing_id
            FROM public.user_platform_profiles
            WHERE company_member_id = v_company_member_id
            AND platform_type = NEW.platform_type;
            
            IF existing_id IS NOT NULL THEN
                -- Update existing record
                UPDATE public.user_platform_profiles
                SET platform_user_id = v_platform_user_id,
                    metadata = jsonb_build_object(
                        'data_source_id', NEW.id,
                        'platform_type', NEW.platform_type,
                        'display_name', v_display_name,
                        'scope', NEW.scope,
                        'site_id', NEW.site_id,
                        'tenant_id', NEW.tenant_id,
                        'drive_id', NEW.drive_id
                    ),
                    updated_at = now()
                WHERE id = existing_id;
                
                RAISE LOG 'Updated existing platform profile with ID: %', existing_id;
            ELSE
                -- Insert new record
                INSERT INTO public.user_platform_profiles (
                    company_member_id,
                    platform_type,
                    platform_user_id,
                    is_primary,
                    metadata
                ) VALUES (
                    v_company_member_id,
                    NEW.platform_type,
                    v_platform_user_id,
                    true,
                    jsonb_build_object(
                        'data_source_id', NEW.id,
                        'platform_type', NEW.platform_type,
                        'display_name', v_display_name,
                        'scope', NEW.scope,
                        'site_id', NEW.site_id,
                        'tenant_id', NEW.tenant_id,
                        'drive_id', NEW.drive_id
                    )
                );
                
                RAISE LOG 'Created new platform profile for data source %', NEW.id;
            END IF;
        END;
    ELSE
        RAISE LOG 'Missing required data. company_member_id: %, platform_user_id: %', 
                  v_company_member_id, v_platform_user_id;
    END IF;

    RETURN NEW;
EXCEPTION WHEN OTHERS THEN
    RAISE LOG 'Error in handle_data_source_user_profile: %, %', SQLERRM, SQLSTATE;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."handle_data_source_user_profile"() OWNER TO "postgres";


COMMENT ON FUNCTION "public"."handle_data_source_user_profile"() IS 'Creates or updates a user_platform_profile entry when a data source is added, with special handling for SharePoint';



CREATE OR REPLACE FUNCTION "public"."handle_new_user"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RAISE LOG 'Handling new user: %', new.email;

  -- Check if the user already exists in base_users
  IF EXISTS (SELECT 1 FROM public.base_users WHERE id = new.id) THEN
    RAISE LOG 'User already exists with ID: %', new.id;
    RETURN new;
  END IF;

  -- Create a new base_user record
  INSERT INTO public.base_users (
    id,
    display_name,
    created_at,
    updated_at
  ) VALUES (
    new.id,
    COALESCE(new.raw_user_meta_data->>'name', new.email),
    NOW(),
    NOW()
  );

  -- If there are any pre-created company memberships (e.g., from Microsoft invites),
  -- update them with the new user_id
  UPDATE public.company_members cm
  SET 
    user_id = new.id,
    is_active = true
  FROM public.user_contact_info uci
  WHERE uci.company_member_id = cm.id
    AND uci.type = 'email'
    AND uci.value = new.email
    AND cm.user_id IS NULL;

  RAISE LOG 'User created with ID: % and email: %', new.id, new.email;
  
  RETURN new;
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Error handling new user: %, SQLSTATE: %', SQLERRM, SQLSTATE;
END;
$$;


ALTER FUNCTION "public"."handle_new_user"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_company_member"("company_id" "uuid", "role_name" "text" DEFAULT NULL::"text") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM public.company_members
    WHERE 
      company_members.company_id = is_company_member.company_id 
      AND user_id = auth.uid()
      AND is_active = true
  );
END;$$;


ALTER FUNCTION "public"."is_company_member"("company_id" "uuid", "role_name" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_current_user"("user_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RETURN auth.uid() = user_id;
END;
$$;


ALTER FUNCTION "public"."is_current_user"("user_id" "uuid") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."is_current_user"("user_id" "uuid") IS 'Checks if the specified user ID matches the current user';



CREATE OR REPLACE FUNCTION "public"."match_documents"("user_id" "uuid", "query_embedding" "public"."vector", "match_count" integer DEFAULT 8, "filter" "jsonb" DEFAULT '{}'::"jsonb", "similarity_threshold" double precision DEFAULT 0.75) RETURNS TABLE("id" "text", "content" "text", "metadata" "jsonb", "similarity" double precision, "updated_at" timestamp with time zone)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN QUERY
    SELECT
        d.id,
        d.content,
        d.metadata,
        1 - (d.embedding <=> query_embedding::vector) AS similarity,
        d.updated_at
    FROM documents d
    WHERE
        d.user_id = match_documents.user_id
        AND (filter = '{}'::jsonb OR d.metadata @> filter)
        AND 1 - (d.embedding <=> query_embedding::vector) >= similarity_threshold
    ORDER BY d.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;


ALTER FUNCTION "public"."match_documents"("user_id" "uuid", "query_embedding" "public"."vector", "match_count" integer, "filter" "jsonb", "similarity_threshold" double precision) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."match_documents_public"("query_embedding" "public"."vector", "match_count" integer DEFAULT 10, "similarity_threshold" double precision DEFAULT 0.1, "filter" "jsonb" DEFAULT '{}'::"jsonb") RETURNS TABLE("id" "text", "content" "text", "metadata" "jsonb", "similarity" double precision, "updated_at" timestamp with time zone)
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
DECLARE
    company_id text;
BEGIN
    -- Extract company_id from filter
    company_id := filter->>'company_id';
    
    -- Validate company_id is provided
    IF company_id IS NULL THEN
        RAISE EXCEPTION 'company_id is required in filter parameter'
            USING HINT = 'Add {"company_id": "your-company-id"} to the filter parameter';
    END IF;

    -- Perform the document matching query
    RETURN QUERY 
    SELECT
        d.id::text,
        d.content,
        d.metadata,
        1 - (d.embedding::vector(1024) <=> query_embedding) AS similarity,
        d.updated_at
    FROM 
        public.documents d
        JOIN public.files f ON d.file_id = f.id
    WHERE 
        -- Ensure company isolation using explicit table reference
        f.company_id = company_id::text
        -- Only include documents with embeddings
        AND d.embedding IS NOT NULL
        -- Apply similarity threshold
        AND 1 - (d.embedding::vector(1024) <=> query_embedding) >= similarity_threshold
        -- Apply any additional filters from the filter parameter, excluding company_id
        AND (
            filter = '{}'::jsonb 
            OR (d.metadata @> (filter - 'company_id'))
        )
    ORDER BY 
        similarity DESC,
        d.updated_at DESC
    LIMIT match_count;
END;
$$;


ALTER FUNCTION "public"."match_documents_public"("query_embedding" "public"."vector", "match_count" integer, "similarity_threshold" double precision, "filter" "jsonb") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."match_documents_public"("query_embedding" "public"."vector", "match_count" integer, "similarity_threshold" double precision, "filter" "jsonb") IS 'Matches documents based on vector similarity search with strict company isolation.';



CREATE OR REPLACE FUNCTION "public"."match_documents_with_permissions"("user_id" "text", "query_embedding" "public"."vector", "match_count" integer DEFAULT 10, "filter" "jsonb" DEFAULT '{}'::"jsonb", "similarity_threshold" double precision DEFAULT 0.1) RETURNS TABLE("id" "text", "content" "text", "metadata" "jsonb", "similarity" double precision, "updated_at" timestamp with time zone)
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
#variable_conflict use_column
BEGIN
    -- Return matching documents with similarity scores
    RETURN QUERY 
    SELECT
        d.id::text,
        d.content,
        d.metadata,
        1 - (d.embedding::vector(1024) <=> query_embedding) AS similarity,
        d.updated_at
    FROM 
        public.documents d
        JOIN public.files f ON d.file_id = f.id
        JOIN public.company_members cm ON f.company_id = cm.company_id
    WHERE 
        -- Check user permissions via company membership
        cm.user_id = user_id
        -- Apply metadata filter (e.g., for company_id)
        AND (filter = '{}'::jsonb OR d.metadata @> filter)
        -- Only include documents with embeddings
        AND d.embedding IS NOT NULL
        -- Apply similarity threshold
        AND 1 - (d.embedding::vector(1024) <=> query_embedding) >= similarity_threshold
        -- Ensure user is active in the company
        AND cm.is_active = true
    ORDER BY 
        similarity DESC,
        d.updated_at DESC
    LIMIT match_count;
END;
$$;


ALTER FUNCTION "public"."match_documents_with_permissions"("user_id" "text", "query_embedding" "public"."vector", "match_count" integer, "filter" "jsonb", "similarity_threshold" double precision) OWNER TO "postgres";


COMMENT ON FUNCTION "public"."match_documents_with_permissions"("user_id" "text", "query_embedding" "public"."vector", "match_count" integer, "filter" "jsonb", "similarity_threshold" double precision) IS 'Matches documents based on vector similarity search with proper permission checks and company isolation';



CREATE OR REPLACE FUNCTION "public"."match_documents_with_permissions"("query_embedding" "public"."vector", "user_email" "text", "company_id" "uuid" DEFAULT NULL::"uuid", "match_count" integer DEFAULT 10, "similarity_threshold" double precision DEFAULT 0.1, "filter" "jsonb" DEFAULT '{}'::"jsonb") RETURNS TABLE("id" "text", "content" "text", "metadata" "jsonb", "similarity" double precision, "has_access" boolean)
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $_$
#variable_conflict use_column
DECLARE
  base_url text;
  http_response_status int;
  http_response_content text;
  file_items jsonb;
  file_ids text[];
  accessible_file_ids text[];
BEGIN
  -- Determine the base URL for the Edge Function from request headers
  BEGIN
    SELECT COALESCE(
      current_setting('request.headers', true)::json->>'origin',
      'http://localhost:3000'
    ) INTO base_url;
  EXCEPTION WHEN OTHERS THEN
    base_url := 'http://localhost:3000';
  END;
  
  -- First perform the similarity search filtered by company_id if provided
  CREATE TEMP TABLE matched_docs ON COMMIT DROP AS
  SELECT
    d.id::text AS doc_id,
    d.content,
    jsonb_build_object(
      'file_id', f.id::text,
      'file_name', f.name,
      'file_path', f.path,
      'file_type', f.mime_type,
      'company_id', f.company_id::text,
      'data_source_id', f.data_source_id::text,
      'updated_at', f.updated_at
    ) || COALESCE(d.metadata, '{}'::jsonb) AS metadata,
    1 - (d.embedding <=> query_embedding) AS similarity,
    f.id::text AS file_id
  FROM 
    public.documents d
    JOIN public.files f ON d.file_id = f.id
  WHERE 
    -- Filter by company_id if provided
    (company_id IS NULL OR f.company_id = company_id)
    -- Apply additional metadata filter if provided
    AND (filter = '{}'::jsonb OR d.metadata @> filter)
    -- Only include documents with embeddings
    AND d.embedding IS NOT NULL
    -- Apply similarity threshold
    AND 1 - (d.embedding <=> query_embedding) >= similarity_threshold
  ORDER BY 
    similarity DESC,
    d.updated_at DESC
  LIMIT match_count;
  
  -- Get all file IDs from matched documents
  SELECT array_agg(file_id)
  INTO file_ids
  FROM matched_docs;
  
  -- If no documents matched, return empty result
  IF file_ids IS NULL OR array_length(file_ids, 1) = 0 THEN
    DROP TABLE matched_docs;
    RETURN;
  END IF;
  
  -- Prepare JSON payload for Edge Function to check permissions
  file_items := jsonb_build_object(
    'fileItems', 
    (SELECT jsonb_agg(jsonb_build_object('id', file_id)) FROM unnest(file_ids) AS file_id)
  );
  
  -- Call Edge Function to get accessible file IDs
  BEGIN
    SELECT
      status,
      content
    INTO 
      http_response_status,
      http_response_content
    FROM
      http((
        'POST',
        format('%s/functions/v1/check_sharepoint_file_permissions?user=%s', base_url, user_email),
        ARRAY[('Content-Type', 'application/json'), ('Authorization', format('Bearer %s', current_setting('request.jwt.claim.sub', true)))],
        'timeout', 
        10,
        file_items::text
      ));
  
    -- Check if the request was successful
    IF http_response_status <> 200 THEN
      RAISE NOTICE 'Error from Edge Function: status %, body: %', http_response_status, http_response_content;
      -- If permission check fails, assume no access to any document
      accessible_file_ids := '{}';
    ELSE
      -- Extract file IDs from response
      SELECT array_agg(jsonb_array_elements_text(response_json->'fileIds'))
      INTO accessible_file_ids
      FROM jsonb_path_query_first(http_response_content::jsonb, '$') AS response_json;
      
      -- Handle case where no files are accessible
      IF accessible_file_ids IS NULL THEN
        accessible_file_ids := '{}';
      END IF;
    END IF;
  EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Error calling Edge Function: %', SQLERRM;
    accessible_file_ids := '{}';
  END;
  
  -- Return matched documents with redacted content for inaccessible files
  RETURN QUERY 
  SELECT
    doc_id,
    CASE
      WHEN file_id = ANY(accessible_file_ids) THEN content
      ELSE 'Redacted - user does not have access'
    END AS content,
    metadata,
    similarity,
    file_id = ANY(accessible_file_ids) AS has_access
  FROM 
    matched_docs
  ORDER BY 
    similarity DESC;
  
  -- Clean up
  DROP TABLE matched_docs;

EXCEPTION WHEN OTHERS THEN
  RAISE NOTICE 'Error in match_documents_with_permissions: %, SQLSTATE: %', SQLERRM, SQLSTATE;
  IF EXISTS (SELECT 1 FROM pg_class WHERE relname = 'matched_docs' AND relkind = 'r') THEN
    DROP TABLE matched_docs;
  END IF;
  RETURN;
END;
$_$;


ALTER FUNCTION "public"."match_documents_with_permissions"("query_embedding" "public"."vector", "user_email" "text", "company_id" "uuid", "match_count" integer, "similarity_threshold" double precision, "filter" "jsonb") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."match_documents_with_permissions"("query_embedding" "public"."vector", "user_email" "text", "company_id" "uuid", "match_count" integer, "similarity_threshold" double precision, "filter" "jsonb") IS 'Matches documents based on vector similarity search with optional company filtering, checking file permissions via SharePoint and redacting content for inaccessible files.';



CREATE OR REPLACE FUNCTION "public"."match_documents_with_user_permissions"("user_id" "uuid", "query_embedding" "public"."vector", "match_count" integer DEFAULT 10, "similarity_threshold" double precision DEFAULT 0.1, "filter" "jsonb" DEFAULT '{}'::"jsonb") RETURNS TABLE("id" "text", "content" "text", "metadata" "jsonb", "similarity" double precision, "file_id" "uuid", "permission_level" "text", "updated_at" timestamp with time zone)
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
DECLARE
    company_id text;
BEGIN
    -- Extract company_id from filter if provided
    company_id := filter->>'company_id';
    
    -- Perform the document matching query with permissions filtering
    RETURN QUERY 
    SELECT
        d.id::text,
        d.content,
        d.metadata,
        1 - (d.embedding::vector(1024) <=> query_embedding) AS similarity,
        d.file_id,
        p.permission_level::text,
        d.updated_at
    FROM 
        public.documents d
        -- Join with files table to get company isolation
        JOIN public.files f ON d.file_id = f.id
        -- Join with permissions table to filter by user access
        JOIN public.permissions p ON d.file_id = p.file_id AND p.user_id = match_documents_with_user_permissions.user_id
    WHERE 
        -- Apply company filter if provided
        (company_id IS NULL OR f.company_id = company_id::uuid)
        -- Only include documents with embeddings
        AND d.embedding IS NOT NULL
        -- Apply similarity threshold
        AND 1 - (d.embedding::vector(1024) <=> query_embedding) >= similarity_threshold
        -- Apply any additional filters from the filter parameter, excluding company_id
        AND (
            filter = '{}'::jsonb
            OR (d.metadata @> (filter - 'company_id'))
        )
    ORDER BY 
        similarity DESC,
        d.updated_at DESC
    LIMIT match_count;
END;
$$;


ALTER FUNCTION "public"."match_documents_with_user_permissions"("user_id" "uuid", "query_embedding" "public"."vector", "match_count" integer, "similarity_threshold" double precision, "filter" "jsonb") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."match_documents_with_user_permissions"("user_id" "uuid", "query_embedding" "public"."vector", "match_count" integer, "similarity_threshold" double precision, "filter" "jsonb") IS 'Matches documents based on vector similarity search and user permissions cached from get_user_files.';



CREATE OR REPLACE FUNCTION "public"."populate_missing_base_users"() RETURNS "jsonb"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_count integer := 0;
    v_user record;
BEGIN
    FOR v_user IN 
        SELECT au.id, au.email, au.raw_user_meta_data
        FROM auth.users au
        LEFT JOIN public.base_users bu ON au.id = bu.id
        WHERE bu.id IS NULL
    LOOP
        INSERT INTO public.base_users (
            id,
            display_name,
            email,
            avatar_url,
            created_at,
            updated_at
        )
        VALUES (
            v_user.id,
            COALESCE(v_user.raw_user_meta_data->>'full_name', v_user.email),
            v_user.email,
            v_user.raw_user_meta_data->>'avatar_url',
            NOW(),
            NOW()
        )
        ON CONFLICT (id) DO NOTHING;
        
        v_count := v_count + 1;
    END LOOP;
    
    RETURN jsonb_build_object(
        'success', true,
        'message', 'Base users populated',
        'count', v_count
    );
EXCEPTION WHEN OTHERS THEN
    RETURN jsonb_build_object(
        'success', false,
        'message', 'Error: ' || SQLERRM,
        'error_code', SQLSTATE
    );
END;
$$;


ALTER FUNCTION "public"."populate_missing_base_users"() OWNER TO "postgres";


COMMENT ON FUNCTION "public"."populate_missing_base_users"() IS 'Admin utility to populate missing base_users entries for existing auth users';



CREATE OR REPLACE FUNCTION "public"."refresh_user_permissions"("member_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
DECLARE
    success boolean;
    status int;
    result jsonb;
BEGIN
    -- Call the get_user_files Edge Function to refresh permissions
    SELECT
        INTO status, result
        supabase_functions.http((
            'POST',
            concat(url.origin, '/functions/v1/get_user_files'),
            ARRAY[('Authorization', 'Bearer ' || auth.role())],
            'application/json',
            ('{"memberId": "' || member_id::text || '"}')::jsonb
        ))
    FROM
        (SELECT 
            (CASE WHEN substr(current_setting('request.headers')::json->>'origin', 1, 8) = 'https://'
                THEN substr(current_setting('request.headers')::json->>'origin', 1)
                ELSE 'https://' || split_part(current_setting('request.headers')::json->>'host', ':', 1)
            END) AS origin
        ) AS url;

    success := (status >= 200 AND status < 300);
    
    IF NOT success THEN
        RAISE EXCEPTION 'Failed to refresh user permissions: %', result;
    END IF;

    RETURN success;
END;
$$;


ALTER FUNCTION "public"."refresh_user_permissions"("member_id" "uuid") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."refresh_user_permissions"("member_id" "uuid") IS 'Refreshes user permissions by calling the get_user_files Edge Function.';



CREATE OR REPLACE FUNCTION "public"."search_documents_by_company"("query_embedding" "public"."vector", "company_id" "uuid", "match_count" integer DEFAULT 10, "similarity_threshold" double precision DEFAULT 0.1, "filter" "jsonb" DEFAULT '{}'::"jsonb") RETURNS TABLE("id" "text", "content" "text", "file_id" "text", "file_path" "text", "file_name" "text", "file_type" "text", "data_source_id" "text", "metadata" "jsonb", "similarity" double precision, "updated_at" timestamp with time zone)
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
BEGIN
  -- Return documents for the specified company with similarity scores
  RETURN QUERY 
  SELECT
    d.id::text,
    d.content,
    f.id::text AS file_id,
    f.path AS file_path,
    f.name AS file_name,
    f.mime_type AS file_type,
    f.data_source_id::text,
    d.metadata,
    1 - (d.embedding <=> query_embedding) AS similarity,
    d.updated_at
  FROM 
    public.documents d
    JOIN public.files f ON d.file_id = f.id
  WHERE 
    f.company_id = search_documents_by_company.company_id
    AND (filter = '{}'::jsonb OR d.metadata @> filter)
    AND 1 - (d.embedding <=> query_embedding) >= similarity_threshold
  ORDER BY similarity DESC
  LIMIT match_count;
END;
$$;


ALTER FUNCTION "public"."search_documents_by_company"("query_embedding" "public"."vector", "company_id" "uuid", "match_count" integer, "similarity_threshold" double precision, "filter" "jsonb") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."search_documents_by_company"("query_embedding" "public"."vector", "company_id" "uuid", "match_count" integer, "similarity_threshold" double precision, "filter" "jsonb") IS 'Retrieves documents by company ID for external permission checking. This function returns all matching documents regardless of user permissions, allowing the edge function to handle permission checks via Microsoft Graph.';



CREATE OR REPLACE FUNCTION "public"."set_companies_created_by"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  NEW.created_by := auth.uid();
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."set_companies_created_by"() OWNER TO "postgres";


COMMENT ON FUNCTION "public"."set_companies_created_by"() IS 'Automatically sets created_by to current user when creating a company (uses SECURITY INVOKER to access auth.uid())';



CREATE OR REPLACE FUNCTION "public"."sync_current_user_sharepoint_profiles"() RETURNS "jsonb"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_user_id UUID;
    v_result jsonb;
BEGIN
    -- Get the current user's ID
    v_user_id := auth.uid();
    
    IF v_user_id IS NULL THEN
        RETURN jsonb_build_object('success', false, 'message', 'Authentication required');
    END IF;
    
    -- Call the sync function
    PERFORM public.sync_user_sharepoint_profiles(v_user_id);
    
    RETURN jsonb_build_object('success', true, 'message', 'SharePoint profiles synchronized');
EXCEPTION WHEN OTHERS THEN
    RETURN jsonb_build_object(
        'success', false, 
        'message', 'Error: ' || SQLERRM,
        'error_code', SQLSTATE
    );
END;
$$;


ALTER FUNCTION "public"."sync_current_user_sharepoint_profiles"() OWNER TO "postgres";


COMMENT ON FUNCTION "public"."sync_current_user_sharepoint_profiles"() IS 'API endpoint for current user to sync their SharePoint profiles';



CREATE OR REPLACE FUNCTION "public"."sync_oauth_metadata"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Update base_users raw_user_meta_data when a user authenticates
  INSERT INTO public.base_users (id, email, raw_user_meta_data)
  VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data)
  ON CONFLICT (id) DO UPDATE
  SET raw_user_meta_data = EXCLUDED.raw_user_meta_data,
      email = EXCLUDED.email;
  
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."sync_oauth_metadata"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."sync_user_sharepoint_profiles"("user_uuid" "uuid") RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    company_member_rec RECORD;
    data_source_rec RECORD;
    v_platform_user_id TEXT;
    v_display_name TEXT;
    v_existing_id UUID;
BEGIN
    -- Get the user's email for SharePoint user ID
    SELECT email INTO v_platform_user_id
    FROM public.base_users
    WHERE id = user_uuid;

    IF v_platform_user_id IS NULL THEN
        RAISE LOG 'No email found for user %', user_uuid;
        RETURN;
    END IF;

    -- Log the operation
    RAISE LOG 'Syncing SharePoint profiles for user % with email %', user_uuid, v_platform_user_id;

    -- Loop through all companies the user is a member of
    FOR company_member_rec IN 
        SELECT cm.id as company_member_id, cm.company_id
        FROM public.company_members cm
        WHERE cm.user_id = user_uuid
    LOOP
        RAISE LOG 'Checking company: %', company_member_rec.company_id;
        
        -- Find all SharePoint data sources for this company
        FOR data_source_rec IN
            SELECT ds.id, ds.display_name, ds.site_id, ds.tenant_id, ds.scope, ds.drive_id
            FROM public.data_sources ds
            WHERE ds.company_id = company_member_rec.company_id
            AND ds.platform_type = 'sharepoint'::public.platform_type
        LOOP
            RAISE LOG 'Found SharePoint data source: %', data_source_rec.id;
            
            -- Set display name
            v_display_name := COALESCE(data_source_rec.display_name, 'SharePoint Integration');
            
            -- Check if profile already exists
            SELECT id INTO v_existing_id
            FROM public.user_platform_profiles
            WHERE company_member_id = company_member_rec.company_member_id
            AND platform_type = 'sharepoint'::public.platform_type;
            
            IF v_existing_id IS NOT NULL THEN
                -- Update existing profile
                UPDATE public.user_platform_profiles
                SET platform_user_id = v_platform_user_id,
                    metadata = jsonb_build_object(
                        'data_source_id', data_source_rec.id,
                        'platform_type', 'sharepoint',
                        'display_name', v_display_name,
                        'scope', data_source_rec.scope,
                        'site_id', data_source_rec.site_id,
                        'tenant_id', data_source_rec.tenant_id,
                        'drive_id', data_source_rec.drive_id
                    ),
                    updated_at = now()
                WHERE id = v_existing_id;
                
                RAISE LOG 'Updated existing SharePoint profile: %', v_existing_id;
            ELSE
                -- Create new profile
                INSERT INTO public.user_platform_profiles (
                    company_member_id,
                    platform_type,
                    platform_user_id,
                    is_primary,
                    metadata
                ) VALUES (
                    company_member_rec.company_member_id,
                    'sharepoint'::public.platform_type,
                    v_platform_user_id,
                    true,
                    jsonb_build_object(
                        'data_source_id', data_source_rec.id,
                        'platform_type', 'sharepoint',
                        'display_name', v_display_name,
                        'scope', data_source_rec.scope,
                        'site_id', data_source_rec.site_id,
                        'tenant_id', data_source_rec.tenant_id,
                        'drive_id', data_source_rec.drive_id
                    )
                );
                
                RAISE LOG 'Created new SharePoint profile for user % in company %', 
                          user_uuid, company_member_rec.company_id;
            END IF;
        END LOOP;
    END LOOP;
EXCEPTION WHEN OTHERS THEN
    RAISE LOG 'Error in sync_user_sharepoint_profiles: %, %', SQLERRM, SQLSTATE;
END;
$$;


ALTER FUNCTION "public"."sync_user_sharepoint_profiles"("user_uuid" "uuid") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."sync_user_sharepoint_profiles"("user_uuid" "uuid") IS 'Checks if a user belongs to companies with SharePoint data sources and creates user_platform_profiles accordingly';



CREATE OR REPLACE FUNCTION "public"."test_user_creation"("test_email" "text" DEFAULT '<EMAIL>'::"text") RETURNS "jsonb"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_auth_id UUID;
    v_base_user_id UUID;
    v_result jsonb;
BEGIN
    -- Only allow in development
    IF current_setting('app.settings.environment', TRUE) != 'development' THEN
        RETURN jsonb_build_object(
            'success', false,
            'message', 'This function is only available in development environments'
        );
    END IF;

    -- Check if user already exists and delete if needed
    DELETE FROM auth.users WHERE email = test_email;
    DELETE FROM public.base_users WHERE email = test_email;
    
    -- Create test user in auth.users
    INSERT INTO auth.users (
        email,
        raw_user_meta_data,
        email_confirmed_at,
        created_at,
        updated_at
    ) VALUES (
        test_email,
        jsonb_build_object('full_name', 'Test User', 'avatar_url', 'https://example.com/avatar.png'),
        now(),
        now(),
        now()
    ) RETURNING id INTO v_auth_id;
    
    -- Wait briefly to allow the trigger to process
    PERFORM pg_sleep(1);
    
    -- Check if base_users entry was created
    SELECT id INTO v_base_user_id
    FROM public.base_users
    WHERE email = test_email;
    
    -- Compile and return result
    IF v_base_user_id IS NOT NULL THEN
        v_result := jsonb_build_object(
            'success', true,
            'message', 'Test user created and base_users entry was automatically populated',
            'auth_user_id', v_auth_id,
            'base_user_id', v_base_user_id,
            'trigger_working', true
        );
    ELSE
        -- Try to manually create the base_users entry as a fallback
        INSERT INTO public.base_users (
            id,
            display_name,
            email,
            created_at,
            updated_at
        ) VALUES (
            v_auth_id,
            'Test User (Manual)',
            test_email,
            now(),
            now()
        );
        
        v_result := jsonb_build_object(
            'success', false,
            'message', 'Test user created but base_users entry was NOT automatically populated. Manual insert attempted.',
            'auth_user_id', v_auth_id,
            'trigger_working', false,
            'debug_info', 'Check that the on_auth_user_created trigger is properly set up on auth.users'
        );
    END IF;
    
    -- Clean up test data
    DELETE FROM auth.users WHERE email = test_email;
    
    RETURN v_result;
EXCEPTION WHEN OTHERS THEN
    RETURN jsonb_build_object(
        'success', false,
        'message', 'Error during test: ' || SQLERRM,
        'error_code', SQLSTATE
    );
END;
$$;


ALTER FUNCTION "public"."test_user_creation"("test_email" "text") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."test_user_creation"("test_email" "text") IS 'Test function to verify that the auth.handle_new_user trigger correctly populates base_users';



CREATE OR REPLACE FUNCTION "public"."update_company_users"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
begin
    -- Update the user_company_access table
    update user_company_access
    set is_active = NEW.is_active
    where user_id = NEW.user_id
    and company_id = NEW.company_id;
    
    return NEW;
end;
$$;


ALTER FUNCTION "public"."update_company_users"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_contact_info"("p_user_id" "uuid", "p_company_id" "uuid", "p_email" "text", "p_phone" "text") RETURNS "json"
    LANGUAGE "plpgsql"
    AS $_$
declare
  v_result json;
  v_member_exists boolean;
  v_is_admin boolean;
  v_requesting_user_id uuid;
  v_member_id uuid;
begin
  -- Input validation
  if p_user_id is null or p_company_id is null or p_email is null then
    return json_build_object(
      'success', false,
      'error', 'Missing required fields'
    );
  end if;

  -- Validate email format
  if not p_email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' then
    return json_build_object(
      'success', false,
      'error', 'Invalid email format'
    );
  end if;

  -- Validate phone format (if provided)
  if p_phone is not null and not p_phone ~ '^\d{11}$' then
    return json_build_object(
      'success', false,
      'error', 'Phone number must be 11 digits'
    );
  end if;

  -- Get the requesting user's ID from the RLS context
  v_requesting_user_id := auth.uid();
  
  if v_requesting_user_id is null then
    return json_build_object(
      'success', false,
      'error', 'Authentication required'
    );
  end if;

  -- Check if the user exists in company_members and get member_id
  select cm.id, exists(
    select 1 
    from company_members cm2
    where cm2.user_id = p_user_id 
    and cm2.company_id = p_company_id
  ) into v_member_id, v_member_exists
  from company_members cm
  where cm.user_id = p_user_id 
  and cm.company_id = p_company_id;
  
  if not v_member_exists then
    return json_build_object(
      'success', false,
      'error', 'User not found in company'
    );
  end if;
  
  -- Check if requesting user is admin or updating their own info
  select role = 'admin' into v_is_admin
  from company_user_profiles
  where user_id = v_requesting_user_id
  and company_id = p_company_id;
  
  if not (v_is_admin or v_requesting_user_id = p_user_id) then
    return json_build_object(
      'success', false,
      'error', 'Unauthorized to update user contact information'
    );
  end if;

  -- Start transaction
  begin
    -- Update user_platform_profiles for email
    insert into user_platform_profiles (
      company_member_id,
      platform_type,
      platform_user_id,
      is_primary,
      metadata
    )
    values (
      v_member_id,
      'webchat',
      p_email,
      true,
      jsonb_build_object('type', 'email')
    )
    on conflict (company_member_id, platform_type, platform_user_id)
    do update set
      updated_at = now();

    -- Then handle phone if provided
    if p_phone is not null then
      insert into user_platform_profiles (
        company_member_id,
        platform_type,
        platform_user_id,
        is_primary,
        metadata
      )
      values (
        v_member_id,
        'whatsapp',
        p_phone,
        true,
        jsonb_build_object('type', 'phone')
      )
      on conflict (company_member_id, platform_type, platform_user_id)
      do update set
        updated_at = now();
    end if;

    return json_build_object(
      'success', true,
      'data', json_build_object(
        'user_id', p_user_id,
        'email', p_email,
        'phone', p_phone
      )
    );
  exception when others then
    -- Rollback will happen automatically
    return json_build_object(
      'success', false,
      'error', SQLERRM
    );
  end;
end;
$_$;


ALTER FUNCTION "public"."update_contact_info"("p_user_id" "uuid", "p_company_id" "uuid", "p_email" "text", "p_phone" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_files_table"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    INSERT INTO files (
        id,
        company_id,
        data_source_id,
        external_id,
        name,
        path,
        size,
        mime_type,
        storage_provider,
        summary,
        added_to_knowledge_base,
        created_at,
        updated_at,
        created_by,
        updated_by
    )
    VALUES (
        NEW.id,
        NULLIF(NEW.metadata->>'company_id', '')::uuid,
        NULLIF(NEW.metadata->>'data_source_id', '')::uuid,
        NEW.metadata->>'external_id',
        NEW.metadata->>'name',
        NEW.metadata->>'path',
        CASE WHEN NEW.metadata->>'size' = '' THEN NULL ELSE (NEW.metadata->>'size')::bigint END,
        NEW.metadata->>'mime_type',
        NEW.metadata->>'storage_provider'::platform,
        NEW.metadata->>'summary',
        CASE WHEN NEW.metadata->>'added_to_knowledge_base' = '' THEN NULL ELSE (NEW.metadata->>'added_to_knowledge_base')::boolean END,
        CASE WHEN NEW.metadata->>'created_at' = '' THEN NOW() ELSE (NEW.metadata->>'created_at')::timestamptz END,
        CASE WHEN NEW.metadata->>'updated_at' = '' THEN NOW() ELSE (NEW.metadata->>'updated_at')::timestamptz END,
        NULLIF(NEW.metadata->>'created_by', '')::uuid,
        NULLIF(NEW.metadata->>'updated_by', '')::uuid
    )
    ON CONFLICT (id) DO UPDATE SET
        company_id = EXCLUDED.company_id,
        data_source_id = EXCLUDED.data_source_id,
        external_id = EXCLUDED.external_id,
        name = EXCLUDED.name,
        path = EXCLUDED.path,
        size = EXCLUDED.size,
        mime_type = EXCLUDED.mime_type,
        storage_provider = EXCLUDED.storage_provider,
        summary = EXCLUDED.summary,
        added_to_knowledge_base = EXCLUDED.added_to_knowledge_base,
        updated_at = EXCLUDED.updated_at,
        updated_by = EXCLUDED.updated_by;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_files_table"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."upsert_document"("p_id" "uuid", "p_content" "text", "p_metadata" "jsonb", "p_file_id" "uuid") RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    INSERT INTO public.documents (id, content, metadata, file_id)
    VALUES (p_id, p_content, p_metadata, p_file_id)
    ON CONFLICT (id) DO UPDATE
    SET content = EXCLUDED.content,
        metadata = EXCLUDED.metadata,
        file_id = EXCLUDED.file_id;
END;
$$;


ALTER FUNCTION "public"."upsert_document"("p_id" "uuid", "p_content" "text", "p_metadata" "jsonb", "p_file_id" "uuid") OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."base_users" (
    "id" "uuid" NOT NULL,
    "display_name" "text",
    "avatar_url" "text",
    "bio" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "email" "text",
    "raw_user_meta_data" "jsonb",
    "phone_number" bigint,
    CONSTRAINT "phone_number_format_check" CHECK ((("phone_number" IS NULL) OR (("phone_number")::"text" ~ '^[1-9][0-9]{10,14}$'::"text")))
);


ALTER TABLE "public"."base_users" OWNER TO "postgres";


COMMENT ON COLUMN "public"."base_users"."email" IS 'User email synced from auth.users';



COMMENT ON COLUMN "public"."base_users"."raw_user_meta_data" IS 'Stores raw user metadata from OAuth providers like Azure and Google';



COMMENT ON COLUMN "public"."base_users"."phone_number" IS 'International phone number without + symbol, starting with country code (e.g. ************ for UK number +44 7123 456789)';



CREATE TABLE IF NOT EXISTS "public"."billing_records" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "company_id" "uuid" NOT NULL,
    "billing_period_start" "date" NOT NULL,
    "billing_period_end" "date" NOT NULL,
    "amount_due" numeric(10,2),
    "amount_paid" numeric(10,2),
    "seats_charged" integer,
    "transcription_time_allocated" integer,
    "transcription_time_used" integer,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."billing_records" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."chat_messages" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "user_id" "uuid",
    "company_id" "uuid",
    "session_id" "uuid",
    "content" "text",
    "role" "text",
    "source_documents" "jsonb",
    "chat_id" "uuid",
    "metadata" "jsonb"
);


ALTER TABLE "public"."chat_messages" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."communication_channels" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "company_id" "uuid" NOT NULL,
    "user_id" "uuid",
    "platform_type" "public"."platform_type" NOT NULL,
    "channel_id" "text",
    "dedicated_number" boolean DEFAULT false,
    "default_number" boolean DEFAULT false,
    "oauth_token_id" "uuid" DEFAULT '5a3b6c9f-a594-4954-9805-296afa93c6f9'::"uuid",
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL
);


ALTER TABLE "public"."communication_channels" OWNER TO "postgres";


COMMENT ON TABLE "public"."communication_channels" IS 'Communication channels for companies. Platform-specific identifiers are stored in the channel_id column.';



COMMENT ON COLUMN "public"."communication_channels"."oauth_token_id" IS 'references supabase vault to get token';



CREATE TABLE IF NOT EXISTS "public"."companies" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "status" "public"."company_status" DEFAULT 'trial'::"public"."company_status" NOT NULL,
    "transcription_time_allocated" numeric,
    "transcription_time_used" numeric,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "is_public" boolean DEFAULT false NOT NULL,
    "created_by" "uuid"
);


ALTER TABLE "public"."companies" OWNER TO "postgres";


COMMENT ON TABLE "public"."companies" IS 'Companies with RLS policies that restrict access to members and limit updates/deletes to admins';



COMMENT ON COLUMN "public"."companies"."is_public" IS 'Is the company data public';



COMMENT ON COLUMN "public"."companies"."created_by" IS 'The user who created this company. Used by triggers to assign admin role.';



CREATE TABLE IF NOT EXISTS "public"."company_invitations" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "company_id" "uuid" NOT NULL,
    "invited_by" "uuid" NOT NULL,
    "role" "text" NOT NULL,
    "email" "text",
    "token" "text" DEFAULT ("gen_random_uuid"())::"text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "expires_at" timestamp with time zone DEFAULT ("now"() + '24:00:00'::interval) NOT NULL,
    "invitation_type" "public"."invitation_type" DEFAULT 'one_time'::"public"."invitation_type" NOT NULL,
    "metadata" "jsonb" DEFAULT '{}'::"jsonb"
);


ALTER TABLE "public"."company_invitations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."company_invites" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "company_id" "uuid",
    "email" "text" NOT NULL,
    "role" "public"."user_role" DEFAULT 'member'::"public"."user_role",
    "status" "public"."invite_status" DEFAULT 'pending'::"public"."invite_status",
    "invited_by" "uuid",
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "expires_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", ("now"() + '7 days'::interval)) NOT NULL
);


ALTER TABLE "public"."company_invites" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."company_members" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid",
    "company_id" "uuid",
    "role" "public"."user_role" DEFAULT 'member'::"public"."user_role",
    "display_name" "text",
    "avatar_url" "text",
    "is_active" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."company_members" OWNER TO "postgres";


COMMENT ON TABLE "public"."company_members" IS 'Company members table. Contains user roles including admins (migrated from old admin_uid column in companies table)';



CREATE TABLE IF NOT EXISTS "public"."company_personalization" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "company_id" "uuid" NOT NULL,
    "custom_prompt" "text",
    "branding_logo" "text",
    "small_logo" "text",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "slug" "text"
);


ALTER TABLE "public"."company_personalization" OWNER TO "postgres";


COMMENT ON COLUMN "public"."company_personalization"."slug" IS 'URL-friendly version of company name';



CREATE OR REPLACE VIEW "public"."company_user_profiles" WITH ("security_invoker"='on') AS
 SELECT "cm"."id" AS "company_member_id",
    "cm"."company_id",
    "cm"."user_id",
    "cm"."role",
    "cm"."is_active",
    "bu"."email" AS "user_email",
    "bu"."phone_number" AS "user_phone_number",
    "bu"."raw_user_meta_data",
    "c"."name" AS "company_name",
    "cp"."slug" AS "company_slug",
    "ci"."id" AS "invite_id",
    "ci"."status" AS "invite_status",
        CASE
            WHEN "cm"."is_active" THEN 'active'::"text"
            WHEN ("ci"."status" = 'pending'::"public"."invite_status") THEN 'pending'::"text"
            ELSE 'inactive'::"text"
        END AS "user_status"
   FROM (((("public"."company_members" "cm"
     JOIN "public"."base_users" "bu" ON (("bu"."id" = "cm"."user_id")))
     JOIN "public"."companies" "c" ON (("c"."id" = "cm"."company_id")))
     LEFT JOIN "public"."company_personalization" "cp" ON (("cp"."company_id" = "c"."id")))
     LEFT JOIN "public"."company_invites" "ci" ON ((("ci"."email" = "bu"."email") AND ("ci"."company_id" = "cm"."company_id") AND ("ci"."status" = 'pending'::"public"."invite_status"))))
  WHERE (EXISTS ( SELECT 1
           FROM "public"."company_members"
          WHERE (("company_members"."company_id" = "cm"."company_id") AND ("company_members"."user_id" = "auth"."uid"()) AND ("company_members"."is_active" = true))));


ALTER TABLE "public"."company_user_profiles" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."data_sources" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "company_id" "uuid" NOT NULL,
    "platform_type" "public"."platform_type" NOT NULL,
    "display_name" "text",
    "access_token" "text",
    "id_token" "text" NOT NULL,
    "secret_oauth" "uuid" NOT NULL,
    "tenant_id" "text",
    "site_id" "text",
    "drive_id" "text",
    "deltas" "text",
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "scope" "text"
);


ALTER TABLE "public"."data_sources" OWNER TO "postgres";


COMMENT ON COLUMN "public"."data_sources"."scope" IS 'The scope of access for this data source (e.g., specific folders, sites, or channels)';



CREATE TABLE IF NOT EXISTS "public"."documents" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "content" "text",
    "metadata" "jsonb",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "file_id" "uuid",
    "embedding" "public"."vector"(1024),
    "external_id" "text"
);

ALTER TABLE ONLY "public"."documents" FORCE ROW LEVEL SECURITY;


ALTER TABLE "public"."documents" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."files" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "company_id" "uuid" NOT NULL,
    "data_source_id" "uuid" NOT NULL,
    "external_id" "text" NOT NULL,
    "name" "text",
    "path" "text",
    "size" bigint,
    "mime_type" "text",
    "summary" "text",
    "added_to_knowledge_base" boolean DEFAULT false,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "is_ingested" boolean DEFAULT false,
    "hash" "text",
    "processing_error" "text",
    "metadata" "jsonb",
    "file_embedding" "public"."vector"(1024)
);

ALTER TABLE ONLY "public"."files" FORCE ROW LEVEL SECURITY;


ALTER TABLE "public"."files" OWNER TO "postgres";


COMMENT ON COLUMN "public"."files"."is_ingested" IS 'a flag for if the column has been ingested';



COMMENT ON COLUMN "public"."files"."hash" IS 'system file contents changes tracking hash';



CREATE TABLE IF NOT EXISTS "public"."map_question_to_responses" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "question_id" "uuid" NOT NULL,
    "response_id" "uuid" NOT NULL
);


ALTER TABLE "public"."map_question_to_responses" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."oauth_tokens" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "company_data_source_id" "uuid" NOT NULL,
    "secret_id" "uuid",
    "tenant_id" "text",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "token_obj" "jsonb",
    "company_id" "uuid"
);


ALTER TABLE "public"."oauth_tokens" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."permissions" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "file_id" "uuid" NOT NULL,
    "permission_level" "public"."permission_level" NOT NULL,
    "source" "text",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "permission_id" "text",
    "share_id" "text"
);


ALTER TABLE "public"."permissions" OWNER TO "postgres";


COMMENT ON COLUMN "public"."permissions"."permission_id" IS 'a column for permissions';



COMMENT ON COLUMN "public"."permissions"."share_id" IS 'for rapidly sharing the files';



CREATE TABLE IF NOT EXISTS "public"."team_members" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "team_id" "uuid" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "added_at" timestamp with time zone DEFAULT "now"(),
    "external_id" "text",
    "platform_type" "public"."platform_type"
);


ALTER TABLE "public"."team_members" OWNER TO "postgres";


COMMENT ON TABLE "public"."team_members" IS 'The users in a team';



COMMENT ON COLUMN "public"."team_members"."external_id" IS 'Sharepoint id of the user';



CREATE TABLE IF NOT EXISTS "public"."team_permissions" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "team_id" "uuid" NOT NULL,
    "file_id" "uuid" NOT NULL,
    "permission_level" "public"."permission_level" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."team_permissions" OWNER TO "postgres";


COMMENT ON TABLE "public"."team_permissions" IS 'Files that Teams have access to';



CREATE TABLE IF NOT EXISTS "public"."teams" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "company_id" "uuid" NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "parent_team_id" "uuid",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "team_id" "text"
);


ALTER TABLE "public"."teams" OWNER TO "postgres";


COMMENT ON TABLE "public"."teams" IS 'The name and external ID of the team, as well as the company that owns the team';



COMMENT ON COLUMN "public"."teams"."team_id" IS 'the system team ID';



CREATE TABLE IF NOT EXISTS "public"."totm_inbox_questions" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "company_id" "uuid" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "question_text" "text",
    "status" "text",
    "priority" "text",
    "category" "text",
    "assigned_to" "uuid",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."totm_inbox_questions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."totm_inbox_responses" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "question_id" "uuid" NOT NULL,
    "expert_id" "uuid" NOT NULL,
    "response_text" "text",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."totm_inbox_responses" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."transcription_usage_records" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "company_id" "uuid" NOT NULL,
    "file_id" "uuid" NOT NULL,
    "transcription_time_used" integer,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "transcription_data" "jsonb" NOT NULL
);


ALTER TABLE "public"."transcription_usage_records" OWNER TO "postgres";


COMMENT ON COLUMN "public"."transcription_usage_records"."transcription_data" IS 'the returned data from deepgram';



CREATE TABLE IF NOT EXISTS "public"."upsertion_records" (
    "uuid" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "key" "text" NOT NULL,
    "namespace" "text" NOT NULL,
    "updated_at" double precision NOT NULL,
    "group_id" "text"
);


ALTER TABLE "public"."upsertion_records" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_contact_info" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "company_member_id" "uuid",
    "type" "text" NOT NULL,
    "value" "text" NOT NULL,
    "is_primary" boolean DEFAULT false,
    "verified" boolean DEFAULT false,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."user_contact_info" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_platform_profiles" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "company_member_id" "uuid",
    "platform_type" "public"."platform_type" NOT NULL,
    "platform_user_id" "text" NOT NULL,
    "is_primary" boolean DEFAULT false,
    "metadata" "jsonb",
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()),
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()),
    "encrypted_refresh_token" "uuid"
);


ALTER TABLE "public"."user_platform_profiles" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_sessions" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "session_id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."user_sessions" OWNER TO "postgres";


COMMENT ON TABLE "public"."user_sessions" IS 'tracking user''s chat sessions and their communication platform. This aids in conversation history recall.';



CREATE OR REPLACE VIEW "public"."view_totm_inbox_questions_responses" WITH ("security_invoker"='on') AS
 SELECT "q"."id" AS "question_id",
    "q"."company_id",
    "q"."user_id" AS "asker_id",
    "q"."assigned_to" AS "responsible_party_id",
    "q"."question_text",
    "q"."status",
    "q"."priority",
    "q"."category",
    "q"."created_at" AS "question_created_at",
    "r"."id" AS "response_id",
    "r"."expert_id",
    "r"."response_text",
    "r"."created_at" AS "response_created_at",
    "r"."updated_at" AS "response_updated_at"
   FROM ("public"."totm_inbox_questions" "q"
     LEFT JOIN "public"."totm_inbox_responses" "r" ON (("q"."id" = "r"."question_id")));


ALTER TABLE "public"."view_totm_inbox_questions_responses" OWNER TO "postgres";


ALTER TABLE ONLY "public"."base_users"
    ADD CONSTRAINT "base_users_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."billing_records"
    ADD CONSTRAINT "billing_records_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."chat_messages"
    ADD CONSTRAINT "chat_messages_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."communication_channels"
    ADD CONSTRAINT "communication_channels_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."companies"
    ADD CONSTRAINT "companies_name_key" UNIQUE ("name");



ALTER TABLE ONLY "public"."companies"
    ADD CONSTRAINT "companies_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."company_invitations"
    ADD CONSTRAINT "company_invitations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."company_invitations"
    ADD CONSTRAINT "company_invitations_token_key" UNIQUE ("token");



ALTER TABLE ONLY "public"."company_invites"
    ADD CONSTRAINT "company_invites_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."company_members"
    ADD CONSTRAINT "company_members_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."company_members"
    ADD CONSTRAINT "company_members_user_id_company_id_key" UNIQUE ("user_id", "company_id");



ALTER TABLE ONLY "public"."company_personalization"
    ADD CONSTRAINT "company_personalization_company_id_key" UNIQUE ("company_id");



ALTER TABLE ONLY "public"."company_personalization"
    ADD CONSTRAINT "company_personalization_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."data_sources"
    ADD CONSTRAINT "data_sources_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."documents"
    ADD CONSTRAINT "documents_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."files"
    ADD CONSTRAINT "files_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."map_question_to_responses"
    ADD CONSTRAINT "map_question_to_responses_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."oauth_tokens"
    ADD CONSTRAINT "oauth_tokens_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."permissions"
    ADD CONSTRAINT "permissions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."base_users"
    ADD CONSTRAINT "phone_number_unique" UNIQUE ("phone_number");



ALTER TABLE ONLY "public"."team_members"
    ADD CONSTRAINT "team_members_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."team_members"
    ADD CONSTRAINT "team_members_team_id_user_id_key" UNIQUE ("team_id", "user_id");



ALTER TABLE ONLY "public"."team_permissions"
    ADD CONSTRAINT "team_permissions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."teams"
    ADD CONSTRAINT "teams_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."totm_inbox_questions"
    ADD CONSTRAINT "totm_inbox_questions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."totm_inbox_responses"
    ADD CONSTRAINT "totm_inbox_responses_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."transcription_usage_records"
    ADD CONSTRAINT "transcription_usage_records_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."files"
    ADD CONSTRAINT "unique_external_id_company_id" UNIQUE ("external_id", "company_id");



ALTER TABLE ONLY "public"."upsertion_records"
    ADD CONSTRAINT "upsertion_records_key_namespace_key" UNIQUE ("key", "namespace");



ALTER TABLE ONLY "public"."upsertion_records"
    ADD CONSTRAINT "upsertion_records_pkey" PRIMARY KEY ("uuid");



ALTER TABLE ONLY "public"."user_contact_info"
    ADD CONSTRAINT "user_contact_info_company_member_id_type_value_key" UNIQUE ("company_member_id", "type", "value");



ALTER TABLE ONLY "public"."user_contact_info"
    ADD CONSTRAINT "user_contact_info_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_platform_profiles"
    ADD CONSTRAINT "user_platform_profiles_company_member_id_platform_type_key" UNIQUE ("company_member_id", "platform_type");



ALTER TABLE ONLY "public"."user_platform_profiles"
    ADD CONSTRAINT "user_platform_profiles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_sessions"
    ADD CONSTRAINT "user_sessions_pkey" PRIMARY KEY ("id");



CREATE INDEX "group_id_index" ON "public"."upsertion_records" USING "btree" ("group_id");



CREATE INDEX "idx_communication_channels_company_type" ON "public"."communication_channels" USING "btree" ("company_id", "platform_type");



CREATE INDEX "idx_companies_created_by" ON "public"."companies" USING "btree" ("created_by");



CREATE INDEX "idx_company_members_company_id" ON "public"."company_members" USING "btree" ("company_id");



CREATE INDEX "idx_company_members_user_id" ON "public"."company_members" USING "btree" ("user_id");



CREATE INDEX "idx_company_personalization_company_id" ON "public"."company_personalization" USING "btree" ("company_id");



CREATE INDEX "idx_data_sources_company_type" ON "public"."data_sources" USING "btree" ("company_id", "platform_type");



CREATE INDEX "idx_user_contact_info_company_member" ON "public"."user_contact_info" USING "btree" ("company_member_id");



CREATE INDEX "idx_user_platform_profiles_member_type" ON "public"."user_platform_profiles" USING "btree" ("company_member_id", "platform_type");



CREATE INDEX "key_index" ON "public"."upsertion_records" USING "btree" ("key");



CREATE INDEX "namespace_index" ON "public"."upsertion_records" USING "btree" ("namespace");



CREATE INDEX "updated_at_index" ON "public"."upsertion_records" USING "btree" ("updated_at");



CREATE OR REPLACE TRIGGER "after_company_insert" AFTER INSERT ON "public"."companies" FOR EACH ROW EXECUTE FUNCTION "public"."add_creator_as_admin"();



CREATE OR REPLACE TRIGGER "on_data_source_created" AFTER INSERT ON "public"."data_sources" FOR EACH ROW EXECUTE FUNCTION "public"."handle_data_source_user_profile"();



ALTER TABLE ONLY "public"."base_users"
    ADD CONSTRAINT "base_users_id_fkey" FOREIGN KEY ("id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."billing_records"
    ADD CONSTRAINT "billing_records_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."chat_messages"
    ADD CONSTRAINT "chat_messages_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."chat_messages"
    ADD CONSTRAINT "chat_messages_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."communication_channels"
    ADD CONSTRAINT "communication_channels_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."communication_channels"
    ADD CONSTRAINT "communication_channels_oauth_token_id_fkey1" FOREIGN KEY ("oauth_token_id") REFERENCES "vault"."secrets"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."communication_channels"
    ADD CONSTRAINT "communication_channels_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."base_users"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."companies"
    ADD CONSTRAINT "companies_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."company_invitations"
    ADD CONSTRAINT "company_invitations_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."company_invitations"
    ADD CONSTRAINT "company_invitations_invited_by_fkey" FOREIGN KEY ("invited_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."company_invites"
    ADD CONSTRAINT "company_invites_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."company_invites"
    ADD CONSTRAINT "company_invites_invited_by_fkey" FOREIGN KEY ("invited_by") REFERENCES "auth"."users"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."company_members"
    ADD CONSTRAINT "company_members_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."company_members"
    ADD CONSTRAINT "company_members_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."base_users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."company_personalization"
    ADD CONSTRAINT "company_personalization_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."data_sources"
    ADD CONSTRAINT "data_sources_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."data_sources"
    ADD CONSTRAINT "data_sources_secret_oauth_fkey" FOREIGN KEY ("secret_oauth") REFERENCES "vault"."secrets"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."documents"
    ADD CONSTRAINT "documents_file_id_fkey" FOREIGN KEY ("file_id") REFERENCES "public"."files"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."files"
    ADD CONSTRAINT "files_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."map_question_to_responses"
    ADD CONSTRAINT "map_question_to_responses_question_id_fkey" FOREIGN KEY ("question_id") REFERENCES "public"."totm_inbox_questions"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."map_question_to_responses"
    ADD CONSTRAINT "map_question_to_responses_response_id_fkey" FOREIGN KEY ("response_id") REFERENCES "public"."totm_inbox_responses"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."oauth_tokens"
    ADD CONSTRAINT "oauth_tokens_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."permissions"
    ADD CONSTRAINT "permissions_file_id_fkey" FOREIGN KEY ("file_id") REFERENCES "public"."files"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."team_permissions"
    ADD CONSTRAINT "team_permissions_file_id_fkey" FOREIGN KEY ("file_id") REFERENCES "public"."files"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."team_permissions"
    ADD CONSTRAINT "team_permissions_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."teams"
    ADD CONSTRAINT "teams_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."teams"
    ADD CONSTRAINT "teams_parent_team_id_fkey" FOREIGN KEY ("parent_team_id") REFERENCES "public"."teams"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."totm_inbox_questions"
    ADD CONSTRAINT "totm_inbox_questions_assigned_to_fkey1" FOREIGN KEY ("assigned_to") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."totm_inbox_questions"
    ADD CONSTRAINT "totm_inbox_questions_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."totm_inbox_questions"
    ADD CONSTRAINT "totm_inbox_questions_user_id_fkey1" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."totm_inbox_responses"
    ADD CONSTRAINT "totm_inbox_responses_question_id_fkey" FOREIGN KEY ("question_id") REFERENCES "public"."totm_inbox_questions"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."transcription_usage_records"
    ADD CONSTRAINT "transcription_usage_records_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."transcription_usage_records"
    ADD CONSTRAINT "transcription_usage_records_file_id_fkey" FOREIGN KEY ("file_id") REFERENCES "public"."files"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_contact_info"
    ADD CONSTRAINT "user_contact_info_company_member_id_fkey" FOREIGN KEY ("company_member_id") REFERENCES "public"."company_members"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_platform_profiles"
    ADD CONSTRAINT "user_platform_profiles_company_member_id_fkey" FOREIGN KEY ("company_member_id") REFERENCES "public"."company_members"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_platform_profiles"
    ADD CONSTRAINT "user_platform_profiles_encrypted_refresh _token_fkey" FOREIGN KEY ("encrypted_refresh_token") REFERENCES "vault"."secrets"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_sessions"
    ADD CONSTRAINT "user_sessions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON UPDATE CASCADE ON DELETE CASCADE;



CREATE POLICY "Allow admins to delete company members" ON "public"."company_members" FOR DELETE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."company_id" = "company_members"."company_id") AND ("cm"."user_id" = "auth"."uid"()) AND ("cm"."role" = 'admin'::"public"."user_role") AND ("cm"."is_active" = true)))));



CREATE POLICY "Allow admins to update company members" ON "public"."company_members" FOR UPDATE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."company_id" = "company_members"."company_id") AND ("cm"."user_id" = "auth"."uid"()) AND ("cm"."role" = 'admin'::"public"."user_role") AND ("cm"."is_active" = true))))) WITH CHECK (("user_id" <> "auth"."uid"()));



CREATE POLICY "Allow authenticated users to create companies" ON "public"."companies" FOR INSERT TO "authenticated" WITH CHECK (("created_by" = "auth"."uid"()));



CREATE POLICY "Allow authenticated users to delete documents" ON "public"."documents" FOR DELETE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM ("public"."company_members" "cm"
     JOIN "public"."files" "f" ON (("f"."id" = "documents"."file_id")))
  WHERE (("cm"."user_id" = "auth"."uid"()) AND ("cm"."role" = ANY (ARRAY['admin'::"public"."user_role", 'expert'::"public"."user_role"])) AND ("cm"."company_id" = "f"."company_id")))));



CREATE POLICY "Allow authenticated users to delete documents they have permiss" ON "public"."documents" FOR DELETE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM ("public"."permissions" "p"
     JOIN "public"."files" "f" ON (("p"."file_id" = "f"."id")))
  WHERE (("p"."user_id" = "auth"."uid"()) AND ("f"."id" = "documents"."file_id")))));



CREATE POLICY "Allow authenticated users to delete their own companies" ON "public"."companies" FOR DELETE TO "authenticated" USING (("created_by" = "auth"."uid"()));



CREATE POLICY "Allow authenticated users to insert documents" ON "public"."documents" FOR INSERT TO "authenticated" WITH CHECK ((EXISTS ( SELECT 1
   FROM ("public"."company_members" "cm"
     JOIN "public"."files" "f" ON (("f"."id" = "documents"."file_id")))
  WHERE (("cm"."user_id" = "auth"."uid"()) AND ("cm"."role" = ANY (ARRAY['admin'::"public"."user_role", 'expert'::"public"."user_role"])) AND ("cm"."company_id" = "f"."company_id")))));



CREATE POLICY "Allow authenticated users to insert their own chat messages" ON "public"."chat_messages" FOR INSERT TO "authenticated" WITH CHECK (("user_id" = "auth"."uid"()));



CREATE POLICY "Allow authenticated users to read documents" ON "public"."documents" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM ("public"."company_members" "cm"
     JOIN "public"."files" "f" ON (("f"."id" = "documents"."file_id")))
  WHERE (("cm"."user_id" = "auth"."uid"()) AND ("cm"."role" = ANY (ARRAY['admin'::"public"."user_role", 'expert'::"public"."user_role"])) AND ("cm"."company_id" = "f"."company_id")))));



CREATE POLICY "Allow authenticated users to select documents they have permiss" ON "public"."documents" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM (("public"."permissions" "p"
     JOIN "public"."files" "f" ON (("p"."file_id" = "f"."id")))
     JOIN "public"."companies" "c" ON (("f"."company_id" = "c"."id")))
  WHERE (((("p"."file_id" = "documents"."file_id") OR ("p"."file_id" = (("documents"."metadata" ->> 'file_id'::"text"))::"uuid")) AND ("p"."user_id" = "auth"."uid"())) OR ("c"."is_public" = true)))));



CREATE POLICY "Allow authenticated users to select their own chat messages" ON "public"."chat_messages" FOR SELECT TO "authenticated" USING (("user_id" = "auth"."uid"()));



CREATE POLICY "Allow authenticated users to select their own companies" ON "public"."companies" FOR SELECT TO "authenticated" USING (("created_by" = "auth"."uid"()));



CREATE POLICY "Allow authenticated users to update documents they have permiss" ON "public"."documents" FOR UPDATE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM ("public"."company_members" "cm"
     JOIN "public"."files" "f" ON (("f"."id" = "documents"."file_id")))
  WHERE (("cm"."user_id" = "auth"."uid"()) AND ("cm"."role" = ANY (ARRAY['admin'::"public"."user_role", 'expert'::"public"."user_role"])) AND ("cm"."company_id" = "f"."company_id"))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM ("public"."company_members" "cm"
     JOIN "public"."files" "f" ON (("f"."id" = "documents"."file_id")))
  WHERE (("cm"."user_id" = "auth"."uid"()) AND ("cm"."role" = ANY (ARRAY['admin'::"public"."user_role", 'expert'::"public"."user_role"])) AND ("cm"."company_id" = "f"."company_id")))));



CREATE POLICY "Allow authenticated users to update their own companies" ON "public"."companies" FOR UPDATE TO "authenticated" USING (("created_by" = "auth"."uid"())) WITH CHECK (("created_by" = "auth"."uid"()));



CREATE POLICY "Allow company admins to add members" ON "public"."company_members" FOR INSERT TO "authenticated" WITH CHECK ((EXISTS ( SELECT 1
   FROM ("public"."companies" "c"
     JOIN "public"."company_members" "cm" ON (("c"."id" = "cm"."company_id")))
  WHERE (("cm"."user_id" = "auth"."uid"()) AND ("cm"."role" = 'admin'::"public"."user_role") AND ("cm"."is_active" = true) AND ("c"."id" = "company_members"."company_id")))));



CREATE POLICY "Allow company admins to manage company personalization" ON "public"."company_personalization" TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."company_id" = "company_personalization"."company_id") AND ("cm"."user_id" = "auth"."uid"()) AND ("cm"."role" = 'admin'::"public"."user_role"))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."company_id" = "company_personalization"."company_id") AND ("cm"."user_id" = "auth"."uid"()) AND ("cm"."role" = 'admin'::"public"."user_role")))));



CREATE POLICY "Allow company members to view company personalization" ON "public"."company_personalization" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."company_id" = "company_personalization"."company_id") AND ("cm"."user_id" = "auth"."uid"())))));



CREATE POLICY "Allow users to delete their own company membership" ON "public"."company_members" FOR DELETE TO "authenticated" USING (("user_id" = "auth"."uid"()));



CREATE POLICY "Allow users to update their own company membership" ON "public"."company_members" FOR UPDATE TO "authenticated" USING (("user_id" = "auth"."uid"())) WITH CHECK ((("user_id" = "auth"."uid"()) OR (EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."company_id" = "company_members"."company_id") AND ("cm"."user_id" = "auth"."uid"()) AND ("cm"."role" = 'admin'::"public"."user_role") AND ("cm"."is_active" = true))))));



CREATE POLICY "Allow users to view their own company memberships" ON "public"."company_members" FOR SELECT TO "authenticated" USING (("user_id" = "auth"."uid"()));



CREATE POLICY "Base users visibility policy" ON "public"."base_users" FOR SELECT TO "authenticated" USING ((("id" = "auth"."uid"()) OR (EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."user_id" = "auth"."uid"()) AND ("cm"."role" = 'admin'::"public"."user_role") AND ("cm"."is_active" = true)))) OR (EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."user_id" = "auth"."uid"()) AND ("cm"."is_active" = true) AND (EXISTS ( SELECT 1
           FROM "public"."company_members" "other_cm"
          WHERE (("other_cm"."company_id" = "cm"."company_id") AND ("other_cm"."user_id" = "base_users"."id")))))))));



CREATE POLICY "Company admins can create invites" ON "public"."company_invites" FOR INSERT WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."company_id" = "company_invites"."company_id") AND ("cm"."user_id" = "auth"."uid"()) AND ("cm"."role" = 'admin'::"public"."user_role")))));



CREATE POLICY "Company admins can manage invites" ON "public"."company_invites" USING ((EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."company_id" = "company_invites"."company_id") AND ("cm"."user_id" = "auth"."uid"()) AND ("cm"."role" = 'admin'::"public"."user_role")))));



CREATE POLICY "Company owners can create invitations" ON "public"."company_invitations" FOR INSERT WITH CHECK ("public"."is_company_member"("company_id", 'admin'::"text"));



CREATE POLICY "Company owners can delete invitations" ON "public"."company_invitations" FOR DELETE USING ("public"."is_company_member"("company_id", 'admin'::"text"));



CREATE POLICY "Company owners can view invitations" ON "public"."company_invitations" FOR SELECT USING ("public"."is_company_member"("company_id", 'admin'::"text"));



CREATE POLICY "Enable insert for users based on user_id" ON "public"."user_sessions" FOR INSERT WITH CHECK ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Enable update for users based on email" ON "public"."user_sessions" FOR UPDATE USING ((( SELECT "auth"."uid"() AS "uid") = "user_id")) WITH CHECK ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Enable users to view their own data only" ON "public"."user_sessions" FOR SELECT TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Members can view their companies" ON "public"."companies" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."company_id" = "companies"."id") AND ("cm"."user_id" = "auth"."uid"()) AND ("cm"."is_active" = true)))));



CREATE POLICY "Users can manage their own contact info" ON "public"."user_contact_info" TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."id" = "user_contact_info"."company_member_id") AND ("cm"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can update their own base profile" ON "public"."base_users" FOR UPDATE TO "authenticated" USING (("auth"."uid"() = "id")) WITH CHECK (("auth"."uid"() = "id"));



CREATE POLICY "Users can view contact info in their companies" ON "public"."user_contact_info" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."id" = "user_contact_info"."company_member_id") AND (EXISTS ( SELECT 1
           FROM "public"."company_members" "viewer"
          WHERE (("viewer"."company_id" = "cm"."company_id") AND ("viewer"."user_id" = "auth"."uid"()))))))));



CREATE POLICY "Users can view invites sent to them" ON "public"."company_invites" FOR SELECT USING (true);



CREATE POLICY "admins can create data sources" ON "public"."data_sources" FOR INSERT TO "authenticated" WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."company_id" = "data_sources"."company_id") AND ("cm"."user_id" = "auth"."uid"()) AND ("cm"."role" = 'admin'::"public"."user_role") AND ("cm"."is_active" = true)))));



CREATE POLICY "admins can delete data sources" ON "public"."data_sources" FOR DELETE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."company_id" = "data_sources"."company_id") AND ("cm"."user_id" = "auth"."uid"()) AND ("cm"."role" = 'admin'::"public"."user_role") AND ("cm"."is_active" = true)))));



CREATE POLICY "admins can manage communication channels" ON "public"."communication_channels" TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."company_id" = "communication_channels"."company_id") AND ("cm"."user_id" = "auth"."uid"()) AND ("cm"."role" = 'admin'::"public"."user_role") AND ("cm"."is_active" = true))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."company_id" = "communication_channels"."company_id") AND ("cm"."user_id" = "auth"."uid"()) AND ("cm"."role" = 'admin'::"public"."user_role") AND ("cm"."is_active" = true)))));



CREATE POLICY "admins can manage data sources" ON "public"."data_sources" TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."company_id" = "data_sources"."company_id") AND ("cm"."user_id" = "auth"."uid"()) AND ("cm"."role" = 'admin'::"public"."user_role") AND ("cm"."is_active" = true))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."company_id" = "data_sources"."company_id") AND ("cm"."user_id" = "auth"."uid"()) AND ("cm"."role" = 'admin'::"public"."user_role") AND ("cm"."is_active" = true)))));



CREATE POLICY "admins can update data sources" ON "public"."data_sources" FOR UPDATE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."company_id" = "data_sources"."company_id") AND ("cm"."user_id" = "auth"."uid"()) AND ("cm"."role" = 'admin'::"public"."user_role") AND ("cm"."is_active" = true))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."company_id" = "cm"."company_id") AND ("cm"."user_id" = "auth"."uid"()) AND ("cm"."role" = 'admin'::"public"."user_role") AND ("cm"."is_active" = true)))));



CREATE POLICY "allow company admins and experts to create documents" ON "public"."documents" FOR INSERT TO "authenticated" WITH CHECK ((EXISTS ( SELECT 1
   FROM ("public"."company_members" "cm"
     JOIN "public"."files" "f" ON (("f"."company_id" = "cm"."company_id")))
  WHERE (("cm"."user_id" = "auth"."uid"()) AND ("f"."id" = "documents"."file_id") AND ("cm"."role" = ANY (ARRAY['admin'::"public"."user_role", 'expert'::"public"."user_role"]))))));



CREATE POLICY "allow company admins and experts to create files" ON "public"."files" FOR INSERT TO "authenticated" WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."user_id" = "auth"."uid"()) AND ("cm"."company_id" = "files"."company_id") AND ("cm"."role" = ANY (ARRAY['admin'::"public"."user_role", 'expert'::"public"."user_role"]))))));



CREATE POLICY "allow company admins and experts to delete documents" ON "public"."documents" FOR DELETE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM ("public"."company_members" "cm"
     JOIN "public"."files" "f" ON (("f"."company_id" = "cm"."company_id")))
  WHERE (("cm"."user_id" = "auth"."uid"()) AND ("f"."id" = "documents"."file_id") AND ("cm"."role" = ANY (ARRAY['admin'::"public"."user_role", 'expert'::"public"."user_role"]))))));



CREATE POLICY "allow company admins and experts to delete files" ON "public"."files" FOR DELETE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."user_id" = "auth"."uid"()) AND ("cm"."company_id" = "files"."company_id") AND ("cm"."role" = ANY (ARRAY['admin'::"public"."user_role", 'expert'::"public"."user_role"]))))));



CREATE POLICY "allow company admins and experts to update documents" ON "public"."documents" FOR UPDATE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM ("public"."company_members" "cm"
     JOIN "public"."files" "f" ON (("f"."company_id" = "cm"."company_id")))
  WHERE (("cm"."user_id" = "auth"."uid"()) AND ("f"."id" = "documents"."file_id") AND ("cm"."role" = ANY (ARRAY['admin'::"public"."user_role", 'expert'::"public"."user_role"])))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM ("public"."company_members" "cm"
     JOIN "public"."files" "f" ON (("f"."company_id" = "cm"."company_id")))
  WHERE (("cm"."user_id" = "auth"."uid"()) AND ("f"."id" = "documents"."file_id") AND ("cm"."role" = ANY (ARRAY['admin'::"public"."user_role", 'expert'::"public"."user_role"]))))));



CREATE POLICY "allow company admins and experts to update files" ON "public"."files" FOR UPDATE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."user_id" = "auth"."uid"()) AND ("cm"."company_id" = "files"."company_id") AND ("cm"."role" = ANY (ARRAY['admin'::"public"."user_role", 'expert'::"public"."user_role"])))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."user_id" = "auth"."uid"()) AND ("cm"."company_id" = "files"."company_id") AND ("cm"."role" = ANY (ARRAY['admin'::"public"."user_role", 'expert'::"public"."user_role"]))))));



CREATE POLICY "allow company members to read documents" ON "public"."documents" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM ("public"."company_members" "cm"
     JOIN "public"."files" "f" ON (("f"."company_id" = "cm"."company_id")))
  WHERE (("cm"."user_id" = "auth"."uid"()) AND ("f"."id" = "documents"."file_id")))));



CREATE POLICY "allow company members to read files" ON "public"."files" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."user_id" = "auth"."uid"()) AND ("cm"."company_id" = "files"."company_id")))));



ALTER TABLE "public"."base_users" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."billing_records" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."chat_messages" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."communication_channels" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."companies" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."company_invitations" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."company_invites" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."company_personalization" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."data_sources" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."documents" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."files" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."map_question_to_responses" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "members can view communication channels" ON "public"."communication_channels" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."company_id" = "communication_channels"."company_id") AND ("cm"."user_id" = "auth"."uid"()) AND ("cm"."is_active" = true)))));



CREATE POLICY "members can view data sources" ON "public"."data_sources" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."company_members" "cm"
  WHERE (("cm"."company_id" = "data_sources"."company_id") AND ("cm"."user_id" = "auth"."uid"()) AND ("cm"."is_active" = true)))));



ALTER TABLE "public"."oauth_tokens" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."permissions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."team_members" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."team_permissions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."teams" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."totm_inbox_questions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."totm_inbox_responses" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."transcription_usage_records" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."upsertion_records" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_contact_info" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_sessions" ENABLE ROW LEVEL SECURITY;




ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";








GRANT USAGE ON SCHEMA "private" TO "service_role";



GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_in"("cstring", "oid", integer) TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_in"("cstring", "oid", integer) TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_in"("cstring", "oid", integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_in"("cstring", "oid", integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_out"("public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_out"("public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_out"("public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_out"("public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_recv"("internal", "oid", integer) TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_recv"("internal", "oid", integer) TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_recv"("internal", "oid", integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_recv"("internal", "oid", integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_send"("public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_send"("public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_send"("public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_send"("public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_typmod_in"("cstring"[]) TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_typmod_in"("cstring"[]) TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_typmod_in"("cstring"[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_typmod_in"("cstring"[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_in"("cstring", "oid", integer) TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_in"("cstring", "oid", integer) TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_in"("cstring", "oid", integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_in"("cstring", "oid", integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_out"("public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_out"("public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_out"("public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_out"("public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_recv"("internal", "oid", integer) TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_recv"("internal", "oid", integer) TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_recv"("internal", "oid", integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_recv"("internal", "oid", integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_send"("public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_send"("public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_send"("public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_send"("public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_typmod_in"("cstring"[]) TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_typmod_in"("cstring"[]) TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_typmod_in"("cstring"[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_typmod_in"("cstring"[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_in"("cstring", "oid", integer) TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_in"("cstring", "oid", integer) TO "anon";
GRANT ALL ON FUNCTION "public"."vector_in"("cstring", "oid", integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_in"("cstring", "oid", integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_out"("public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_out"("public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_out"("public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_out"("public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_recv"("internal", "oid", integer) TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_recv"("internal", "oid", integer) TO "anon";
GRANT ALL ON FUNCTION "public"."vector_recv"("internal", "oid", integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_recv"("internal", "oid", integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_send"("public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_send"("public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_send"("public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_send"("public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_typmod_in"("cstring"[]) TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_typmod_in"("cstring"[]) TO "anon";
GRANT ALL ON FUNCTION "public"."vector_typmod_in"("cstring"[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_typmod_in"("cstring"[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."array_to_halfvec"(real[], integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."array_to_halfvec"(real[], integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."array_to_halfvec"(real[], integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."array_to_halfvec"(real[], integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."array_to_vector"(real[], integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."array_to_vector"(real[], integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."array_to_vector"(real[], integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."array_to_vector"(real[], integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."array_to_halfvec"(double precision[], integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."array_to_halfvec"(double precision[], integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."array_to_halfvec"(double precision[], integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."array_to_halfvec"(double precision[], integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."array_to_vector"(double precision[], integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."array_to_vector"(double precision[], integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."array_to_vector"(double precision[], integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."array_to_vector"(double precision[], integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."array_to_halfvec"(integer[], integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."array_to_halfvec"(integer[], integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."array_to_halfvec"(integer[], integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."array_to_halfvec"(integer[], integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."array_to_vector"(integer[], integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."array_to_vector"(integer[], integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."array_to_vector"(integer[], integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."array_to_vector"(integer[], integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."array_to_halfvec"(numeric[], integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."array_to_halfvec"(numeric[], integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."array_to_halfvec"(numeric[], integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."array_to_halfvec"(numeric[], integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."array_to_vector"(numeric[], integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."array_to_vector"(numeric[], integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."array_to_vector"(numeric[], integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."array_to_vector"(numeric[], integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_to_float4"("public"."halfvec", integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_to_float4"("public"."halfvec", integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_to_float4"("public"."halfvec", integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_to_float4"("public"."halfvec", integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec"("public"."halfvec", integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec"("public"."halfvec", integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec"("public"."halfvec", integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec"("public"."halfvec", integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_to_sparsevec"("public"."halfvec", integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_to_sparsevec"("public"."halfvec", integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_to_sparsevec"("public"."halfvec", integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_to_sparsevec"("public"."halfvec", integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_to_vector"("public"."halfvec", integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_to_vector"("public"."halfvec", integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_to_vector"("public"."halfvec", integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_to_vector"("public"."halfvec", integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_to_halfvec"("public"."sparsevec", integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_to_halfvec"("public"."sparsevec", integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_to_halfvec"("public"."sparsevec", integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_to_halfvec"("public"."sparsevec", integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec"("public"."sparsevec", integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec"("public"."sparsevec", integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec"("public"."sparsevec", integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec"("public"."sparsevec", integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_to_vector"("public"."sparsevec", integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_to_vector"("public"."sparsevec", integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_to_vector"("public"."sparsevec", integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_to_vector"("public"."sparsevec", integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_to_float4"("public"."vector", integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_to_float4"("public"."vector", integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."vector_to_float4"("public"."vector", integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_to_float4"("public"."vector", integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_to_halfvec"("public"."vector", integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_to_halfvec"("public"."vector", integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."vector_to_halfvec"("public"."vector", integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_to_halfvec"("public"."vector", integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_to_sparsevec"("public"."vector", integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_to_sparsevec"("public"."vector", integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."vector_to_sparsevec"("public"."vector", integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_to_sparsevec"("public"."vector", integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."vector"("public"."vector", integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."vector"("public"."vector", integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."vector"("public"."vector", integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector"("public"."vector", integer, boolean) TO "service_role";




































































































































































































































































































































GRANT ALL ON FUNCTION "public"."accept_company_invitation"("token" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."accept_company_invitation"("token" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."accept_company_invitation"("token" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."accept_company_invite"("invite_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."accept_company_invite"("invite_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."accept_company_invite"("invite_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."add_creator_as_admin"() TO "anon";
GRANT ALL ON FUNCTION "public"."add_creator_as_admin"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."add_creator_as_admin"() TO "service_role";



GRANT ALL ON FUNCTION "public"."add_question"("question_text" "text", "assigned_to" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."add_question"("question_text" "text", "assigned_to" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."add_question"("question_text" "text", "assigned_to" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."add_question"("question_text" "text", "user_id" "uuid", "assigned_to" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."add_question"("question_text" "text", "user_id" "uuid", "assigned_to" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."add_question"("question_text" "text", "user_id" "uuid", "assigned_to" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."add_question_providing_user_id"("question_text" "text", "user_id" "uuid", "assigned_to" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."add_question_providing_user_id"("question_text" "text", "user_id" "uuid", "assigned_to" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."add_question_providing_user_id"("question_text" "text", "user_id" "uuid", "assigned_to" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."add_response"("question_id" "uuid", "response_text" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."add_response"("question_id" "uuid", "response_text" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."add_response"("question_id" "uuid", "response_text" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."add_user_to_team"("team_uuid" "uuid", "user_uuid" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."add_user_to_team"("team_uuid" "uuid", "user_uuid" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."add_user_to_team"("team_uuid" "uuid", "user_uuid" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."binary_quantize"("public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."binary_quantize"("public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."binary_quantize"("public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."binary_quantize"("public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."binary_quantize"("public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."binary_quantize"("public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."binary_quantize"("public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."binary_quantize"("public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."check_and_fix_user_profile"() TO "anon";
GRANT ALL ON FUNCTION "public"."check_and_fix_user_profile"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."check_and_fix_user_profile"() TO "service_role";



GRANT ALL ON FUNCTION "public"."cosine_distance"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."cosine_distance"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."cosine_distance"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."cosine_distance"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."cosine_distance"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."cosine_distance"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."cosine_distance"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."cosine_distance"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."cosine_distance"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."cosine_distance"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."cosine_distance"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."cosine_distance"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."create_data_source"("company_uuid" "uuid", "platform_type" "public"."platform_type", "display_name" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."create_data_source"("company_uuid" "uuid", "platform_type" "public"."platform_type", "display_name" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_data_source"("company_uuid" "uuid", "platform_type" "public"."platform_type", "display_name" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."create_data_source"("company_uuid" "uuid", "platform_type" "public"."platform_type", "display_name" "text", "scope" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."create_data_source"("company_uuid" "uuid", "platform_type" "public"."platform_type", "display_name" "text", "scope" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_data_source"("company_uuid" "uuid", "platform_type" "public"."platform_type", "display_name" "text", "scope" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."create_secret"("secret" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."create_secret"("secret" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_secret"("secret" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."create_team"("company_uuid" "uuid", "team_name" "text", "team_description" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."create_team"("company_uuid" "uuid", "team_name" "text", "team_description" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_team"("company_uuid" "uuid", "team_name" "text", "team_description" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."current_user_email"() TO "anon";
GRANT ALL ON FUNCTION "public"."current_user_email"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."current_user_email"() TO "service_role";



GRANT ALL ON FUNCTION "public"."gen_test_uuid"("seed" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."gen_test_uuid"("seed" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gen_test_uuid"("seed" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."generate_file_metadata_text"("file_name" "text", "mime_type" "text", "summary" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."generate_file_metadata_text"("file_name" "text", "mime_type" "text", "summary" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."generate_file_metadata_text"("file_name" "text", "mime_type" "text", "summary" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_invitation_details"("token" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."get_invitation_details"("token" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_invitation_details"("token" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_vault_secret"("secret_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_vault_secret"("secret_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_vault_secret"("secret_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_vault_secrets"("secret_ids" "uuid"[]) TO "anon";
GRANT ALL ON FUNCTION "public"."get_vault_secrets"("secret_ids" "uuid"[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_vault_secrets"("secret_ids" "uuid"[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."get_whatsapp_data"("from_number" "text", "to_number" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."get_whatsapp_data"("from_number" "text", "to_number" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_whatsapp_data"("from_number" "text", "to_number" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_accum"(double precision[], "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_accum"(double precision[], "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_accum"(double precision[], "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_accum"(double precision[], "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_add"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_add"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_add"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_add"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_avg"(double precision[]) TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_avg"(double precision[]) TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_avg"(double precision[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_avg"(double precision[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_cmp"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_cmp"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_cmp"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_cmp"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_combine"(double precision[], double precision[]) TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_combine"(double precision[], double precision[]) TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_combine"(double precision[], double precision[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_combine"(double precision[], double precision[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_concat"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_concat"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_concat"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_concat"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_eq"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_eq"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_eq"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_eq"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_ge"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_ge"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_ge"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_ge"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_gt"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_gt"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_gt"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_gt"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_l2_squared_distance"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_l2_squared_distance"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_l2_squared_distance"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_l2_squared_distance"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_le"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_le"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_le"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_le"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_lt"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_lt"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_lt"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_lt"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_mul"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_mul"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_mul"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_mul"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_ne"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_ne"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_ne"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_ne"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_negative_inner_product"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_negative_inner_product"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_negative_inner_product"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_negative_inner_product"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_spherical_distance"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_spherical_distance"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_spherical_distance"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_spherical_distance"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_sub"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_sub"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_sub"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_sub"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."hamming_distance"(bit, bit) TO "postgres";
GRANT ALL ON FUNCTION "public"."hamming_distance"(bit, bit) TO "anon";
GRANT ALL ON FUNCTION "public"."hamming_distance"(bit, bit) TO "authenticated";
GRANT ALL ON FUNCTION "public"."hamming_distance"(bit, bit) TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_auth_user_signin"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_auth_user_signin"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_auth_user_signin"() TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_company_personalization_upsert"("p_company_id" "uuid", "p_branding_logo" "text", "p_small_logo" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."handle_company_personalization_upsert"("p_company_id" "uuid", "p_branding_logo" "text", "p_small_logo" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_company_personalization_upsert"("p_company_id" "uuid", "p_branding_logo" "text", "p_small_logo" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_data_source_user_profile"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_data_source_user_profile"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_data_source_user_profile"() TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "service_role";



GRANT ALL ON FUNCTION "public"."hnsw_bit_support"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."hnsw_bit_support"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."hnsw_bit_support"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."hnsw_bit_support"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."hnsw_halfvec_support"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."hnsw_halfvec_support"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."hnsw_halfvec_support"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."hnsw_halfvec_support"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."hnsw_sparsevec_support"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."hnsw_sparsevec_support"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."hnsw_sparsevec_support"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."hnsw_sparsevec_support"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."hnswhandler"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."hnswhandler"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."hnswhandler"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."hnswhandler"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."inner_product"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."inner_product"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."inner_product"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."inner_product"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."inner_product"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."inner_product"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."inner_product"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."inner_product"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."inner_product"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."inner_product"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."inner_product"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."inner_product"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_company_member"("company_id" "uuid", "role_name" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."is_company_member"("company_id" "uuid", "role_name" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_company_member"("company_id" "uuid", "role_name" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_current_user"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_current_user"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_current_user"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."ivfflat_bit_support"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."ivfflat_bit_support"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."ivfflat_bit_support"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ivfflat_bit_support"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."ivfflat_halfvec_support"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."ivfflat_halfvec_support"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."ivfflat_halfvec_support"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ivfflat_halfvec_support"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."ivfflathandler"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."ivfflathandler"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."ivfflathandler"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ivfflathandler"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."jaccard_distance"(bit, bit) TO "postgres";
GRANT ALL ON FUNCTION "public"."jaccard_distance"(bit, bit) TO "anon";
GRANT ALL ON FUNCTION "public"."jaccard_distance"(bit, bit) TO "authenticated";
GRANT ALL ON FUNCTION "public"."jaccard_distance"(bit, bit) TO "service_role";



GRANT ALL ON FUNCTION "public"."l1_distance"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."l1_distance"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."l1_distance"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."l1_distance"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."l1_distance"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."l1_distance"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."l1_distance"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."l1_distance"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."l1_distance"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."l1_distance"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."l1_distance"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."l1_distance"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."l2_distance"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."l2_distance"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."l2_distance"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."l2_distance"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."l2_distance"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."l2_distance"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."l2_distance"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."l2_distance"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."l2_distance"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."l2_distance"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."l2_distance"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."l2_distance"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."l2_norm"("public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."l2_norm"("public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."l2_norm"("public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."l2_norm"("public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."l2_norm"("public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."l2_norm"("public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."l2_norm"("public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."l2_norm"("public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."l2_normalize"("public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."l2_normalize"("public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."l2_normalize"("public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."l2_normalize"("public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."l2_normalize"("public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."l2_normalize"("public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."l2_normalize"("public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."l2_normalize"("public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."l2_normalize"("public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."l2_normalize"("public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."l2_normalize"("public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."l2_normalize"("public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."match_documents"("user_id" "uuid", "query_embedding" "public"."vector", "match_count" integer, "filter" "jsonb", "similarity_threshold" double precision) TO "anon";
GRANT ALL ON FUNCTION "public"."match_documents"("user_id" "uuid", "query_embedding" "public"."vector", "match_count" integer, "filter" "jsonb", "similarity_threshold" double precision) TO "authenticated";
GRANT ALL ON FUNCTION "public"."match_documents"("user_id" "uuid", "query_embedding" "public"."vector", "match_count" integer, "filter" "jsonb", "similarity_threshold" double precision) TO "service_role";



GRANT ALL ON FUNCTION "public"."match_documents_public"("query_embedding" "public"."vector", "match_count" integer, "similarity_threshold" double precision, "filter" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."match_documents_public"("query_embedding" "public"."vector", "match_count" integer, "similarity_threshold" double precision, "filter" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."match_documents_public"("query_embedding" "public"."vector", "match_count" integer, "similarity_threshold" double precision, "filter" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."match_documents_with_permissions"("user_id" "text", "query_embedding" "public"."vector", "match_count" integer, "filter" "jsonb", "similarity_threshold" double precision) TO "anon";
GRANT ALL ON FUNCTION "public"."match_documents_with_permissions"("user_id" "text", "query_embedding" "public"."vector", "match_count" integer, "filter" "jsonb", "similarity_threshold" double precision) TO "authenticated";
GRANT ALL ON FUNCTION "public"."match_documents_with_permissions"("user_id" "text", "query_embedding" "public"."vector", "match_count" integer, "filter" "jsonb", "similarity_threshold" double precision) TO "service_role";



GRANT ALL ON FUNCTION "public"."match_documents_with_permissions"("query_embedding" "public"."vector", "user_email" "text", "company_id" "uuid", "match_count" integer, "similarity_threshold" double precision, "filter" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."match_documents_with_permissions"("query_embedding" "public"."vector", "user_email" "text", "company_id" "uuid", "match_count" integer, "similarity_threshold" double precision, "filter" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."match_documents_with_permissions"("query_embedding" "public"."vector", "user_email" "text", "company_id" "uuid", "match_count" integer, "similarity_threshold" double precision, "filter" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."match_documents_with_user_permissions"("user_id" "uuid", "query_embedding" "public"."vector", "match_count" integer, "similarity_threshold" double precision, "filter" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."match_documents_with_user_permissions"("user_id" "uuid", "query_embedding" "public"."vector", "match_count" integer, "similarity_threshold" double precision, "filter" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."match_documents_with_user_permissions"("user_id" "uuid", "query_embedding" "public"."vector", "match_count" integer, "similarity_threshold" double precision, "filter" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."populate_missing_base_users"() TO "anon";
GRANT ALL ON FUNCTION "public"."populate_missing_base_users"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."populate_missing_base_users"() TO "service_role";



GRANT ALL ON FUNCTION "public"."refresh_user_permissions"("member_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."refresh_user_permissions"("member_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."refresh_user_permissions"("member_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."search_documents_by_company"("query_embedding" "public"."vector", "company_id" "uuid", "match_count" integer, "similarity_threshold" double precision, "filter" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."search_documents_by_company"("query_embedding" "public"."vector", "company_id" "uuid", "match_count" integer, "similarity_threshold" double precision, "filter" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."search_documents_by_company"("query_embedding" "public"."vector", "company_id" "uuid", "match_count" integer, "similarity_threshold" double precision, "filter" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."set_companies_created_by"() TO "anon";
GRANT ALL ON FUNCTION "public"."set_companies_created_by"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_companies_created_by"() TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_cmp"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_cmp"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_cmp"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_cmp"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_eq"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_eq"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_eq"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_eq"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_ge"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_ge"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_ge"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_ge"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_gt"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_gt"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_gt"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_gt"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_l2_squared_distance"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_l2_squared_distance"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_l2_squared_distance"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_l2_squared_distance"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_le"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_le"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_le"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_le"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_lt"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_lt"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_lt"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_lt"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_ne"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_ne"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_ne"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_ne"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_negative_inner_product"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_negative_inner_product"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_negative_inner_product"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_negative_inner_product"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."subvector"("public"."halfvec", integer, integer) TO "postgres";
GRANT ALL ON FUNCTION "public"."subvector"("public"."halfvec", integer, integer) TO "anon";
GRANT ALL ON FUNCTION "public"."subvector"("public"."halfvec", integer, integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."subvector"("public"."halfvec", integer, integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."subvector"("public"."vector", integer, integer) TO "postgres";
GRANT ALL ON FUNCTION "public"."subvector"("public"."vector", integer, integer) TO "anon";
GRANT ALL ON FUNCTION "public"."subvector"("public"."vector", integer, integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."subvector"("public"."vector", integer, integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."sync_current_user_sharepoint_profiles"() TO "anon";
GRANT ALL ON FUNCTION "public"."sync_current_user_sharepoint_profiles"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."sync_current_user_sharepoint_profiles"() TO "service_role";



GRANT ALL ON FUNCTION "public"."sync_oauth_metadata"() TO "anon";
GRANT ALL ON FUNCTION "public"."sync_oauth_metadata"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."sync_oauth_metadata"() TO "service_role";



GRANT ALL ON FUNCTION "public"."sync_user_sharepoint_profiles"("user_uuid" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."sync_user_sharepoint_profiles"("user_uuid" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sync_user_sharepoint_profiles"("user_uuid" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."test_user_creation"("test_email" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."test_user_creation"("test_email" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."test_user_creation"("test_email" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."update_company_users"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_company_users"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_company_users"() TO "service_role";



REVOKE ALL ON FUNCTION "public"."update_contact_info"("p_user_id" "uuid", "p_company_id" "uuid", "p_email" "text", "p_phone" "text") FROM PUBLIC;
GRANT ALL ON FUNCTION "public"."update_contact_info"("p_user_id" "uuid", "p_company_id" "uuid", "p_email" "text", "p_phone" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."update_contact_info"("p_user_id" "uuid", "p_company_id" "uuid", "p_email" "text", "p_phone" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_contact_info"("p_user_id" "uuid", "p_company_id" "uuid", "p_email" "text", "p_phone" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."update_files_table"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_files_table"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_files_table"() TO "service_role";



GRANT ALL ON FUNCTION "public"."upsert_document"("p_id" "uuid", "p_content" "text", "p_metadata" "jsonb", "p_file_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."upsert_document"("p_id" "uuid", "p_content" "text", "p_metadata" "jsonb", "p_file_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."upsert_document"("p_id" "uuid", "p_content" "text", "p_metadata" "jsonb", "p_file_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_accum"(double precision[], "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_accum"(double precision[], "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_accum"(double precision[], "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_accum"(double precision[], "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_add"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_add"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_add"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_add"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_avg"(double precision[]) TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_avg"(double precision[]) TO "anon";
GRANT ALL ON FUNCTION "public"."vector_avg"(double precision[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_avg"(double precision[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_cmp"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_cmp"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_cmp"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_cmp"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_combine"(double precision[], double precision[]) TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_combine"(double precision[], double precision[]) TO "anon";
GRANT ALL ON FUNCTION "public"."vector_combine"(double precision[], double precision[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_combine"(double precision[], double precision[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_concat"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_concat"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_concat"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_concat"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_dims"("public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_dims"("public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_dims"("public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_dims"("public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_dims"("public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_dims"("public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_dims"("public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_dims"("public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_eq"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_eq"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_eq"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_eq"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_ge"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_ge"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_ge"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_ge"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_gt"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_gt"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_gt"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_gt"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_l2_squared_distance"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_l2_squared_distance"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_l2_squared_distance"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_l2_squared_distance"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_le"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_le"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_le"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_le"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_lt"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_lt"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_lt"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_lt"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_mul"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_mul"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_mul"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_mul"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_ne"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_ne"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_ne"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_ne"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_negative_inner_product"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_negative_inner_product"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_negative_inner_product"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_negative_inner_product"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_norm"("public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_norm"("public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_norm"("public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_norm"("public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_spherical_distance"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_spherical_distance"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_spherical_distance"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_spherical_distance"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_sub"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_sub"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_sub"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_sub"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."avg"("public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."avg"("public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."avg"("public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."avg"("public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."avg"("public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."avg"("public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."avg"("public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."avg"("public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."sum"("public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."sum"("public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."sum"("public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sum"("public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."sum"("public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."sum"("public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."sum"("public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sum"("public"."vector") TO "service_role";



























GRANT ALL ON TABLE "public"."base_users" TO "anon";
GRANT ALL ON TABLE "public"."base_users" TO "authenticated";
GRANT ALL ON TABLE "public"."base_users" TO "service_role";



GRANT ALL ON TABLE "public"."billing_records" TO "anon";
GRANT ALL ON TABLE "public"."billing_records" TO "authenticated";
GRANT ALL ON TABLE "public"."billing_records" TO "service_role";



GRANT ALL ON TABLE "public"."chat_messages" TO "anon";
GRANT ALL ON TABLE "public"."chat_messages" TO "authenticated";
GRANT ALL ON TABLE "public"."chat_messages" TO "service_role";



GRANT ALL ON TABLE "public"."communication_channels" TO "anon";
GRANT ALL ON TABLE "public"."communication_channels" TO "authenticated";
GRANT ALL ON TABLE "public"."communication_channels" TO "service_role";



GRANT ALL ON TABLE "public"."companies" TO "anon";
GRANT ALL ON TABLE "public"."companies" TO "authenticated";
GRANT ALL ON TABLE "public"."companies" TO "service_role";



GRANT ALL ON TABLE "public"."company_invitations" TO "anon";
GRANT ALL ON TABLE "public"."company_invitations" TO "authenticated";
GRANT ALL ON TABLE "public"."company_invitations" TO "service_role";



GRANT ALL ON TABLE "public"."company_invites" TO "anon";
GRANT ALL ON TABLE "public"."company_invites" TO "authenticated";
GRANT ALL ON TABLE "public"."company_invites" TO "service_role";



GRANT ALL ON TABLE "public"."company_members" TO "anon";
GRANT ALL ON TABLE "public"."company_members" TO "authenticated";
GRANT ALL ON TABLE "public"."company_members" TO "service_role";



GRANT ALL ON TABLE "public"."company_personalization" TO "anon";
GRANT ALL ON TABLE "public"."company_personalization" TO "authenticated";
GRANT ALL ON TABLE "public"."company_personalization" TO "service_role";



GRANT ALL ON TABLE "public"."company_user_profiles" TO "anon";
GRANT ALL ON TABLE "public"."company_user_profiles" TO "authenticated";
GRANT ALL ON TABLE "public"."company_user_profiles" TO "service_role";



GRANT ALL ON TABLE "public"."data_sources" TO "anon";
GRANT ALL ON TABLE "public"."data_sources" TO "authenticated";
GRANT ALL ON TABLE "public"."data_sources" TO "service_role";



GRANT ALL ON TABLE "public"."documents" TO "anon";
GRANT ALL ON TABLE "public"."documents" TO "authenticated";
GRANT ALL ON TABLE "public"."documents" TO "service_role";



GRANT ALL ON TABLE "public"."files" TO "anon";
GRANT ALL ON TABLE "public"."files" TO "authenticated";
GRANT ALL ON TABLE "public"."files" TO "service_role";



GRANT ALL ON TABLE "public"."map_question_to_responses" TO "anon";
GRANT ALL ON TABLE "public"."map_question_to_responses" TO "authenticated";
GRANT ALL ON TABLE "public"."map_question_to_responses" TO "service_role";



GRANT ALL ON TABLE "public"."oauth_tokens" TO "anon";
GRANT ALL ON TABLE "public"."oauth_tokens" TO "authenticated";
GRANT ALL ON TABLE "public"."oauth_tokens" TO "service_role";



GRANT ALL ON TABLE "public"."permissions" TO "anon";
GRANT ALL ON TABLE "public"."permissions" TO "authenticated";
GRANT ALL ON TABLE "public"."permissions" TO "service_role";



GRANT ALL ON TABLE "public"."team_members" TO "anon";
GRANT ALL ON TABLE "public"."team_members" TO "authenticated";
GRANT ALL ON TABLE "public"."team_members" TO "service_role";



GRANT ALL ON TABLE "public"."team_permissions" TO "anon";
GRANT ALL ON TABLE "public"."team_permissions" TO "authenticated";
GRANT ALL ON TABLE "public"."team_permissions" TO "service_role";



GRANT ALL ON TABLE "public"."teams" TO "anon";
GRANT ALL ON TABLE "public"."teams" TO "authenticated";
GRANT ALL ON TABLE "public"."teams" TO "service_role";



GRANT ALL ON TABLE "public"."totm_inbox_questions" TO "anon";
GRANT ALL ON TABLE "public"."totm_inbox_questions" TO "authenticated";
GRANT ALL ON TABLE "public"."totm_inbox_questions" TO "service_role";



GRANT ALL ON TABLE "public"."totm_inbox_responses" TO "anon";
GRANT ALL ON TABLE "public"."totm_inbox_responses" TO "authenticated";
GRANT ALL ON TABLE "public"."totm_inbox_responses" TO "service_role";



GRANT ALL ON TABLE "public"."transcription_usage_records" TO "anon";
GRANT ALL ON TABLE "public"."transcription_usage_records" TO "authenticated";
GRANT ALL ON TABLE "public"."transcription_usage_records" TO "service_role";



GRANT ALL ON TABLE "public"."upsertion_records" TO "anon";
GRANT ALL ON TABLE "public"."upsertion_records" TO "authenticated";
GRANT ALL ON TABLE "public"."upsertion_records" TO "service_role";



GRANT ALL ON TABLE "public"."user_contact_info" TO "anon";
GRANT ALL ON TABLE "public"."user_contact_info" TO "authenticated";
GRANT ALL ON TABLE "public"."user_contact_info" TO "service_role";



GRANT ALL ON TABLE "public"."user_platform_profiles" TO "anon";
GRANT ALL ON TABLE "public"."user_platform_profiles" TO "authenticated";
GRANT ALL ON TABLE "public"."user_platform_profiles" TO "service_role";



GRANT ALL ON TABLE "public"."user_sessions" TO "anon";
GRANT ALL ON TABLE "public"."user_sessions" TO "authenticated";
GRANT ALL ON TABLE "public"."user_sessions" TO "service_role";



GRANT ALL ON TABLE "public"."view_totm_inbox_questions_responses" TO "anon";
GRANT ALL ON TABLE "public"."view_totm_inbox_questions_responses" TO "authenticated";
GRANT ALL ON TABLE "public"."view_totm_inbox_questions_responses" TO "service_role";



ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






























RESET ALL;
