-- Update the update_contact_info function to save phone numbers to base_users table
-- instead of user_platform_profiles table

CREATE OR REPLACE FUNCTION "public"."update_contact_info"("p_user_id" "uuid", "p_company_id" "uuid", "p_email" "text", "p_phone" "text") RETURNS "json"
    LANGUAGE "plpgsql"
    AS $_$
declare
  v_result json;
  v_member_exists boolean;
  v_is_admin boolean;
  v_requesting_user_id uuid;
  v_member_id uuid;
begin
  -- Input validation
  if p_user_id is null or p_company_id is null or p_email is null then
    return json_build_object(
      'success', false,
      'error', 'Missing required fields'
    );
  end if;

  -- Validate email format
  if not p_email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' then
    return json_build_object(
      'success', false,
      'error', 'Invalid email format'
    );
  end if;

  -- Validate phone format if provided (11-15 digits, no + symbol)
  if p_phone is not null and not p_phone ~ '^[1-9][0-9]{10,14}$' then
    return json_build_object(
      'success', false,
      'error', 'Phone number must be 11-15 digits starting with country code (e.g. ************)'
    );
  end if;

  -- Get requesting user ID
  v_requesting_user_id := auth.uid();
  
  if v_requesting_user_id is null then
    return json_build_object(
      'success', false,
      'error', 'User not authenticated'
    );
  end if;

  -- Check if the user is a member of the company and get member_id
  select cm.id into v_member_id
  from company_members cm
  where cm.user_id = p_user_id
  and cm.company_id = p_company_id
  and cm.is_active = true;

  if v_member_id is null then
    return json_build_object(
      'success', false,
      'error', 'User is not a member of this company'
    );
  end if;
  
  -- Check if requesting user is admin or updating their own info
  select role = 'admin' into v_is_admin
  from company_user_profiles
  where user_id = v_requesting_user_id
  and company_id = p_company_id;
  
  if not (v_is_admin or v_requesting_user_id = p_user_id) then
    return json_build_object(
      'success', false,
      'error', 'Unauthorized to update user contact information'
    );
  end if;

  -- Start transaction
  begin
    -- Update base_users table with email and phone
    update base_users 
    set 
      email = p_email,
      phone_number = case when p_phone is not null then p_phone::bigint else null end,
      updated_at = now()
    where id = p_user_id;

    -- Also update user_platform_profiles for email (for compatibility)
    insert into user_platform_profiles (
      company_member_id,
      platform_type,
      platform_user_id,
      is_primary,
      metadata
    )
    values (
      v_member_id,
      'webchat',
      p_email,
      true,
      jsonb_build_object('type', 'email')
    )
    on conflict (company_member_id, platform_type, platform_user_id)
    do update set
      updated_at = now();

    return json_build_object(
      'success', true,
      'data', json_build_object(
        'user_id', p_user_id,
        'email', p_email,
        'phone', p_phone
      )
    );
  exception when others then
    -- Rollback will happen automatically
    return json_build_object(
      'success', false,
      'error', SQLERRM
    );
  end;
end;
$_$;
