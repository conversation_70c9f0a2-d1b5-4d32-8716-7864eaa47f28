-- Migration: add_company_products
-- Description: Adds tables for managing company products and their features
-- Affected tables: products, company_products
-- Security: RLS enabled with appropriate policies

-- Create products table
create table public.products (
    id uuid primary key default gen_random_uuid(),
    name text not null,
    description text,
    icon text,
    is_coming_soon boolean default false,
    category text default 'Other',
    features jsonb default '[]'::jsonb,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create company_products junction table
create table public.company_products (
    id uuid primary key default gen_random_uuid(),
    company_id uuid references public.companies(id) on delete cascade not null,
    product_id uuid references public.products(id) on delete cascade not null,
    is_enabled boolean default false,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null,
    unique(company_id, product_id)
);

-- Enable RLS
alter table public.products enable row level security;
alter table public.company_products enable row level security;

-- Create policies for products table
-- Allow all authenticated users to read products
create policy "Allow authenticated users to read products"
    on public.products for select
    to authenticated
    using (true);

-- Create policies for company_products table
-- Allow users to read company_products for their companies
create policy "Allow users to read company_products for their companies"
    on public.company_products for select
    to authenticated
    using (
        company_id in (
            select company_id 
            from public.company_members 
            where user_id = auth.uid()
        )
    );

-- Allow users to insert company_products for their companies if they are admin
create policy "Allow admins to insert company_products for their companies"
    on public.company_products for insert
    to authenticated
    with check (
        company_id in (
            select company_id 
            from public.company_members 
            where user_id = auth.uid() 
            and role = 'admin'
        )
    );

-- Allow users to update company_products for their companies if they are admin
create policy "Allow admins to update company_products for their companies"
    on public.company_products for update
    to authenticated
    using (
        company_id in (
            select company_id 
            from public.company_members 
            where user_id = auth.uid() 
            and role = 'admin'
        )
    );

-- Insert default products with categories and features
insert into public.products (name, description, icon, is_coming_soon, category, features) values
    -- Document Storage & Collaboration
    ('SharePoint Integration', 'Connect and search your SharePoint documents', 'sharepoint', false, 'Document Storage', '["Advanced document search", "Version history tracking", "Collaborative editing", "Permission management"]'::jsonb),
    ('Google Workspace', 'Connect and search your Google Drive documents', 'google', false, 'Document Storage', '["Google Docs integration", "Shared drives access", "Real-time collaboration", "Cloud storage management"]'::jsonb),
    ('OneDrive Connection', 'Access and search your Microsoft OneDrive files', 'onedrive', false, 'Document Storage', '["Personal file storage", "Microsoft 365 integration", "Cross-platform sync", "File sharing capabilities"]'::jsonb),
    ('Dropbox Integration', 'Connect and search your Dropbox files and folders', 'dropbox', false, 'Document Storage', '["Secure file sharing", "Document organization", "Version history", "Access controls"]'::jsonb),
    
    -- Communication Platforms
    ('Slack Integration', 'Connect and search your Slack messages and files', 'slack', false, 'Communication', '["Message search", "Channel integration", "File indexing", "Notification management"]'::jsonb),
    ('Microsoft Teams', 'Connect and search your Teams messages and documents', 'teams', false, 'Communication', '["Chat history search", "Meeting integration", "Document collaboration", "Team management"]'::jsonb),
    ('Email Integration', 'Search across your email communications', 'email', false, 'Communication', '["Email indexing", "Attachment search", "Contact management", "Thread organization"]'::jsonb),
    ('WhatsApp Business', 'Connect WhatsApp Business for customer messaging', 'whatsapp', false, 'Communication', '["Customer messaging", "Automated responses", "Business profile & catalog", "Message templates"]'::jsonb),
    
    -- AI & Analytics
    ('Upstack AI', 'AI-powered document analysis and insights', 'ai', false, 'AI & Analytics', '["Smart document summaries", "Semantic search", "Content recommendations", "Trend analysis"]'::jsonb),
    ('Analytics Dashboard', 'View detailed usage analytics and insights', 'default', true, 'AI & Analytics', '["Usage reports", "Search analytics", "User activity tracking", "Performance metrics"]'::jsonb),
    ('Content Intelligence', 'AI-driven content organization and discovery', 'ai', true, 'AI & Analytics', '["Automatic categorization", "Content relationship mapping", "Knowledge extraction", "Insight generation"]'::jsonb);

-- Insert default company_products for existing companies (SharePoint enabled by default)
insert into public.company_products (company_id, product_id, is_enabled)
select c.id, p.id, true
from public.companies c
cross join public.products p
where p.name = 'SharePoint Integration';

-- Create function to update updated_at timestamp
create or replace function public.handle_updated_at()
returns trigger as $$
begin
    new.updated_at = timezone('utc'::text, now());
    return new;
end;
$$ language plpgsql;

-- Create triggers for updated_at
create trigger handle_products_updated_at
    before update on public.products
    for each row
    execute function public.handle_updated_at();

create trigger handle_company_products_updated_at
    before update on public.company_products
    for each row
    execute function public.handle_updated_at();

-- Down migration
-- drop trigger if exists handle_company_products_updated_at on public.company_products;
-- drop trigger if exists handle_products_updated_at on public.products;
-- drop function if exists public.handle_updated_at();
-- drop policy if exists "Allow admins to update company_products for their companies" on public.company_products;
-- drop policy if exists "Allow users to read company_products for their companies" on public.company_products;
-- drop policy if exists "Allow authenticated users to read products" on public.products;
-- drop table if exists public.company_products;
-- drop table if exists public.products;

-- Migration file to add 'upstack' to platform_type enum
-- Description: Adds Upstack as a new platform type for data source integration

-- Add new platform types
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'platform_type') THEN
        CREATE TYPE platform_type AS ENUM (
            'google',
            'sharepoint',
            'slack',
            'whatsapp',
            'email',
            'upstack',
            'courses'
        );
    ELSE
        -- Add new values if they don't exist
        BEGIN
            ALTER TYPE platform_type ADD VALUE IF NOT EXISTS 'upstack';
            ALTER TYPE platform_type ADD VALUE IF NOT EXISTS 'courses';
        EXCEPTION
            WHEN duplicate_object THEN NULL;
        END;
    END IF;
END$$;

COMMIT;

-- Migration: add_upstack_product
-- Description: Adds Upstack as a product option in the products table
-- Affected tables: products

-- Insert Upstack product if it doesn't exist already
INSERT INTO public.products (name, description, icon, is_coming_soon, category, features)
SELECT 
    'Upstack Integration', 
    'Connect and search your Upstack entities and documents', 
    'upstack', 
    false, 
    'Document Storage', 
    '["Entity management", "Document organization", "Version tracking", "PDF document search"]'::jsonb
WHERE NOT EXISTS (
    SELECT 1 FROM public.products WHERE name = 'Upstack Integration'
);

-- Add comment explaining what Upstack is
COMMENT ON TABLE public.products IS 
'Available products that can be enabled by companies. Includes document storage options like SharePoint, Google Workspace, and Upstack.'; 
-- Migration: 20250327090426_add_course_creator_integration.sql
-- Description: Adds Course Creator integration with multiple categories support
-- Author: System Administrator

-- Step 1: Add categories array to products table (if not exists)
ALTER TABLE products ADD COLUMN IF NOT EXISTS categories TEXT[] DEFAULT '{}';

-- Step 2: Add requires_product_id column if it doesn't exist
ALTER TABLE products ADD COLUMN IF NOT EXISTS requires_product_id UUID REFERENCES products(id) NULL;

-- Step 3: Add the Course Creator product if it doesn't exist
INSERT INTO products (id, name, description, categories, is_coming_soon)
VALUES 
  ('0d7b3945-0900-4096-bd8a-2b0e7d3f95ef', 'Course Creator', 'Create and manage educational courses. Includes the Courses storage provider for accessing course content.', ARRAY['System Extensions', 'Storage Provider'], false)
ON CONFLICT (id) DO UPDATE 
SET 
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  categories = EXCLUDED.categories,
  is_coming_soon = EXCLUDED.is_coming_soon;

-- Step 5: Add platform_type enum value for 'courses' if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'platform_type') THEN
    CREATE TYPE platform_type AS ENUM ('google', 'sharepoint', 'slack', 'dropbox', 'upstack', 'courses');
  ELSE
    BEGIN
      ALTER TYPE platform_type ADD VALUE IF NOT EXISTS 'courses';
    EXCEPTION
      WHEN duplicate_object THEN NULL;
    END;
  END IF;
END$$;

-- Step 6: Update existing views with platform_type if they exist
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_views WHERE viewname = 'data_sources_with_metadata') THEN
    DROP VIEW IF EXISTS data_sources_with_metadata;
    
    CREATE OR REPLACE VIEW data_sources_with_metadata AS
    SELECT 
      ds.id,
      ds.company_id,
      ds.platform_type,
      ds.display_name,
      ds.access_token,
      ds.secret_oauth,
      ds.metadata,
      ds.created_at,
      ds.updated_at,
      ds.is_active
    FROM data_sources ds;
  END IF;
END$$;

-- Step 7: Create RLS policy for the Courses storage provider
ALTER TABLE IF EXISTS data_sources ENABLE ROW LEVEL SECURITY;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM pg_policies 
    WHERE tablename = 'data_sources' 
    AND policyname = 'courses_storage_access'
  ) THEN
    CREATE POLICY courses_storage_access ON data_sources
    FOR ALL
    TO authenticated
    USING (
      platform_type::text = 'courses' AND 
      EXISTS (
        SELECT 1 FROM company_members 
        WHERE company_members.company_id = data_sources.company_id 
        AND company_members.user_id = auth.uid()
        AND company_members.is_active = true
      )
    );
  END IF;
END$$;

-- Step 8: Add comments to document the changes
COMMENT ON TABLE products IS 'Products and integrations available for companies to enable. Can now have multiple categories via categories array.';
COMMENT ON COLUMN products.categories IS 'An array of categories this product belongs to (e.g., ["Storage Provider", "System Extensions"])';
COMMENT ON COLUMN products.requires_product_id IS 'ID of another product that must be enabled for this product to work';

-- Migration Rollback:
/*
-- Drop the courses RLS policy
DROP POLICY IF EXISTS courses_storage_access ON data_sources;

-- Remove the courses platform_type enum value (not directly possible in PostgreSQL)
-- Would require recreating the enum without the value

-- Remove the courses storage provider product
DELETE FROM products WHERE id = 'fc1a72b2-2c33-4857-b387-c28902dc3fb8';

-- Remove the course creator product
DELETE FROM products WHERE id = '0d7b3945-0900-4096-bd8a-2b0e7d3f95ef';

-- Remove the requires_product_id column if needed
-- ALTER TABLE products DROP COLUMN IF EXISTS requires_product_id;

-- Remove the categories array if needed
-- ALTER TABLE products DROP COLUMN IF EXISTS categories;
*/ 

-- Migration: Add Course Creator integration
-- Description: Adds Course Creator product and Courses platform type with proper category handling
-- This migration adds support for multiple categories via an array column and implements the dependency between Course Creator and Courses

-- Add categories column to products table if it doesn't exist
do $$
begin
    if not exists (select 1 from information_schema.columns where table_name = 'products' and column_name = 'categories') then
        alter table products 
        add column categories text[] default '{}';
        
        -- Update existing products with their category in the new array format
        update products 
        set categories = array[category] 
        where category is not null and categories is null;
    end if;
end $$;

-- Add requires_product_id column if it doesn't exist
do $$
begin
    if not exists (select 1 from information_schema.columns where table_name = 'products' and column_name = 'requires_product_id') then
        alter table products 
        add column requires_product_id uuid references products(id) null;
    end if;
end $$;

-- Insert Course Creator product
insert into products (
    id, 
    name, 
    description, 
    category, 
    categories, 
    is_coming_soon, 
    created_at, 
    updated_at
)
values (
    '0d7b3945-0900-4096-bd8a-2b0e7d3f95ef', 
    'Course Creator', 
    'Create and manage courses with multiple providers', 
    'Learning', 
    array['Learning', 'Course Management'], 
    false, 
    now(), 
    now()
)
on conflict (id) do update
set 
    name = excluded.name,
    description = excluded.description,
    category = excluded.category,
    categories = excluded.categories,
    is_coming_soon = excluded.is_coming_soon,
    updated_at = now();

-- Insert Courses product with reference to Course Creator
insert into products (
    id, 
    name, 
    description, 
    category, 
    categories, 
    is_coming_soon, 
    created_at, 
    updated_at,
    requires_product_id
)
values (
    'fc1a72b2-2c33-4857-b387-c28902dc3fb8', 
    'Courses', 
    'Store and access course data from integrated platforms', 
    'Document Storage', 
    array['Document Storage'], 
    false, 
    now(), 
    now(),
    '0d7b3945-0900-4096-bd8a-2b0e7d3f95ef'
)
on conflict (id) do update
set 
    name = excluded.name,
    description = excluded.description,
    category = excluded.category,
    categories = excluded.categories,
    is_coming_soon = excluded.is_coming_soon,
    requires_product_id = excluded.requires_product_id,
    updated_at = now();

-- Create or update the platform_type enum to include 'courses'
do $$
begin
    -- First ensure the enum type exists
    if not exists (select 1 from pg_type where typname = 'platform_type') then
        create type platform_type as enum (
            'google_drive', 
            'notion', 
            'onedrive', 
            'confluence', 
            'slack', 
            'upstack', 
            'courses'
        );
    else
        -- Add 'courses' value if it doesn't exist
        if not exists (
            select 1 
            from pg_enum 
            where enumtypid = (select oid from pg_type where typname = 'platform_type') 
            and enumlabel = 'courses'
        ) then
            alter type platform_type add value if not exists 'courses';
        end if;
    end if;
end $$;

-- Ensure RLS policies for courses platform type
alter table data_sources enable row level security;

-- Policy: courses_data_sources_select
-- Description: Allow authenticated users to select their own courses data sources
-- Roles: authenticated
-- Operations: select
do $$
begin
    -- Drop existing policy if it exists
    drop policy if exists "courses_data_sources_select" on data_sources;
    
    -- Create new policy with direct text comparison
    create policy "courses_data_sources_select" on data_sources
    for select to authenticated
    using (
        platform_type::text = 'courses' and 
        company_id in (
            select company_id 
            from company_members 
            where user_id = auth.uid()
        )
    );
end $$;

-- Add courses to connected_accounts_by_company view
-- Note: external_id and name columns were commented out as they don't exist in the data_sources table
create or replace view connected_accounts_by_company as
select
    d.id,
    d.company_id,
    d.platform_type,
    d.created_at,
    -- d.external_id, -- commented out as column doesn't exist
    -- d.name, -- commented out as column doesn't exist
    case
        when d.platform_type = 'google' then 'Google Drive'
        when d.platform_type = 'sharepoint' then 'SharePoint'
        when d.platform_type = 'slack' then 'Slack'
        when d.platform_type = 'whatsapp' then 'WhatsApp'
        when d.platform_type = 'email' then 'Email'
        when d.platform_type = 'upstack' then 'Upstack'
        when d.platform_type = 'courses' then 'Courses'
        else 'Unknown'
    end as platform_name,
    array_agg(distinct m.user_id) as member_ids
from
    data_sources d
    left join company_members m on d.company_id = m.company_id
group by
    d.id, d.company_id, d.platform_type, d.created_at;

-- Update the data_sources_by_company_members view to include courses
-- Note: external_id and name columns were commented out as they don't exist in the data_sources table
create or replace view data_sources_by_company_members as
select
    d.id,
    d.company_id,
    d.platform_type,
    d.created_at,
    -- d.external_id, -- commented out as column doesn't exist
    -- d.name, -- commented out as column doesn't exist
    case
        when d.platform_type = 'google' then 'Google Drive'
        when d.platform_type = 'sharepoint' then 'SharePoint'
        when d.platform_type = 'slack' then 'Slack'
        when d.platform_type = 'whatsapp' then 'WhatsApp'
        when d.platform_type = 'email' then 'Email'
        when d.platform_type = 'upstack' then 'Upstack'
        when d.platform_type = 'courses' then 'Courses'
        else 'Unknown'
    end as platform_name,
    m.user_id
from
    data_sources d
    join company_members m on d.company_id = m.company_id;

-- Rollback section
-- In case of rollback, execute these statements:
-- 
-- -- Remove courses from platform_type enum
-- -- Note: Cannot remove enum values in PostgreSQL, would need to recreate the type
-- 
-- -- Remove requires_product_id column
-- -- alter table products drop column requires_product_id;
-- 
-- -- Remove categories column
-- -- alter table products drop column categories;
-- 
-- -- Delete the Courses product
-- -- delete from products where id = 'fc1a72b2-2c33-4857-b387-c28902dc3fb8';
-- 
-- -- Delete the Course Creator product
-- -- delete from products where id = '0d7b3945-0900-4096-bd8a-2b0e7d3f95ef';
-- 
-- -- Revert connected_accounts_by_company view
-- -- [previous view definition would go here]
-- 
-- -- Revert data_sources_by_company_members view
-- -- [previous view definition would go here]
-- 
-- -- Drop RLS policy
-- -- drop policy "courses_data_sources_select" on data_sources; 

-- Migration: Add user tags functionality
-- Description: Creates a user_tags table and RLS policies, with support for tagging users as course creators
-- Affected tables: user_tags (new)

-- Create user_tags table
CREATE TABLE IF NOT EXISTS public.user_tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
    tag_name TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    created_by UUID REFERENCES auth.users(id),
    UNIQUE(user_id, company_id, tag_name)
);

-- Add comment for the user_tags table
COMMENT ON TABLE public.user_tags IS 
'Stores tags assigned to users within a specific company context. Used for features like course creator access.';

-- Enable RLS for user_tags table
ALTER TABLE public.user_tags ENABLE ROW LEVEL SECURITY;

-- Policy: user_tags_select
-- Description: Allow authenticated users to view user tags in their companies
-- Roles: authenticated
-- Operations: select
CREATE POLICY "user_tags_select" ON public.user_tags
FOR SELECT TO authenticated
USING (
    company_id IN (
        SELECT company_id
        FROM public.company_members
        WHERE user_id = auth.uid()
    )
);

-- Policy: user_tags_insert
-- Description: Allow company admins to create user tags in their companies
-- Roles: authenticated
-- Operations: insert
CREATE POLICY "user_tags_insert" ON public.user_tags
FOR INSERT TO authenticated
WITH CHECK (
    company_id IN (
        SELECT company_id
        FROM public.company_members
        WHERE user_id = auth.uid() AND role = 'admin'
    )
);

-- Policy: user_tags_update
-- Description: Allow company admins to update user tags in their companies
-- Roles: authenticated
-- Operations: update
CREATE POLICY "user_tags_update" ON public.user_tags
FOR UPDATE TO authenticated
USING (
    company_id IN (
        SELECT company_id 
        FROM public.company_members 
        WHERE user_id = auth.uid() AND role = 'admin'
    )
)
WITH CHECK (
    company_id IN (
        SELECT company_id 
        FROM public.company_members 
        WHERE user_id = auth.uid() AND role = 'admin'
    )
);

-- Policy: user_tags_delete
-- Description: Allow company admins to delete user tags in their companies
-- Roles: authenticated
-- Operations: delete
CREATE POLICY "user_tags_delete" ON public.user_tags
FOR DELETE TO authenticated
USING (
    company_id IN (
        SELECT company_id 
        FROM public.company_members 
        WHERE user_id = auth.uid() AND role = 'admin'
    )
);

-- Create a function to check if a user has a specific tag
CREATE OR REPLACE FUNCTION public.user_has_tag(
    p_user_id UUID,
    p_company_id UUID,
    p_tag_name TEXT
) RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    tag_exists BOOLEAN;
BEGIN
    SELECT EXISTS(
        SELECT 1
        FROM public.user_tags
        WHERE user_id = p_user_id
        AND company_id = p_company_id
        AND tag_name = p_tag_name
    ) INTO tag_exists;
    
    RETURN tag_exists;
END;
$$;

-- Create a view to easily access user tags
CREATE OR REPLACE VIEW public.user_tags_view AS
SELECT 
    ut.id,
    ut.user_id,
    ut.company_id,
    ut.tag_name,
    ut.created_at,
    ut.created_by,
    u.email as user_email,
    u.raw_user_meta_data->>'full_name' as user_name,
    c.name as company_name,
    cu.raw_user_meta_data->>'full_name' as creator_name
FROM 
    public.user_tags ut
JOIN 
    auth.users u ON ut.user_id = u.id
JOIN 
    public.companies c ON ut.company_id = c.id
LEFT JOIN 
    auth.users cu ON ut.created_by = cu.id;

-- Create the course_creator_users view for easy access
CREATE OR REPLACE VIEW public.course_creator_users AS
SELECT 
    u.id as user_id,
    u.email as user_email,
    u.raw_user_meta_data->>'full_name' as user_name,
    ut.company_id,
    c.name as company_name,
    ut.created_at as tag_added_at
FROM 
    public.user_tags ut
JOIN 
    auth.users u ON ut.user_id = u.id
JOIN 
    public.companies c ON ut.company_id = c.id
WHERE 
    ut.tag_name = 'course_creator';

-- Rollback section
-- In case of rollback, execute these statements:
--
-- DROP VIEW IF EXISTS public.course_creator_users;
-- DROP VIEW IF EXISTS public.user_tags_view;
-- DROP FUNCTION IF EXISTS public.user_has_tag;
-- DROP TABLE IF EXISTS public.user_tags; 