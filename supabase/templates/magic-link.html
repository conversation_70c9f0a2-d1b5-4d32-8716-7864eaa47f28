<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{if eq .Data.type "login"}}Log in to TOTM{{else}}Join {{ .Data.companyName }} on TOTM{{end}}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f9fafb;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
        }
        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .logo {
            max-width: 150px;
            height: auto;
        }
        .content {
            padding: 30px 20px;
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #2563eb;
            color: #ffffff !important;
            text-decoration: none;
            border-radius: 6px;
            margin: 20px 0;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
            transition: transform 0.1s ease-in-out;
        }
        .button:hover {
            transform: translateY(-1px);
        }
        .footer {
            text-align: center;
            padding: 20px;
            color: #6b7280;
            font-size: 14px;
            border-top: 1px solid #e5e7eb;
        }
        .security-notice {
            background-color: #fffbeb;
            border: 1px solid #fef3c7;
            border-radius: 6px;
            padding: 12px;
            margin-top: 24px;
            font-size: 14px;
            color: #92400e;
        }
        @media only screen and (max-width: 600px) {
            .container {
                width: 100% !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 style="color: #1e3a8a;">TOTM</h1>
        </div>
        
        <div class="content">
            {{if eq .Data.type "login"}}
                <h2>Log in to TOTM</h2>
                <p>Hello,</p>
                <p>Click the button below or enter the code to log in to your account.</p>
            {{else}}
                <h2>You've been invited to {{ .Data.companyName }}!</h2>
                <p>Hello,</p>
                <p>You've been invited to join {{ .Data.companyName }} on TOTM. To get started, you'll need to:</p>
                <ol>
                    <li>Click the button below or enter the code to log in</li>
                    <li>Visit your profile page to set up WhatsApp integration</li>
                    <li>Start chatting with your organization's knowledge base</li>
                </ol>
                <p>Once you've set up your WhatsApp number, you'll be able to chat directly with your company's data through WhatsApp - making knowledge access easier than ever!</p>
            {{end}}

            <div style="text-align: center;">
                {{if eq .Data.type "login"}}
                    <a href="{{ .ConfirmationURL }}" class="button">Log In</a>
                {{else}}
                    <a href="{{ .ConfirmationURL }}" class="button">Log In & Set Up WhatsApp</a>
                {{end}}
            </div>

            {{if .Token}}
            <div style="margin: 20px 0; text-align: center; padding: 16px; background-color: #f8fafc; border-radius: 6px;">
                <p style="margin: 0 0 8px 0;">Or enter this login code:</p>
                <code style="font-size: 24px; letter-spacing: 2px; color: #2563eb;">{{ .Token }}</code>
                <p style="margin: 8px 0 0 0; font-size: 14px; color: #64748b;">You can use this code to log in if the button doesn't work</p>
            </div>
            {{end}}

            <div class="security-notice">
                <strong>Security Notice:</strong>
                <ul style="margin: 8px 0 0 0; padding-left: 20px;">
                    <li>This login link will expire in 24 hours</li>
                    <li>It can only be used once</li>
                    <li>Only works from the same device and browser</li>
                </ul>
            </div>

            {{if eq .Data.type "login"}}
                <p>If you did not request this login link, you can safely ignore this email.</p>
            {{else}}
                <p>If you believe this was sent in error, you can safely ignore this email.</p>
            {{end}}
            
            <p>Best regards,<br>The TOTM Team</p>
        </div>
        
        <div class="footer">
            <p>© 2025 TOTM. All rights reserved.</p>
            <p>If you have any questions, <NAME_EMAIL></p>
            <p style="font-size: 12px; margin-top: 16px;">For security reasons, please do not forward this email to anyone.</p>
        </div>
    </div>
</body>
</html> 