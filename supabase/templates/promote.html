<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to {{ .Data.companyName }}'s Knowledge Base</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f9fafb;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .logo {
            max-width: 150px;
            height: auto;
        }
        .content {
            padding: 30px 20px;
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #2563eb;
            color: #ffffff !important;
            text-decoration: none;
            border-radius: 6px;
            margin: 20px 0;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
            transition: transform 0.1s ease-in-out;
        }
        .button:hover {
            transform: translateY(-1px);
        }
        .role-badge {
            display: inline-block;
            padding: 4px 12px;
            background-color: #ef4444;
            color: #ffffff;
            border-radius: 9999px;
            font-size: 14px;
            font-weight: 500;
            margin: 8px 0;
        }
        .welcome-box {
            background-color: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .features-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
            margin: 20px 0;
        }
        .feature-item {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 16px;
        }
        .feature-item h3 {
            margin: 0 0 8px 0;
            font-size: 16px;
            color: #0f172a;
        }
        .feature-item p {
            margin: 0;
            font-size: 14px;
            color: #64748b;
        }
        .footer {
            text-align: center;
            padding: 20px;
            color: #6b7280;
            font-size: 14px;
            border-top: 1px solid #e5e7eb;
        }
        .security-notice {
            background-color: #fffbeb;
            border: 1px solid #fef3c7;
            border-radius: 6px;
            padding: 12px;
            margin-top: 24px;
            font-size: 14px;
            color: #92400e;
        }
        @media only screen and (max-width: 600px) {
            .container {
                width: 100% !important;
                margin: 0;
                border-radius: 0;
            }
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 style="color: #1e3a8a;">TOTM</h1>
        </div>
        
        <div class="content">
            <h2>Welcome to {{ .Data.companyName }}'s Knowledge Base!</h2>
            
            <p>Hello,</p>
            
            <div class="welcome-box">
                <p>You've been promoted to <span class="role-badge">{{ .Data.role }}</span> in {{ .Data.companyName }}</p>
                <p>TOTM helps teams collaborate and share knowledge effectively. As a {{ .Data.role }}, you'll have special access to help manage and curate your company's knowledge base.</p>
            </div>

            <div class="features-grid">
                {{if eq .Data.role "admin"}}
                <div class="feature-item">
                    <h3>🔑 User Management</h3>
                    <p>Manage team access and permissions</p>
                </div>
                <div class="feature-item">
                    <h3>⚙️ Company Settings</h3>
                    <p>Configure and customize your workspace</p>
                </div>
                <div class="feature-item">
                    <h3>📊 Data Sources</h3>
                    <p>Manage integrations and connections</p>
                </div>
                <div class="feature-item">
                    <h3>🎯 Full Access</h3>
                    <p>Complete control over company features</p>
                </div>
                {{else if eq .Data.role "expert"}}
                <div class="feature-item">
                    <h3>📚 Knowledge Base</h3>
                    <p>Curate and manage content</p>
                </div>
                <div class="feature-item">
                    <h3>❓ Question Management</h3>
                    <p>Answer and organize inquiries</p>
                </div>
                <div class="feature-item">
                    <h3>🔍 Advanced</h3>
                    <p>Access enhanced capabilities</p>
                </div>
                <div class="feature-item">
                    <h3>📈 Analytics</h3>
                    <p>Track knowledge base performance</p>
                </div>
                {{end}}
            </div>

            <p>To get started:</p>
            <ol style="margin: 0 0 20px 0; padding-left: 24px;">
                <li>Click the button below to access your new role</li>
                <li>Review your new permissions</li>
                <li>Start exploring your enhanced capabilities</li>
            </ol>

            <div style="text-align: center;">
                <a href="{{ .ConfirmationURL }}" class="button">Access Your New Role</a>
            </div>

            <div class="security-notice">
                <strong>Security Notice:</strong>
                <ul style="margin: 8px 0 0 0; padding-left: 20px;">
                    <li>This promotion link will expire in 24 hours</li>
                    <li>For security, please don't forward this email</li>
                    <li>Contact support if you weren't expecting this promotion</li>
                </ul>
            </div>
            
            <p style="margin-top: 24px;">Welcome to your new role!</p>
            
            <p>Best regards,<br>The TOTM Team</p>
        </div>
        
        <div class="footer">
            <p>© 2025 TOTM. All rights reserved.</p>
            <p>If you have any questions, <NAME_EMAIL></p>
        </div>
    </div>
</body>
</html> 