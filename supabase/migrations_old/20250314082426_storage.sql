drop policy "Allow users to insert their own platform profiles" on "public"."user_platform_profiles";

drop policy "Allow users to read platform profiles of company members" on "public"."user_platform_profiles";

drop policy "Users can delete their own platform profiles" on "public"."user_platform_profiles";

drop view if exists "public"."company_user_profiles";

alter table "public"."companies" alter column "transcription_time_allocated" drop default;

alter table "public"."companies" alter column "transcription_time_allocated" set data type numeric using "transcription_time_allocated"::numeric;

alter table "public"."companies" alter column "transcription_time_used" drop default;

alter table "public"."companies" alter column "transcription_time_used" set data type numeric using "transcription_time_used"::numeric;

alter table "public"."company_members" disable row level security;

alter table "public"."data_sources" alter column "secret_oauth" set data type uuid using "secret_oauth"::uuid;

alter table "public"."documents" add column "external_id" text;

alter table "public"."user_platform_profiles" add column "encrypted_refresh_token" uuid;

CREATE UNIQUE INDEX documents_file_id_key ON public.documents USING btree (file_id);

CREATE INDEX user_platform_profiles_company_member_id_idx ON public.user_platform_profiles USING btree (company_member_id);

CREATE INDEX user_platform_profiles_platform_type_idx ON public.user_platform_profiles USING btree (platform_type);

CREATE INDEX user_platform_profiles_platform_user_id_idx ON public.user_platform_profiles USING btree (platform_user_id);

alter table "public"."documents" add constraint "documents_file_id_key" UNIQUE using index "documents_file_id_key";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.encode_drive_id(drive_id text)
 RETURNS text
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    -- Replace '!' with '%21' in drive IDs that start with 'b!'
    IF drive_id LIKE 'b!%' THEN
        RETURN 'b%21' || substring(drive_id from 3);
    END IF;
    RETURN drive_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.exchange_refresh_token(refresh_token text, scope text DEFAULT 'https://graph.microsoft.com/.default'::text)
 RETURNS text
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    response jsonb;
    url text := 'https://login.microsoftonline.com/common/oauth2/v2.0/token';
    client_id text;
    client_secret text;
BEGIN
    -- Get Azure credentials from vault
    SELECT decrypted_secret INTO client_id FROM vault.decrypted_secrets WHERE name = 'AZURE_CLIENT_ID' LIMIT 1;
    SELECT decrypted_secret INTO client_secret FROM vault.decrypted_secrets WHERE name = 'AZURE_CLIENT_SECRET' LIMIT 1;

    -- Make HTTP request to get access token
    SELECT content::jsonb INTO response
    FROM http((
        'POST',
        url,
        ARRAY[http_header('Content-Type', 'application/x-www-form-urlencoded')],
        'application/json',
        format(
            'client_id=%s&client_secret=%s&grant_type=refresh_token&refresh_token=%s&scope=%s',
            client_id,
            client_secret,
            refresh_token,
            scope
        )
    )::http_request);

    -- Return the access token
    RETURN response->>'access_token';
EXCEPTION
    WHEN OTHERS THEN
        -- Log error details
        RAISE NOTICE 'Error exchanging refresh token: %', SQLERRM;
        RETURN NULL;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_file_access_users(file_external_id text, drive_id text, access_token text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    response jsonb;
    permissions jsonb;
    users jsonb := '[]'::jsonb;
    permission jsonb;
    user_info jsonb;
    group_members jsonb;
    site_id text;
    encoded_drive_id text;
BEGIN
    -- URL encode the drive ID
    encoded_drive_id := public.encode_drive_id(drive_id);

    -- Extract site ID from drive ID (assuming format b!{siteId}_{driveId})
    site_id := split_part(substring(drive_id from 3), '_', 1);

    -- Get file permissions
    SELECT content::jsonb INTO response
    FROM http((
        'GET',
        format(
            'https://graph.microsoft.com/v1.0/drives/%s/items/%s/permissions',
            encoded_drive_id,
            file_external_id
        ),
        ARRAY[http_header('Authorization', format('Bearer %s', access_token))],
        'application/json',
        ''
    )::http_request);

    -- Extract permissions array
    permissions := response->'value';

    -- Process each permission
    FOR permission IN SELECT * FROM jsonb_array_elements(permissions)
    LOOP
        -- Handle direct user access
        IF permission->'grantedToV2'->'user' IS NOT NULL THEN
            -- Add user directly from grantedToV2 info
            IF permission->'grantedToV2'->'user'->>'email' IS NOT NULL THEN
                users := users || jsonb_build_object(
                    'id', permission->'grantedToV2'->'user'->>'id',
                    'displayName', permission->'grantedToV2'->'user'->>'displayName',
                    'email', permission->'grantedToV2'->'user'->>'email',
                    'accessType', 'direct',
                    'roles', permission->'roles'
                );
            END IF;
        END IF;

        -- Handle Microsoft 365 group access
        IF permission->'grantedToV2'->'group' IS NOT NULL THEN
            -- Get group members
            SELECT content::jsonb INTO group_members
            FROM http((
                'GET',
                format(
                    'https://graph.microsoft.com/v1.0/groups/%s/members',
                    permission->'grantedToV2'->'group'->>'id'
                ),
                ARRAY[http_header('Authorization', format('Bearer %s', access_token))],
                'application/json',
                ''
            )::http_request);

            -- Add each group member to results
            FOR user_info IN SELECT * FROM jsonb_array_elements(group_members->'value')
            LOOP
                IF user_info->>'mail' IS NOT NULL THEN
                    users := users || jsonb_build_object(
                        'id', user_info->>'id',
                        'displayName', user_info->>'displayName',
                        'email', user_info->>'mail',
                        'accessType', 'group',
                        'groupName', permission->'grantedToV2'->'group'->>'displayName',
                        'roles', permission->'roles'
                    );
                END IF;
            END LOOP;
        END IF;

        -- Handle SharePoint site group access
        IF permission->'grantedToV2'->'siteGroup' IS NOT NULL THEN
            -- Get site group members
            SELECT content::jsonb INTO group_members
            FROM http((
                'GET',
                format(
                    'https://graph.microsoft.com/v1.0/sites/%s/groups/%s/members',
                    site_id,
                    permission->'grantedToV2'->'siteGroup'->>'id'
                ),
                ARRAY[http_header('Authorization', format('Bearer %s', access_token))],
                'application/json',
                ''
            )::http_request);

            -- Add each site group member to results
            FOR user_info IN SELECT * FROM jsonb_array_elements(group_members->'value')
            LOOP
                IF user_info->>'email' IS NOT NULL THEN
                    users := users || jsonb_build_object(
                        'id', user_info->>'id',
                        'displayName', user_info->>'displayName',
                        'email', user_info->>'email',
                        'accessType', 'site_group',
                        'groupName', permission->'grantedToV2'->'siteGroup'->>'displayName',
                        'roles', permission->'roles'
                    );
                END IF;
            END LOOP;
        END IF;
    END LOOP;

    -- Remove duplicates based on email, keeping the highest level of access
    RETURN (
        SELECT jsonb_agg(distinct_users)
        FROM (
            SELECT DISTINCT ON (lower(value->>'email')) value
            FROM jsonb_array_elements(users)
            ORDER BY lower(value->>'email'),
                CASE 
                    WHEN value->>'accessType' = 'direct' THEN 1
                    WHEN value->>'accessType' = 'site_group' THEN 2
                    WHEN value->>'accessType' = 'group' THEN 3
                    ELSE 4
                END
        ) as distinct_users
    );
EXCEPTION
    WHEN OTHERS THEN
        -- Log error details
        RAISE NOTICE 'Error getting file access users: %', SQLERRM;
        RETURN '[]'::jsonb;
END;
$function$
;

create type "public"."match_documents_with_permissions_result" as ("document_id" uuid, "file_name" text, "file_path" text, "file_external_id" text, "drive_id" text, "content" text, "content_preview" text, "similarity" real, "has_access" boolean, "access_details" jsonb);

CREATE OR REPLACE FUNCTION public.match_documents_with_permissions_sp(user_email text, company_id uuid, query_embedding vector)
 RETURNS SETOF match_documents_with_permissions_result
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    data_source record;
    access_token text;
    user_access jsonb;
    document record;
    result match_documents_with_permissions_result;
    has_access boolean;
    access_details jsonb;
BEGIN
    -- Get the SharePoint data source for the company
    SELECT * INTO data_source
    FROM data_sources
    WHERE company_id = $2 AND type = 'sharepoint'
    LIMIT 1;

    IF NOT FOUND THEN
        RAISE WARNING 'No SharePoint data source found for company %', company_id;
        RETURN;
    END IF;

    -- Get access token using the refresh token
    SELECT public.exchange_refresh_token(
        secret_value,
        'https://graph.microsoft.com/.default'
    ) INTO access_token
    FROM secrets
    WHERE id = data_source.id;

    IF access_token IS NULL THEN
        RAISE WARNING 'Failed to get access token for data source %', data_source.id;
        RETURN;
    END IF;

    -- Find matching documents using vector similarity
    FOR document IN
        SELECT 
            d.id as document_id,
            d.file_name,
            d.file_path,
            d.file_external_id,
            d.drive_id,
            d.content,
            d.content_preview,
            1 - (d.embedding <=> query_embedding) as similarity
        FROM documents d
        WHERE d.company_id = $2
        AND 1 - (d.embedding <=> query_embedding) > 0.7
        ORDER BY d.embedding <=> query_embedding
        LIMIT 20
    LOOP
        -- Get file access users with improved SharePoint group handling
        SELECT users INTO user_access
        FROM public.get_file_access_users(
            document.file_external_id,
            document.drive_id,
            data_source.id
        );

        -- Check if the user has access through any means (direct, group, or site group)
        has_access := EXISTS (
            SELECT 1
            FROM jsonb_array_elements(user_access) as u
            WHERE 
                LOWER(u->>'email') = LOWER($1) OR
                (
                    u->>'accessType' IN ('site_group', 'group') AND
                    u->>'groupName' IS NOT NULL
                )
        );

        -- Prepare access details
        access_details := jsonb_build_object(
            'has_direct_access', (
                SELECT EXISTS (
                    SELECT 1
                    FROM jsonb_array_elements(user_access) as u
                    WHERE 
                        LOWER(u->>'email') = LOWER($1) AND
                        u->>'accessType' = 'direct'
                )
            ),
            'groups', (
                SELECT jsonb_agg(DISTINCT u->>'groupName')
                FROM jsonb_array_elements(user_access) as u
                WHERE 
                    u->>'accessType' IN ('site_group', 'group') AND
                    u->>'groupName' IS NOT NULL
            ),
            'roles', (
                SELECT jsonb_agg(DISTINCT u->'roles')
                FROM jsonb_array_elements(user_access) as u
                WHERE 
                    LOWER(u->>'email') = LOWER($1) OR
                    u->>'accessType' IN ('site_group', 'group')
            )
        );

        -- Build the result record
        result := (
            document.document_id,
            document.file_name,
            document.file_path,
            document.file_external_id,
            document.drive_id,
            CASE WHEN has_access THEN document.content ELSE NULL END,
            document.content_preview,
            document.similarity,
            has_access,
            access_details
        );

        RETURN NEXT result;
    END LOOP;

    RETURN;
END;
$function$
;

create or replace view "public"."company_user_profiles" as  SELECT cm.id AS company_member_id,
    cm.company_id,
    cm.user_id,
    cm.role,
    cm.is_active,
    bu.email AS user_email,
    bu.phone_number AS user_phone_number,
    bu.raw_user_meta_data,
    c.name AS company_name,
    cp.slug AS company_slug,
    ci.id AS invite_id,
    ci.status AS invite_status,
        CASE
            WHEN cm.is_active THEN 'active'::text
            WHEN (ci.status = 'pending'::invite_status) THEN 'pending'::text
            ELSE 'inactive'::text
        END AS user_status
   FROM ((((company_members cm
     JOIN base_users bu ON ((bu.id = cm.user_id)))
     JOIN companies c ON ((c.id = cm.company_id)))
     LEFT JOIN company_personalization cp ON ((cp.company_id = c.id)))
     LEFT JOIN company_invites ci ON (((ci.email = bu.email) AND (ci.company_id = cm.company_id) AND (ci.status = 'pending'::invite_status))))
  WHERE (EXISTS ( SELECT 1
           FROM company_members
          WHERE ((company_members.company_id = cm.company_id) AND (company_members.user_id = auth.uid()) AND (company_members.is_active = true))));


create policy "Users can insert their own platform profiles"
on "public"."user_platform_profiles"
as permissive
for insert
to public
with check ((company_member_id IN ( SELECT company_members.id
   FROM company_members
  WHERE (company_members.user_id = auth.uid()))));


create policy "Users can update their own platform profiles"
on "public"."user_platform_profiles"
as permissive
for update
to public
using ((company_member_id IN ( SELECT company_members.id
   FROM company_members
  WHERE (company_members.user_id = auth.uid()))));


create policy "Users can view their own platform profiles"
on "public"."user_platform_profiles"
as permissive
for select
to public
using ((company_member_id IN ( SELECT company_members.id
   FROM company_members
  WHERE (company_members.user_id = auth.uid()))));


create policy "Users can delete their own platform profiles"
on "public"."user_platform_profiles"
as permissive
for delete
to public
using ((company_member_id IN ( SELECT company_members.id
   FROM company_members
  WHERE (company_members.user_id = auth.uid()))));



