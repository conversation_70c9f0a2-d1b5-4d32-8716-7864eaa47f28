-- Create stored procedure for handling WhatsApp data deletion
CREATE OR <PERSON><PERSON>LACE FUNCTION handle_whatsapp_data_deletion(
    p_business_id TEXT,
    p_user_id TEXT,
    p_metadata JSONB
) RETURNS void AS $$
BEGIN
    -- Log the data deletion request
    INSERT INTO auth_events (
        company_id,
        event_type,
        platform,
        metadata
    ) 
    SELECT 
        c.company_id,
        'data_deletion',
        'whatsapp',
        jsonb_build_object(
            'user_id', p_user_id,
            'business_id', p_business_id,
            'timestamp', CURRENT_TIMESTAMP,
            'additional_data', p_metadata
        )
    FROM oauth_tokens ot
    JOIN communication_channels c ON c.oauth_token_id = ot.id
    WHERE ot.business_id = p_business_id
    LIMIT 1;

    -- Delete user-specific data
    -- This will cascade to related tables due to foreign key constraints
    DELETE FROM user_data
    WHERE business_id = p_business_id;

    -- Mark channels as deleted
    UPDATE communication_channels
    SET status = 'deleted',
        deleted_at = CURRENT_TIMESTAMP,
        oauth_token_id = NULL
    WHERE oauth_token_id IN (
        SELECT id 
        FROM oauth_tokens 
        WHERE business_id = p_business_id
    );

    -- Remove OAuth tokens
    DELETE FROM oauth_tokens
    WHERE business_id = p_business_id;

    RETURN;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
