-- Migration: Add user tags functionality
-- Description: Creates a user_tags table and RLS policies, with support for tagging users as course creators
-- Affected tables: user_tags (new)

-- Create user_tags table
CREATE TABLE IF NOT EXISTS public.user_tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
    tag_name TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    created_by UUID REFERENCES auth.users(id),
    UNIQUE(user_id, company_id, tag_name)
);

-- Add comment for the user_tags table
COMMENT ON TABLE public.user_tags IS 
'Stores tags assigned to users within a specific company context. Used for features like course creator access.';

-- Enable RLS for user_tags table
ALTER TABLE public.user_tags ENABLE ROW LEVEL SECURITY;

-- Policy: user_tags_select
-- Description: Allow authenticated users to view user tags in their companies
-- Roles: authenticated
-- Operations: select
CREATE POLICY "user_tags_select" ON public.user_tags
FOR SELECT TO authenticated
USING (
    company_id IN (
        SELECT company_id 
        FROM public.company_members 
        WHERE user_id = auth.uid()
    )
);

-- Policy: user_tags_insert
-- Description: Allow company admins to create user tags in their companies
-- Roles: authenticated
-- Operations: insert
CREATE POLICY "user_tags_insert" ON public.user_tags
FOR INSERT TO authenticated
WITH CHECK (
    company_id IN (
        SELECT company_id 
        FROM public.company_members 
        WHERE user_id = auth.uid() AND role = 'admin'
    )
);

-- Policy: user_tags_update
-- Description: Allow company admins to update user tags in their companies
-- Roles: authenticated
-- Operations: update
CREATE POLICY "user_tags_update" ON public.user_tags
FOR UPDATE TO authenticated
USING (
    company_id IN (
        SELECT company_id 
        FROM public.company_members 
        WHERE user_id = auth.uid() AND role = 'admin'
    )
)
WITH CHECK (
    company_id IN (
        SELECT company_id 
        FROM public.company_members 
        WHERE user_id = auth.uid() AND role = 'admin'
    )
);

-- Policy: user_tags_delete
-- Description: Allow company admins to delete user tags in their companies
-- Roles: authenticated
-- Operations: delete
CREATE POLICY "user_tags_delete" ON public.user_tags
FOR DELETE TO authenticated
USING (
    company_id IN (
        SELECT company_id 
        FROM public.company_members 
        WHERE user_id = auth.uid() AND role = 'admin'
    )
);

-- Create a function to check if a user has a specific tag
CREATE OR REPLACE FUNCTION public.user_has_tag(
    p_user_id UUID,
    p_company_id UUID,
    p_tag_name TEXT
) RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    tag_exists BOOLEAN;
BEGIN
    SELECT EXISTS(
        SELECT 1
        FROM public.user_tags
        WHERE user_id = p_user_id
        AND company_id = p_company_id
        AND tag_name = p_tag_name
    ) INTO tag_exists;
    
    RETURN tag_exists;
END;
$$;

-- Create a view to easily access user tags
CREATE OR REPLACE VIEW public.user_tags_view AS
SELECT 
    ut.id,
    ut.user_id,
    ut.company_id,
    ut.tag_name,
    ut.created_at,
    ut.created_by,
    u.email as user_email,
    u.raw_user_meta_data->>'full_name' as user_name,
    c.name as company_name,
    cu.raw_user_meta_data->>'full_name' as creator_name
FROM 
    public.user_tags ut
JOIN 
    auth.users u ON ut.user_id = u.id
JOIN 
    public.companies c ON ut.company_id = c.id
LEFT JOIN 
    auth.users cu ON ut.created_by = cu.id;

-- Create the course_creator_users view for easy access
CREATE OR REPLACE VIEW public.course_creator_users AS
SELECT 
    u.id as user_id,
    u.email as user_email,
    u.raw_user_meta_data->>'full_name' as user_name,
    ut.company_id,
    c.name as company_name,
    ut.created_at as tag_added_at
FROM 
    public.user_tags ut
JOIN 
    auth.users u ON ut.user_id = u.id
JOIN 
    public.companies c ON ut.company_id = c.id
WHERE 
    ut.tag_name = 'course_creator';

-- Rollback section
-- In case of rollback, execute these statements:
--
-- DROP VIEW IF EXISTS public.course_creator_users;
-- DROP VIEW IF EXISTS public.user_tags_view;
-- DROP FUNCTION IF EXISTS public.user_has_tag;
-- DROP TABLE IF EXISTS public.user_tags; 