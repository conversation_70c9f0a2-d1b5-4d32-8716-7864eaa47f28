-- Create auth_events table for compliance tracking
CREATE TABLE IF NOT EXISTS auth_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
    event_type TEXT NOT NULL,
    platform TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Add indexes for common queries
    CONSTRAINT valid_event_type CHECK (event_type IN ('authorization', 'deauthorization', 'security_incident')),
    CONSTRAINT valid_platform CHECK (platform IN ('whatsapp', 'slack', 'microsoft', 'google'))
);

-- Add RLS policies
ALTER TABLE auth_events ENABLE ROW LEVEL SECURITY;

-- Allow insert from service role (for webhook handlers)
CREATE POLICY "Service can insert auth events"
    ON auth_events FOR INSERT
    TO service_role
    WITH CHECK (true);

-- Allow company members to view their events
CREATE POLICY "Company members can view their auth events"
    ON auth_events FOR SELECT
    TO authenticated
    USING (
        company_id IN (
            SELECT company_id 
            FROM company_members 
            WHERE user_id = auth.uid()
        )
    );

-- Add status and deauthorized_at to communication_channels
ALTER TABLE communication_channels 
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active',
ADD COLUMN IF NOT EXISTS deauthorized_at TIMESTAMPTZ,
ADD CONSTRAINT valid_status CHECK (status IN ('active', 'deauthorized', 'suspended'));
