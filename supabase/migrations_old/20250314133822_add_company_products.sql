-- Migration: add_company_products
-- Description: Adds tables for managing company products and their features
-- Affected tables: products, company_products
-- Security: RLS enabled with appropriate policies

-- Create products table
create table public.products (
    id uuid primary key default gen_random_uuid(),
    name text not null,
    description text,
    icon text,
    is_coming_soon boolean default false,
    category text default 'Other',
    features jsonb default '[]'::jsonb,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create company_products junction table
create table public.company_products (
    id uuid primary key default gen_random_uuid(),
    company_id uuid references public.companies(id) on delete cascade not null,
    product_id uuid references public.products(id) on delete cascade not null,
    is_enabled boolean default false,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null,
    unique(company_id, product_id)
);

-- Enable RLS
alter table public.products enable row level security;
alter table public.company_products enable row level security;

-- Create policies for products table
-- Allow all authenticated users to read products
create policy "Allow authenticated users to read products"
    on public.products for select
    to authenticated
    using (true);

-- Create policies for company_products table
-- Allow users to read company_products for their companies
create policy "Allow users to read company_products for their companies"
    on public.company_products for select
    to authenticated
    using (
        company_id in (
            select company_id 
            from public.company_members 
            where user_id = auth.uid()
        )
    );

-- Allow users to insert company_products for their companies if they are admin
create policy "Allow admins to insert company_products for their companies"
    on public.company_products for insert
    to authenticated
    with check (
        company_id in (
            select company_id 
            from public.company_members 
            where user_id = auth.uid() 
            and role = 'admin'
        )
    );

-- Allow users to update company_products for their companies if they are admin
create policy "Allow admins to update company_products for their companies"
    on public.company_products for update
    to authenticated
    using (
        company_id in (
            select company_id 
            from public.company_members 
            where user_id = auth.uid() 
            and role = 'admin'
        )
    );

-- Insert default products with categories and features
insert into public.products (name, description, icon, is_coming_soon, category, features) values
    -- Document Storage & Collaboration
    ('SharePoint Integration', 'Connect and search your SharePoint documents', 'sharepoint', false, 'Document Storage', '["Advanced document search", "Version history tracking", "Collaborative editing", "Permission management"]'::jsonb),
    ('Google Workspace', 'Connect and search your Google Drive documents', 'google', false, 'Document Storage', '["Google Docs integration", "Shared drives access", "Real-time collaboration", "Cloud storage management"]'::jsonb),
    ('OneDrive Connection', 'Access and search your Microsoft OneDrive files', 'onedrive', false, 'Document Storage', '["Personal file storage", "Microsoft 365 integration", "Cross-platform sync", "File sharing capabilities"]'::jsonb),
    ('Dropbox Integration', 'Connect and search your Dropbox files and folders', 'dropbox', false, 'Document Storage', '["Secure file sharing", "Document organization", "Version history", "Access controls"]'::jsonb),
    
    -- Communication Platforms
    ('Slack Integration', 'Connect and search your Slack messages and files', 'slack', false, 'Communication', '["Message search", "Channel integration", "File indexing", "Notification management"]'::jsonb),
    ('Microsoft Teams', 'Connect and search your Teams messages and documents', 'teams', false, 'Communication', '["Chat history search", "Meeting integration", "Document collaboration", "Team management"]'::jsonb),
    ('Email Integration', 'Search across your email communications', 'email', false, 'Communication', '["Email indexing", "Attachment search", "Contact management", "Thread organization"]'::jsonb),
    ('WhatsApp Business', 'Connect WhatsApp Business for customer messaging', 'whatsapp', false, 'Communication', '["Customer messaging", "Automated responses", "Business profile & catalog", "Message templates"]'::jsonb),
    
    -- AI & Analytics
    ('Upstack AI', 'AI-powered document analysis and insights', 'ai', false, 'AI & Analytics', '["Smart document summaries", "Semantic search", "Content recommendations", "Trend analysis"]'::jsonb),
    ('Analytics Dashboard', 'View detailed usage analytics and insights', 'default', true, 'AI & Analytics', '["Usage reports", "Search analytics", "User activity tracking", "Performance metrics"]'::jsonb),
    ('Content Intelligence', 'AI-driven content organization and discovery', 'ai', true, 'AI & Analytics', '["Automatic categorization", "Content relationship mapping", "Knowledge extraction", "Insight generation"]'::jsonb);

-- Insert default company_products for existing companies (SharePoint enabled by default)
insert into public.company_products (company_id, product_id, is_enabled)
select c.id, p.id, true
from public.companies c
cross join public.products p
where p.name = 'SharePoint Integration';

-- Create function to update updated_at timestamp
create or replace function public.handle_updated_at()
returns trigger as $$
begin
    new.updated_at = timezone('utc'::text, now());
    return new;
end;
$$ language plpgsql;

-- Create triggers for updated_at
create trigger handle_products_updated_at
    before update on public.products
    for each row
    execute function public.handle_updated_at();

create trigger handle_company_products_updated_at
    before update on public.company_products
    for each row
    execute function public.handle_updated_at();

-- Down migration
-- drop trigger if exists handle_company_products_updated_at on public.company_products;
-- drop trigger if exists handle_products_updated_at on public.products;
-- drop function if exists public.handle_updated_at();
-- drop policy if exists "Allow admins to update company_products for their companies" on public.company_products;
-- drop policy if exists "Allow users to read company_products for their companies" on public.company_products;
-- drop policy if exists "Allow authenticated users to read products" on public.products;
-- drop table if exists public.company_products;
-- drop table if exists public.products;

