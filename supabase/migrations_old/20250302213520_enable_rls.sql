-- Enable Row Level Security (RLS) on key tables
-- This migration ensures that RLS is enabled on base_users, companies, and company_members tables

-- Enable RLS on base_users table
ALTER TABLE public.base_users ENABLE ROW LEVEL SECURITY;

-- Enable RLS on companies table
ALTER TABLE public.companies ENABLE ROW LEVEL SECURITY;

-- Enable RLS on company_members table
ALTER TABLE public.company_members ENABLE ROW LEVEL SECURITY;

-- Default RLS policies to deny all operations for security
-- These policies should be refined in subsequent migrations based on business rules

-- Default policy for base_users - users can only see and modify their own records
CREATE POLICY "Users can view their own profile" 
    ON public.base_users FOR SELECT 
    USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" 
    ON public.base_users FOR UPDATE 
    USING (auth.uid() = id);

-- Default policy for companies - members can see companies they belong to
CREATE POLICY "Company members can view their companies" 
    ON public.companies FOR SELECT 
    USING (
        EXISTS (
            SELECT 1 FROM public.company_members
            WHERE company_members.company_id = companies.id
            AND company_members.user_id = auth.uid()
            AND company_members.is_active = true
        )
    );

-- Default policy for company_members - users can see other members of their companies
CREATE POLICY "Users can view members of their companies" 
    ON public.company_members FOR SELECT 
    USING (
        EXISTS (
            SELECT 1 FROM public.company_members AS cm
            WHERE cm.company_id = company_members.company_id
            AND cm.user_id = auth.uid()
            AND cm.is_active = true
        )
    ); 