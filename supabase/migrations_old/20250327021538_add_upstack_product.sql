-- Migration: add_upstack_product
-- Description: Adds Upstack as a product option in the products table
-- Affected tables: products

-- Insert Upstack product if it doesn't exist already
INSERT INTO public.products (name, description, icon, is_coming_soon, category, features)
SELECT 
    'Upstack Integration', 
    'Connect and search your Upstack entities and documents', 
    'upstack', 
    false, 
    'Document Storage', 
    '["Entity management", "Document organization", "Version tracking", "PDF document search"]'::jsonb
WHERE NOT EXISTS (
    SELECT 1 FROM public.products WHERE name = 'Upstack Integration'
);

-- Add comment explaining what Upstack is
COMMENT ON TABLE public.products IS 
'Available products that can be enabled by companies. Includes document storage options like SharePoint, Google Workspace, and Upstack.'; 