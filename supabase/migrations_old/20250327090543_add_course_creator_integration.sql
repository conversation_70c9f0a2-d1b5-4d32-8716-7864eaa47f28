-- Migration: Add Course Creator integration
-- Description: Adds Course Creator product and Courses platform type with proper category handling
-- This migration adds support for multiple categories via an array column and implements the dependency between Course Creator and Courses

-- Add categories column to products table if it doesn't exist
do $$
begin
    if not exists (select 1 from information_schema.columns where table_name = 'products' and column_name = 'categories') then
        alter table products 
        add column categories text[] default '{}';
        
        -- Update existing products with their category in the new array format
        update products 
        set categories = array[category] 
        where category is not null and categories is null;
    end if;
end $$;

-- Add requires_product_id column if it doesn't exist
do $$
begin
    if not exists (select 1 from information_schema.columns where table_name = 'products' and column_name = 'requires_product_id') then
        alter table products 
        add column requires_product_id uuid references products(id) null;
    end if;
end $$;

-- Insert Course Creator product
insert into products (
    id, 
    name, 
    description, 
    category, 
    categories, 
    is_coming_soon, 
    created_at, 
    updated_at
)
values (
    '0d7b3945-0900-4096-bd8a-2b0e7d3f95ef', 
    'Course Creator', 
    'Create and manage courses with multiple providers', 
    'Learning', 
    array['Learning', 'Course Management'], 
    false, 
    now(), 
    now()
)
on conflict (id) do update
set 
    name = excluded.name,
    description = excluded.description,
    category = excluded.category,
    categories = excluded.categories,
    is_coming_soon = excluded.is_coming_soon,
    updated_at = now();

-- Insert Courses product with reference to Course Creator
insert into products (
    id, 
    name, 
    description, 
    category, 
    categories, 
    is_coming_soon, 
    created_at, 
    updated_at,
    requires_product_id
)
values (
    'fc1a72b2-2c33-4857-b387-c28902dc3fb8', 
    'Courses', 
    'Store and access course data from integrated platforms', 
    'Document Storage', 
    array['Document Storage'], 
    false, 
    now(), 
    now(),
    '0d7b3945-0900-4096-bd8a-2b0e7d3f95ef'
)
on conflict (id) do update
set 
    name = excluded.name,
    description = excluded.description,
    category = excluded.category,
    categories = excluded.categories,
    is_coming_soon = excluded.is_coming_soon,
    requires_product_id = excluded.requires_product_id,
    updated_at = now();

-- Create or update the platform_type enum to include 'courses'
do $$
begin
    if not exists (select 1 from pg_type where typname = 'platform_type') then
        create type platform_type as enum (
            'google_drive', 
            'notion', 
            'onedrive', 
            'confluence', 
            'slack', 
            'upstack', 
            'courses'
        );
    else
        -- Check if 'courses' value already exists in the enum
        if not exists (
            select 1 
            from pg_enum 
            where enumtypid = (select oid from pg_type where typname = 'platform_type') 
            and enumlabel = 'courses'
        ) then
            -- Add 'courses' to the enum
            alter type platform_type add value 'courses';
        end if;
    end if;
end $$;

-- Ensure RLS policies for courses platform type
alter table data_sources enable row level security;

-- Policy: courses_data_sources_select
-- Description: Allow authenticated users to select their own courses data sources
-- Roles: authenticated
-- Operations: select
do $$
begin
    if not exists (
        select 1 
        from pg_policies 
        where tablename = 'data_sources' 
        and policyname = 'courses_data_sources_select'
    ) then
        create policy "courses_data_sources_select" on data_sources
        for select to authenticated
        using (
            platform_type = 'courses'::platform_type and 
            company_id in (
                select company_id 
                from company_members 
                where user_id = auth.uid()
            )
        );
    end if;
end $$;

-- Add courses to connected_accounts_by_company view
-- Note: external_id and name columns were commented out as they don't exist in the data_sources table
create or replace view connected_accounts_by_company as
select
    d.id,
    d.company_id,
    d.platform_type,
    d.created_at,
    -- d.external_id, -- commented out as column doesn't exist
    -- d.name, -- commented out as column doesn't exist
    case
        when d.platform_type = 'google' then 'Google Drive'
        when d.platform_type = 'sharepoint' then 'SharePoint'
        when d.platform_type = 'slack' then 'Slack'
        when d.platform_type = 'whatsapp' then 'WhatsApp'
        when d.platform_type = 'email' then 'Email'
        when d.platform_type = 'upstack' then 'Upstack'
        when d.platform_type = 'courses' then 'Courses'
        else 'Unknown'
    end as platform_name,
    array_agg(distinct m.user_id) as member_ids
from
    data_sources d
    left join company_members m on d.company_id = m.company_id
group by
    d.id, d.company_id, d.platform_type, d.created_at;

-- Update the data_sources_by_company_members view to include courses
-- Note: external_id and name columns were commented out as they don't exist in the data_sources table
create or replace view data_sources_by_company_members as
select
    d.id,
    d.company_id,
    d.platform_type,
    d.created_at,
    -- d.external_id, -- commented out as column doesn't exist
    -- d.name, -- commented out as column doesn't exist
    case
        when d.platform_type = 'google' then 'Google Drive'
        when d.platform_type = 'sharepoint' then 'SharePoint'
        when d.platform_type = 'slack' then 'Slack'
        when d.platform_type = 'whatsapp' then 'WhatsApp'
        when d.platform_type = 'email' then 'Email'
        when d.platform_type = 'upstack' then 'Upstack'
        when d.platform_type = 'courses' then 'Courses'
        else 'Unknown'
    end as platform_name,
    m.user_id
from
    data_sources d
    join company_members m on d.company_id = m.company_id;

-- Rollback section
-- In case of rollback, execute these statements:
-- 
-- -- Remove courses from platform_type enum
-- -- Note: Cannot remove enum values in PostgreSQL, would need to recreate the type
-- 
-- -- Remove requires_product_id column
-- -- alter table products drop column requires_product_id;
-- 
-- -- Remove categories column
-- -- alter table products drop column categories;
-- 
-- -- Delete the Courses product
-- -- delete from products where id = 'fc1a72b2-2c33-4857-b387-c28902dc3fb8';
-- 
-- -- Delete the Course Creator product
-- -- delete from products where id = '0d7b3945-0900-4096-bd8a-2b0e7d3f95ef';
-- 
-- -- Revert connected_accounts_by_company view
-- -- [previous view definition would go here]
-- 
-- -- Revert data_sources_by_company_members view
-- -- [previous view definition would go here]
-- 
-- -- Drop RLS policy
-- -- drop policy "courses_data_sources_select" on data_sources; 