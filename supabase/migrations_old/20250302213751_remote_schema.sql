set check_function_bodies = off;

CREATE OR REPLACE FUNCTION auth.handle_new_user()
 <PERSON><PERSON><PERSON><PERSON> trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    -- Enhanced logging
    RAISE LOG 'handle_new_user called: ID=%, email=%, auth_role=%', 
              NEW.id, 
              NEW.email, 
              current_setting('request.jwt.claims', true)::json->>'role';

    -- Test database permissions
    BEGIN
        -- Insert into base_users
        INSERT INTO public.base_users (
            id,
            display_name,
            email,
            avatar_url,
            created_at,
            updated_at
        )
        VALUES (
            NEW.id,
            COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
            NEW.email,
            COALESCE(NEW.raw_user_meta_data->>'avatar_url', NULL),
            NOW(),
            NOW()
        )
        ON CONFLICT (id) DO UPDATE SET
            email = NEW.email,
            display_name = COALESCE(NEW.raw_user_meta_data->>'full_name', EXCLUDED.display_name, NEW.email),
            avatar_url = COALESCE(NEW.raw_user_meta_data->>'avatar_url', EXCLUDED.avatar_url),
            updated_at = NOW();
            
        RAISE LOG 'Successfully inserted/updated base_users for %', NEW.id;
    EXCEPTION WHEN OTHERS THEN
        RAISE LOG 'Error inserting into base_users: %, %', SQLERRM, SQLSTATE;
        -- Continue execution to try the remaining operations
    END;

    -- If there are any pre-created company memberships, update them
    BEGIN
        UPDATE public.company_members cm
        SET 
            user_id = NEW.id,
            is_active = true
        FROM public.user_contact_info uci
        WHERE uci.company_member_id = cm.id
            AND uci.platform_type = 'email'
            AND uci.value = NEW.email
            AND cm.user_id IS NULL;
            
        RAISE LOG 'Updated company memberships for %', NEW.email;
    EXCEPTION WHEN OTHERS THEN
        RAISE LOG 'Error updating company_members: %, %', SQLERRM, SQLSTATE;
    END;
    
    -- Sync SharePoint profiles
    BEGIN
        -- Wait for company_members to be updated
        PERFORM pg_sleep(0.5);
        PERFORM public.sync_user_sharepoint_profiles(NEW.id);
        RAISE LOG 'Synced SharePoint profiles for %', NEW.id;
    EXCEPTION WHEN OTHERS THEN
        RAISE LOG 'Error syncing SharePoint profiles: %, %', SQLERRM, SQLSTATE;
    END;

    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        RAISE LOG 'Critical error in handle_new_user: %, %', SQLERRM, SQLSTATE;
        RETURN NEW; -- Still return NEW to allow the auth user to be created
END;
$function$
;

CREATE TRIGGER on_auth_session_created AFTER INSERT ON auth.sessions FOR EACH ROW EXECUTE FUNCTION handle_auth_user_signin();

-- CREATE TRIGGER on_auth_user_created AFTER INSERT ON auth.users FOR EACH ROW EXECUTE FUNCTION auth.handle_new_user();


set check_function_bodies = off;

CREATE OR REPLACE FUNCTION storage.foldername(name text)
 RETURNS text[]
 LANGUAGE plpgsql
AS $function$
begin
  return string_to_array(regexp_replace(name, '^/+', ''), '/');
end;
$function$
;

grant delete on table "storage"."s3_multipart_uploads" to "postgres";

grant insert on table "storage"."s3_multipart_uploads" to "postgres";

grant references on table "storage"."s3_multipart_uploads" to "postgres";

grant select on table "storage"."s3_multipart_uploads" to "postgres";

grant trigger on table "storage"."s3_multipart_uploads" to "postgres";

grant truncate on table "storage"."s3_multipart_uploads" to "postgres";

grant update on table "storage"."s3_multipart_uploads" to "postgres";

grant delete on table "storage"."s3_multipart_uploads_parts" to "postgres";

grant insert on table "storage"."s3_multipart_uploads_parts" to "postgres";

grant references on table "storage"."s3_multipart_uploads_parts" to "postgres";

grant select on table "storage"."s3_multipart_uploads_parts" to "postgres";

grant trigger on table "storage"."s3_multipart_uploads_parts" to "postgres";

grant truncate on table "storage"."s3_multipart_uploads_parts" to "postgres";

grant update on table "storage"."s3_multipart_uploads_parts" to "postgres";


