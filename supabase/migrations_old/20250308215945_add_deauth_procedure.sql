-- Create stored procedure for handling WhatsApp deauthorization
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION handle_whatsapp_deauthorization(
    p_token_id UUID,
    p_company_id UUID,
    p_business_id TEXT,
    p_user_id TEXT,
    p_metadata JSONB
) R<PERSON><PERSON><PERSON> void AS $$
BEGIN
    -- Insert deauthorization event
    INSERT INTO auth_events (
        company_id,
        event_type,
        platform,
        metadata
    ) VALUES (
        p_company_id,
        'deauthorization',
        'whatsapp',
        jsonb_build_object(
            'user_id', p_user_id,
            'business_id', p_business_id,
            'timestamp', CURRENT_TIMESTAMP,
            'additional_data', p_metadata
        )
    );

    -- Update communication channel status
    UPDATE communication_channels
    SET status = 'deauthorized',
        deauthorized_at = CURRENT_TIMESTAMP,
        oauth_token_id = NULL
    WHERE oauth_token_id = p_token_id;

    -- Delete the OAuth token
    DELETE FROM oauth_tokens
    WHERE id = p_token_id;

    -- Commit the transaction
    RETURN;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
