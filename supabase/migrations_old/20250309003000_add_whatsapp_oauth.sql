-- Create enum for channel status if it doesn't exist
DO $$ BEGIN
    CREATE TYPE public.channel_status AS ENUM ('connected', 'disconnected', 'pending');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Update communication_channels table structure
ALTER TABLE public.communication_channels
ADD COLUMN IF NOT EXISTS channel_status channel_status NOT NULL DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS platform_id TEXT,
ADD COLUMN IF NOT EXISTS platform_metadata JSONB DEFAULT '{}'::jsonb,
ADD COLUMN IF NOT EXISTS access_token_secret uuid,
ADD COLUMN IF NOT EXISTS refresh_token_secret uuid;

-- Create index for platform lookups
CREATE INDEX IF NOT EXISTS idx_communication_channels_platform
ON public.communication_channels(platform_type, platform_id);

-- Add comments
COMMENT ON COLUMN public.communication_channels.platform_type IS 'Type of platform (e.g., whatsapp, slack, teams)';
COMMENT ON COLUMN public.communication_channels.platform_id IS 'Platform-specific identifier (e.g., Whats<PERSON>pp business ID, Slack team ID)';
COMMENT ON COLUMN public.communication_channels.channel_status IS 'Current status of the channel connection';
COMMENT ON COLUMN public.communication_channels.platform_metadata IS 'Additional platform-specific data in JSON format';
COMMENT ON COLUMN public.communication_channels.access_token_secret IS 'Reference to the access token stored in Vault';
COMMENT ON COLUMN public.communication_channels.refresh_token_secret IS 'Reference to the refresh token stored in Vault';

-- Migrate any existing Slack workspace IDs if the column exists
DO $$ 
BEGIN
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'communication_channels' 
        AND column_name = 'slack_workspace_id'
    ) THEN
        UPDATE public.communication_channels
        SET platform_id = slack_workspace_id,
            platform_metadata = jsonb_build_object('workspace_id', slack_workspace_id)
        WHERE platform_type = 'slack' 
        AND slack_workspace_id IS NOT NULL;

        ALTER TABLE public.communication_channels
        DROP COLUMN slack_workspace_id;
    END IF;
END $$; 