-- Migration: 20250327090426_add_course_creator_integration.sql
-- Description: Adds Course Creator integration with multiple categories support
-- Author: System Administrator

-- Step 1: Add categories array to products table (if not exists)
ALTER TABLE products ADD COLUMN IF NOT EXISTS categories TEXT[] DEFAULT '{}';

-- Step 2: Add requires_product_id column if it doesn't exist
ALTER TABLE products ADD COLUMN IF NOT EXISTS requires_product_id UUID REFERENCES products(id) NULL;

-- Step 3: Add the Course Creator product if it doesn't exist
INSERT INTO products (id, name, description, categories, is_coming_soon)
VALUES 
  ('0d7b3945-0900-4096-bd8a-2b0e7d3f95ef', 'Course Creator', 'Create and manage educational courses. Includes the Courses storage provider for accessing course content.', ARRAY['System Extensions', 'Storage Provider'], false)
ON CONFLICT (id) DO UPDATE 
SET 
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  categories = EXCLUDED.categories,
  is_coming_soon = EXCLUDED.is_coming_soon;

-- Step 5: Add platform_type enum value for 'courses' if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'platform_type') THEN
    CREATE TYPE platform_type AS ENUM ('google', 'sharepoint', 'slack', 'dropbox', 'upstack', 'courses');
  ELSE
    BEGIN
      ALTER TYPE platform_type ADD VALUE IF NOT EXISTS 'courses';
    EXCEPTION
      WHEN duplicate_object THEN NULL;
    END;
  END IF;
END$$;

-- Step 6: Update existing views with platform_type if they exist
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_views WHERE viewname = 'data_sources_with_metadata') THEN
    DROP VIEW IF EXISTS data_sources_with_metadata;
    
    CREATE OR REPLACE VIEW data_sources_with_metadata AS
    SELECT 
      ds.id,
      ds.company_id,
      ds.platform_type,
      ds.display_name,
      ds.access_token,
      ds.secret_oauth,
      ds.metadata,
      ds.created_at,
      ds.updated_at,
      ds.is_active
    FROM data_sources ds;
  END IF;
END$$;

-- Step 7: Create RLS policy for the Courses storage provider
ALTER TABLE IF EXISTS data_sources ENABLE ROW LEVEL SECURITY;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM pg_policies 
    WHERE tablename = 'data_sources' 
    AND policyname = 'courses_storage_access'
  ) THEN
    CREATE POLICY courses_storage_access ON data_sources
    FOR ALL
    TO authenticated
    USING (
      platform_type::text = 'courses' AND 
      EXISTS (
        SELECT 1 FROM company_members 
        WHERE company_members.company_id = data_sources.company_id 
        AND company_members.user_id = auth.uid()
        AND company_members.is_active = true
      )
    );
  END IF;
END$$;

-- Step 8: Add comments to document the changes
COMMENT ON TABLE products IS 'Products and integrations available for companies to enable. Can now have multiple categories via categories array.';
COMMENT ON COLUMN products.categories IS 'An array of categories this product belongs to (e.g., ["Storage Provider", "System Extensions"])';
COMMENT ON COLUMN products.requires_product_id IS 'ID of another product that must be enabled for this product to work';

-- Migration Rollback:
/*
-- Drop the courses RLS policy
DROP POLICY IF EXISTS courses_storage_access ON data_sources;

-- Remove the courses platform_type enum value (not directly possible in PostgreSQL)
-- Would require recreating the enum without the value

-- Remove the courses storage provider product
DELETE FROM products WHERE id = 'fc1a72b2-2c33-4857-b387-c28902dc3fb8';

-- Remove the course creator product
DELETE FROM products WHERE id = '0d7b3945-0900-4096-bd8a-2b0e7d3f95ef';

-- Remove the requires_product_id column if needed
-- ALTER TABLE products DROP COLUMN IF EXISTS requires_product_id;

-- Remove the categories array if needed
-- ALTER TABLE products DROP COLUMN IF EXISTS categories;
*/ 