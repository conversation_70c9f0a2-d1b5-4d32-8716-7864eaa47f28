-- Check and log the RLS status for all tables
BEGIN;

-- Load pgTAP extension
CREATE EXTENSION IF NOT EXISTS pgtap;

-- Create a function to check RLS status
CREATE OR REPLACE FUNCTION check_rls_status()
RETURNS VOID AS $$
DECLARE
    r RECORD;
BEGIN
    RAISE NOTICE 'Checking RLS status for all tables in public schema:';
    FOR r IN 
        SELECT c.relname as table_name, c.relrowsecurity as rls_enabled
        FROM pg_class c
        JOIN pg_namespace n ON n.oid = c.relnamespace
        WHERE n.nspname = 'public'
        AND c.relkind = 'r'  -- Only tables
        ORDER BY c.relname
    LOOP
        RAISE NOTICE 'Table: %, RLS Enabled: %', r.table_name, r.rls_enabled;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Check test tables
CREATE TABLE IF NOT EXISTS public.test_table (id serial primary key, name text);
ALTER TABLE public.test_table ENABLE ROW LEVEL SECURITY;
CREATE POLICY test_policy ON public.test_table FOR SELECT USING (true);

-- Execute the function to display RLS status
SELECT check_rls_status();

-- Plan one test
SELECT plan(1);

-- Just run one test to see if we can pass something
SELECT ok(row_security_active('public.test_table'), 'Test table should have RLS enabled');

-- Finish the test
SELECT * FROM finish();

-- Clean up
DROP TABLE IF EXISTS public.test_table;
DROP FUNCTION IF EXISTS check_rls_status();

ROLLBACK; 