-- Based on https://github.com/usebasejump/supabase-test-helpers

BEGIN;

CREATE SCHEMA IF NOT EXISTS tests;

-- function to create a test user in the "auth.users" table and "auth.identities" for JWT tests
CREATE OR REPLACE FUNCTION tests.create_supabase_user(identifier text DEFAULT 'test_user', email text DEFAULT NULL, phone text DEFAULT NULL, metadata jsonb DEFAULT NULL)
    RETURNS uuid
    LANGUAGE plpgsql
    SECURITY DEFINER
    SET search_path = auth, pg_temp
AS $$
DECLARE
    uuid            uuid;
    email_identity  uuid;
    phone_identity  uuid;
    raw_user_meta   jsonb;
BEGIN
    -- ID for the user
    uuid := gen_random_uuid();

    -- Default metadata to the user id, email, and phone if provided
    raw_user_meta := jsonb_build_object('sub', uuid::text);
    IF email IS NOT NULL THEN
        raw_user_meta = raw_user_meta || jsonb_build_object('email', email);
    END IF;
    IF phone IS NOT NULL THEN
        raw_user_meta = raw_user_meta || jsonb_build_object('phone', phone);
    END IF;
    IF metadata IS NOT NULL THEN
        raw_user_meta = raw_user_meta || metadata;
    END IF;

    -- Insert user data into auth.users
    INSERT INTO auth.users (id, email, phone, raw_user_meta_data, raw_app_meta_data)
    VALUES (uuid, email, phone, raw_user_meta, '{}'::jsonb);

    -- Create an identity for the email if provided
    IF email IS NOT NULL THEN
        email_identity := gen_random_uuid();
        INSERT INTO auth.identities (id, provider_id, user_id, identity_data, last_sign_in_at, created_at, updated_at)
        VALUES (email_identity, 'email', uuid, jsonb_build_object('sub', uuid, 'email', email), now(), now(), now());
    END IF;

    -- Create an identity for the phone if provided
    IF phone IS NOT NULL THEN
        phone_identity := gen_random_uuid();
        INSERT INTO auth.identities (id, provider_id, user_id, identity_data, last_sign_in_at, created_at, updated_at)
        VALUES (phone_identity, 'phone', uuid, jsonb_build_object('sub', uuid, 'phone', phone), now(), now(), now());
    END IF;

    RETURN uuid;
END;
$$;

-- function to get a user's id from an identifier
CREATE OR REPLACE FUNCTION tests.get_supabase_uid(identifier text)
    RETURNS uuid
    LANGUAGE plpgsql
    SECURITY DEFINER
    SET search_path = auth, pg_temp
AS $$
DECLARE
    uuid uuid;
BEGIN
    SELECT id INTO uuid FROM auth.users WHERE raw_user_meta_data->>'sub' = identifier;
    RETURN uuid;
END;
$$;

-- function to get a user's jwt from an identifier
CREATE OR REPLACE FUNCTION tests.get_supabase_user(identifier text)
    RETURNS json
    LANGUAGE plpgsql
    SECURITY DEFINER
    SET search_path = auth, pg_temp
AS $$
DECLARE
    usr json;
BEGIN
    SELECT json_build_object(
             'id', id,
             'email', email,
             'phone', phone,
             'raw_user_meta_data', raw_user_meta_data,
             'raw_app_meta_data', raw_app_meta_data,
             'is_anonymous', is_anonymous,
             'created_at', created_at,
             'updated_at', updated_at,
             'last_sign_in_at', last_sign_in_at,
             'banned_until', banned_until,
             'confirmed_at', confirmed_at,
             'confirmation_token', confirmation_token,
             'confirmation_sent_at', confirmation_sent_at,
             'recovery_token', recovery_token,
             'recovery_sent_at', recovery_sent_at,
             'email_change_token', email_change_token,
             'email_change', email_change,
             'email_change_sent_at', email_change_sent_at,
             'reauthentication_token', reauthentication_token,
             'reauthentication_sent_at', reauthentication_sent_at
        ) INTO usr
    FROM auth.users
    WHERE raw_user_meta_data->>'sub' = identifier;
    RETURN usr;
END;
$$;

-- function to authenticate as a user within the tests
CREATE OR REPLACE FUNCTION tests.authenticate_as(identifier text)
    RETURNS void
    LANGUAGE plpgsql
    SECURITY DEFINER
    SET search_path = auth, pg_temp
AS $$
DECLARE
    user_id uuid;
BEGIN
    -- Get the user id for the identifier
    SELECT tests.get_supabase_uid(identifier) INTO user_id;

    -- Set the request.jwt.claims to the user
    PERFORM set_config('request.jwt.claims', json_build_object('sub', user_id::text, 'role', 'authenticated')::text, false);
END;
$$;

-- function to clear the authentication
CREATE OR REPLACE FUNCTION tests.clear_authentication()
    RETURNS void
    LANGUAGE plpgsql
    SECURITY DEFINER
    SET search_path = auth, pg_temp
AS $$
BEGIN
    -- Set the request.jwt.claims to an anonymous user
    PERFORM set_config('request.jwt.claims', json_build_object('role', 'anon')::text, false);
END;
$$;

-- function to check if RLS is enabled on a table
CREATE OR REPLACE FUNCTION tests.rls_enabled(schema_name text, table_name text)
    RETURNS text
    LANGUAGE plpgsql
    SECURITY DEFINER
    SET search_path = pg_catalog, pg_temp
AS $$
DECLARE
    rls_enabled bool;
BEGIN
    SELECT relrowsecurity INTO rls_enabled
    FROM pg_class
    WHERE oid = (schema_name || '.' || table_name)::regclass;

    IF rls_enabled THEN
        RETURN ok(true, format('Row Level Security enabled on %s.%s', schema_name, table_name));
    ELSE
        RETURN ok(false, format('Row Level Security not enabled on %s.%s', schema_name, table_name));
    END IF;
END;
$$;

-- function to check if RLS is enabled on all tables in a schema
CREATE OR REPLACE FUNCTION tests.rls_enabled(schema_name text)
    RETURNS text
    LANGUAGE plpgsql
    SECURITY DEFINER
    SET search_path = pg_catalog, pg_temp
AS $$
DECLARE
    failed_tables text[];
    table_name text;
    rls_enabled bool;
BEGIN
    failed_tables := ARRAY[]::text[];

    FOR table_name IN
        SELECT c.relname
        FROM pg_class c
            JOIN pg_namespace n ON n.oid = c.relnamespace
        WHERE n.nspname = schema_name
            AND c.relkind = 'r'
            AND c.relname != 'schema_migrations'
            AND c.relname != 'roles'
    LOOP
        SELECT c.relrowsecurity INTO rls_enabled
        FROM pg_class c
        WHERE c.oid = (schema_name || '.' || table_name)::regclass;

        IF NOT rls_enabled THEN
            failed_tables := array_append(failed_tables, table_name);
        END IF;
    END LOOP;

    IF array_length(failed_tables, 1) IS NULL THEN
        RETURN ok(true, format('Row Level Security enabled on all tables in schema %s', schema_name));
    ELSE
        RETURN ok(false, format('Row Level Security not enabled on tables in schema %s: %s', schema_name, array_to_string(failed_tables, ', ')));
    END IF;
END;
$$;

-- We have to run some tests to get this to pass as the first test file
select plan(7);
select function_returns('tests', 'create_supabase_user', Array['text', 'text', 'text', 'jsonb'], 'uuid');
select function_returns('tests', 'get_supabase_uid', Array['text'], 'uuid');
select function_returns('tests', 'get_supabase_user', Array['text'], 'json');
select function_returns('tests', 'authenticate_as', Array['text'], 'void');
select function_returns('tests', 'clear_authentication', Array[null], 'void');
select function_returns('tests', 'rls_enabled', Array['text', 'text'], 'text');
select function_returns('tests', 'rls_enabled', Array['text'], 'text');
select * from finish();

ROLLBACK; 