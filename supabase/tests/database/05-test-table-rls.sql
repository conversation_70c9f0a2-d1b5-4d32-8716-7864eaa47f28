-- Test to verify we can enable <PERSON><PERSON> on a test table
BEGIN;

-- Load pgTAP extension
CREATE EXTENSION IF NOT EXISTS pgtap;

-- Plan tests
SELECT plan(1);

-- Create a test table with RLS enabled
CREATE TABLE IF NOT EXISTS public.test_table (id serial primary key, name text);
ALTER TABLE public.test_table ENABLE ROW LEVEL SECURITY;

-- Check RLS is enabled by querying system tables directly
SELECT ok(
    EXISTS (
        SELECT 1 
        FROM pg_class c
        JOIN pg_namespace n ON n.oid = c.relnamespace
        WHERE n.nspname = 'public'
        AND c.relname = 'test_table'
        AND c.relrowsecurity = true
    ),
    'Test table should have RLS enabled'
);

-- Finish the test
SELECT * FROM finish();

-- Clean up
DROP TABLE IF EXISTS public.test_table;

ROLLBACK; 