-- Direct RLS check using PostgreSQL functions
BEGIN;

-- Load pgTAP extension
CREATE EXTENSION IF NOT EXISTS pgtap;

-- Plan the test
SELECT plan(1);

-- Create a test table with RLS enabled
CREATE TABLE IF NOT EXISTS public.direct_test_table (id serial primary key, name text);
ALTER TABLE public.direct_test_table ENABLE ROW LEVEL SECURITY;

-- Instead of using row_security_active(), query pg_class directly
SELECT ok(
    EXISTS (
        SELECT 1 
        FROM pg_class c
        JOIN pg_namespace n ON n.oid = c.relnamespace
        WHERE n.nspname = 'public'
        AND c.relname = 'direct_test_table'
        AND c.relrowsecurity = true
    ),
    'Direct test table should have RLS enabled'
);

-- Finish the test
SELECT * FROM finish();

-- Clean up
DROP TABLE IF EXISTS public.direct_test_table;

ROLLBACK; 