-- Combined RLS test file
-- Sets the client message level to warning to reduce noise
SET client_min_messages TO warning;

-- Begin a transaction so we can roll back all changes
BEGIN;

-- Run all the RLS tests
SELECT plan(13);

-- Create helper function to check if <PERSON><PERSON> is enabled
CREATE OR REPLACE FUNCTION _rls_enabled(testing_schema text, testing_table text)
RETURNS boolean AS $$
    SELECT 
        EXISTS (
            SELECT 1 FROM pg_tables 
            WHERE schemaname = testing_schema 
            AND tablename = testing_table 
            AND rowsecurity = true
        );
$$ LANGUAGE SQL;

-- Basic RLS test to check if RLS is enabled on key tables
SELECT ok(
    _rls_enabled('public', 'base_users'),
    'RLS should be enabled on base_users'
);

SELECT ok(
    _rls_enabled('public', 'companies'),
    'RLS should be enabled on companies'
);

SELECT ok(
    _rls_enabled('public', 'company_members'),
    'RLS should be enabled on company_members'
);

-- List all RLS policies on base_users table
SELECT diag('Current RLS policies for base_users:');
SELECT diag(format(' - %s', p.polname)) 
FROM pg_policy p
JOIN pg_class c ON p.polrelid = c.oid
JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE n.nspname = 'public' AND c.relname = 'base_users';

-- List all RLS policies on companies table
SELECT diag('Current RLS policies for companies:');
SELECT diag(format(' - %s', p.polname)) 
FROM pg_policy p
JOIN pg_class c ON p.polrelid = c.oid
JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE n.nspname = 'public' AND c.relname = 'companies';

-- List all RLS policies on company_members table
SELECT diag('Current RLS policies for company_members:');
SELECT diag(format(' - %s', p.polname)) 
FROM pg_policy p
JOIN pg_class c ON p.polrelid = c.oid
JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE n.nspname = 'public' AND c.relname = 'company_members';

-- Verify key policies exist for base_users
SELECT ok(EXISTS(
  SELECT 1 
  FROM pg_policy p
  JOIN pg_class c ON p.polrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  WHERE n.nspname = 'public' AND c.relname = 'base_users' 
  AND p.polname = 'Base users visibility policy'
), 'base_users has visibility policy');

SELECT ok(EXISTS(
  SELECT 1 
  FROM pg_policy p
  JOIN pg_class c ON p.polrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  WHERE n.nspname = 'public' AND c.relname = 'base_users' 
  AND p.polname = 'Users can update their own base profile'
), 'base_users has self-update policy');

-- Verify key policies exist for companies
SELECT ok(EXISTS(
  SELECT 1 
  FROM pg_policy p
  JOIN pg_class c ON p.polrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  WHERE n.nspname = 'public' AND c.relname = 'companies' 
  AND p.polname = 'Members can view their companies'
), 'companies has member view policy');

SELECT ok(EXISTS(
  SELECT 1 
  FROM pg_policy p
  JOIN pg_class c ON p.polrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  WHERE n.nspname = 'public' AND c.relname = 'companies' 
  AND p.polname = 'Allow company admins to update companies'
), 'companies has admin update policy');

-- Verify key policies exist for company_members
SELECT ok(EXISTS(
  SELECT 1 
  FROM pg_policy p
  JOIN pg_class c ON p.polrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  WHERE n.nspname = 'public' AND c.relname = 'company_members' 
  AND p.polname = 'Allow members to view others in the same company'
), 'company_members has member view policy');

SELECT ok(EXISTS(
  SELECT 1 
  FROM pg_policy p
  JOIN pg_class c ON p.polrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  WHERE n.nspname = 'public' AND c.relname = 'company_members' 
  AND p.polname = 'Allow admins to update other members'
), 'company_members has admin update policy');

-- Check for triggers on auth.users
SELECT ok(
  EXISTS(
    SELECT 1 
    FROM pg_trigger t
    JOIN pg_class c ON t.tgrelid = c.oid
    JOIN pg_namespace n ON c.relnamespace = n.oid
    WHERE n.nspname = 'auth' AND c.relname = 'users'
  ),
  'auth.users table should have a trigger for user creation'
);

-- List all user creation related triggers
SELECT diag('Auth user creation triggers:');
SELECT diag(format(' - %s (function: %s)', t.tgname, p.proname)) 
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
JOIN pg_namespace n ON c.relnamespace = n.oid
JOIN pg_proc p ON t.tgfoid = p.oid
WHERE n.nspname = 'auth' AND c.relname = 'users';

-- Check for company->company_member trigger
SELECT ok(
  EXISTS(
    SELECT 1 
    FROM pg_trigger t
    JOIN pg_class c ON t.tgrelid = c.oid
    JOIN pg_namespace n ON c.relnamespace = n.oid
    WHERE n.nspname = 'public' AND c.relname = 'companies'
    AND t.tgname NOT LIKE 'RI_%' -- Exclude referential integrity triggers
  ),
  'companies table should have a trigger for admin membership creation'
);

-- List all company member creation related triggers
SELECT diag('Company creation triggers (excluding referential integrity):');
SELECT diag(format(' - %s (function: %s)', t.tgname, p.proname)) 
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
JOIN pg_namespace n ON c.relnamespace = n.oid
JOIN pg_proc p ON t.tgfoid = p.oid
WHERE n.nspname = 'public' AND c.relname = 'companies'
AND t.tgname NOT LIKE 'RI_%';

-- Check that a function exists that creates base_users
SELECT ok(
  EXISTS(
    SELECT 1
    FROM pg_proc p
    JOIN pg_namespace n ON p.pronamespace = n.oid
    WHERE p.prosrc LIKE '%INSERT INTO public.base_users%'
  ),
  'A function should exist that creates base_users records'
);

-- Check that the company admin function exists
SELECT ok(
  EXISTS(
    SELECT 1
    FROM pg_proc p
    JOIN pg_namespace n ON p.pronamespace = n.oid
    WHERE n.nspname = 'public'
    AND p.proname LIKE '%admin%'
    AND p.prosrc LIKE '%INSERT INTO%company_members%'
  ),
  'A function exists to insert company_members for company creators'
);

-- Complete the tests
SELECT * FROM finish();

-- Roll back any changes
ROLLBACK; 