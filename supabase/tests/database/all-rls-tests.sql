-- Comprehensive RLS policy tests
BEGIN;

-- Load pgTAP extension
CREATE EXTENSION IF NOT EXISTS pgtap;

CREATE SCHEMA IF NOT EXISTS tests;

-- function to create a test user in the "auth.users" table and "auth.identities" for JWT tests
CREATE OR REPLACE FUNCTION tests.create_supabase_user(identifier text DEFAULT 'test_user', email text DEFAULT NULL, phone text DEFAULT NULL, metadata jsonb DEFAULT NULL)
    RETURNS uuid
    LANGUAGE plpgsql
    SECURITY DEFINER
    SET search_path = auth, pg_temp
AS $$
DECLARE
    uuid            uuid;
    email_identity  uuid;
    phone_identity  uuid;
    raw_user_meta   jsonb;
BEGIN
    -- ID for the user
    uuid := gen_random_uuid();

    -- Default metadata to the user id, email, and phone if provided
    raw_user_meta := jsonb_build_object('sub', uuid::text);
    IF email IS NOT NULL THEN
        raw_user_meta = raw_user_meta || jsonb_build_object('email', email);
    END IF;
    IF phone IS NOT NULL THEN
        raw_user_meta = raw_user_meta || jsonb_build_object('phone', phone);
    END IF;
    IF metadata IS NOT NULL THEN
        raw_user_meta = raw_user_meta || metadata;
    END IF;

    -- Insert user data into auth.users
    INSERT INTO auth.users (id, email, phone, raw_user_meta_data, raw_app_meta_data)
    VALUES (uuid, email, phone, raw_user_meta, '{}'::jsonb);

    -- Create an identity for the email if provided
    IF email IS NOT NULL THEN
        email_identity := gen_random_uuid();
        INSERT INTO auth.identities (id, provider_id, user_id, identity_data, last_sign_in_at, created_at, updated_at)
        VALUES (email_identity, 'email', uuid, jsonb_build_object('sub', uuid, 'email', email), now(), now(), now());
    END IF;

    -- Create an identity for the phone if provided
    IF phone IS NOT NULL THEN
        phone_identity := gen_random_uuid();
        INSERT INTO auth.identities (id, provider_id, user_id, identity_data, last_sign_in_at, created_at, updated_at)
        VALUES (phone_identity, 'phone', uuid, jsonb_build_object('sub', uuid, 'phone', phone), now(), now(), now());
    END IF;

    RETURN uuid;
END;
$$;

-- function to get a user's id from an identifier
CREATE OR REPLACE FUNCTION tests.get_supabase_uid(identifier text)
    RETURNS uuid
    LANGUAGE plpgsql
    SECURITY DEFINER
    SET search_path = auth, pg_temp
AS $$
DECLARE
    uuid uuid;
BEGIN
    SELECT id INTO uuid FROM auth.users WHERE raw_user_meta_data->>'sub' = identifier;
    RETURN uuid;
END;
$$;

-- function to get a user's jwt from an identifier
CREATE OR REPLACE FUNCTION tests.get_supabase_user(identifier text)
    RETURNS json
    LANGUAGE plpgsql
    SECURITY DEFINER
    SET search_path = auth, pg_temp
AS $$
DECLARE
    usr json;
BEGIN
    SELECT json_build_object(
             'id', id,
             'email', email,
             'phone', phone,
             'raw_user_meta_data', raw_user_meta_data,
             'raw_app_meta_data', raw_app_meta_data,
             'is_anonymous', is_anonymous,
             'created_at', created_at,
             'updated_at', updated_at,
             'last_sign_in_at', last_sign_in_at,
             'banned_until', banned_until,
             'confirmed_at', confirmed_at,
             'confirmation_token', confirmation_token,
             'confirmation_sent_at', confirmation_sent_at,
             'recovery_token', recovery_token,
             'recovery_sent_at', recovery_sent_at,
             'email_change_token', email_change_token,
             'email_change', email_change,
             'email_change_sent_at', email_change_sent_at,
             'reauthentication_token', reauthentication_token,
             'reauthentication_sent_at', reauthentication_sent_at
        ) INTO usr
    FROM auth.users
    WHERE raw_user_meta_data->>'sub' = identifier;
    RETURN usr;
END;
$$;

-- function to authenticate as a user within the tests
CREATE OR REPLACE FUNCTION tests.authenticate_as(identifier text)
    RETURNS void
    LANGUAGE plpgsql
    SECURITY DEFINER
    SET search_path = auth, pg_temp
AS $$
DECLARE
    user_id uuid;
BEGIN
    -- Get the user id for the identifier
    SELECT tests.get_supabase_uid(identifier) INTO user_id;

    -- Set the request.jwt.claims to the user
    PERFORM set_config('request.jwt.claims', json_build_object('sub', user_id::text, 'role', 'authenticated')::text, false);
END;
$$;

-- function to clear the authentication
CREATE OR REPLACE FUNCTION tests.clear_authentication()
    RETURNS void
    LANGUAGE plpgsql
    SECURITY DEFINER
    SET search_path = auth, pg_temp
AS $$
BEGIN
    -- Set the request.jwt.claims to an anonymous user
    PERFORM set_config('request.jwt.claims', json_build_object('role', 'anon')::text, false);
END;
$$;

-- function to check if RLS is enabled on a table
CREATE OR REPLACE FUNCTION tests.rls_enabled(schema_name text, table_name text)
    RETURNS boolean
    LANGUAGE plpgsql
    SECURITY DEFINER
    SET search_path = pg_catalog, pg_temp
AS $$
DECLARE
    rls_enabled bool;
BEGIN
    SELECT relrowsecurity INTO rls_enabled
    FROM pg_class
    WHERE oid = (schema_name || '.' || table_name)::regclass;

    RETURN rls_enabled;
END;
$$;

-- function to check if RLS is enabled on all tables in a schema
CREATE OR REPLACE FUNCTION tests.rls_enabled(schema_name text)
    RETURNS text[]
    LANGUAGE plpgsql
    SECURITY DEFINER
    SET search_path = pg_catalog, pg_temp
AS $$
DECLARE
    failed_tables text[];
    table_name text;
    rls_enabled bool;
BEGIN
    failed_tables := ARRAY[]::text[];

    FOR table_name IN
        SELECT c.relname
        FROM pg_class c
            JOIN pg_namespace n ON n.oid = c.relnamespace
        WHERE n.nspname = schema_name
            AND c.relkind = 'r'
            AND c.relname != 'schema_migrations'
            AND c.relname != 'roles'
    LOOP
        SELECT c.relrowsecurity INTO rls_enabled
        FROM pg_class c
        WHERE c.oid = (schema_name || '.' || table_name)::regclass;

        IF NOT rls_enabled THEN
            failed_tables := array_append(failed_tables, table_name);
        END IF;
    END LOOP;

    RETURN failed_tables;
END;
$$;

-- Plan all the tests (20 total)
SELECT plan(20);

-- Test group 1: RLS enabled check
SELECT diag('Test Group 1: Checking if RLS is enabled on all tables');
DO $$
DECLARE
    failed_tables text[];
BEGIN
    failed_tables := tests.rls_enabled('public');
    
    IF array_length(failed_tables, 1) IS NULL THEN
        PERFORM ok(true, 'Row Level Security enabled on all tables in public schema');
    ELSE
        PERFORM ok(false, format('Row Level Security not enabled on tables in public schema: %s', array_to_string(failed_tables, ', ')));
    END IF;
END $$;

-- Test group 2: Companies RLS policies
SELECT diag('Test Group 2: Companies RLS policies');

-- Create test users for companies tests
SELECT tests.create_supabase_user('company_creator');
SELECT tests.create_supabase_user('company_admin');
SELECT tests.create_supabase_user('company_member');
SELECT tests.create_supabase_user('non_member');

-- Get user IDs and set variables
DO $$
DECLARE
    company_creator_id UUID;
    company_admin_id UUID;
    company_member_id UUID;
    non_member_id UUID;
    company_id UUID;
BEGIN
    SELECT tests.get_supabase_uid('company_creator') INTO company_creator_id;
    SELECT tests.get_supabase_uid('company_admin') INTO company_admin_id;
    SELECT tests.get_supabase_uid('company_member') INTO company_member_id;
    SELECT tests.get_supabase_uid('non_member') INTO non_member_id;

    -- Create a test company
    PERFORM tests.authenticate_as('company_creator');
    INSERT INTO "public"."companies" (name, status, created_by)
    VALUES ('Test Company', 'trial', company_creator_id)
    RETURNING id INTO company_id;

    -- Add members to the company
    INSERT INTO "public"."company_members" (company_id, user_id, role, display_name, is_active)
    VALUES 
    (company_id, company_creator_id, 'admin', 'Creator Admin', true),
    (company_id, company_admin_id, 'admin', 'Another Admin', true),
    (company_id, company_member_id, 'member', 'Regular Member', true);

    -- Test 2.1: Company creator can see their company
    PERFORM results_eq(
        format('SELECT count(*) FROM "public"."companies" WHERE id = ''%s''', company_id),
        'VALUES(1::bigint)',
        'Company creator can see their company'
    );

    -- Test 2.2: Company admin can see the company
    PERFORM tests.authenticate_as('company_admin');
    PERFORM results_eq(
        format('SELECT count(*) FROM "public"."companies" WHERE id = ''%s''', company_id),
        'VALUES(1::bigint)',
        'Company admin can see the company'
    );

    -- Test 2.3: Company member can see the company
    PERFORM tests.authenticate_as('company_member');
    PERFORM results_eq(
        format('SELECT count(*) FROM "public"."companies" WHERE id = ''%s''', company_id),
        'VALUES(1::bigint)',
        'Company member can see the company'
    );

    -- Test 2.4: Non-member cannot see the company
    PERFORM tests.authenticate_as('non_member');
    PERFORM results_eq(
        format('SELECT count(*) FROM "public"."companies" WHERE id = ''%s''', company_id),
        'VALUES(0::bigint)',
        'Non-member cannot see the company'
    );

    -- Test 2.5: Anonymous user cannot see the company
    PERFORM tests.clear_authentication();
    PERFORM results_eq(
        format('SELECT count(*) FROM "public"."companies" WHERE id = ''%s''', company_id),
        'VALUES(0::bigint)',
        'Anonymous user cannot see the company'
    );

    -- Test 2.6: Company creator can update their own company
    PERFORM tests.authenticate_as('company_creator');
    PERFORM results_eq(
        format('UPDATE "public"."companies" SET name = ''Updated Test Company'' WHERE id = ''%s'' RETURNING name', company_id),
        'VALUES(''Updated Test Company'')',
        'Company creator can update their own company'
    );

    -- Test 2.7: Company admin (not creator) can update the company via RLS membership policy
    PERFORM tests.authenticate_as('company_admin');
    PERFORM results_eq(
        format('UPDATE "public"."companies" SET name = ''Admin Updated Company'' WHERE id = ''%s'' RETURNING name', company_id),
        'VALUES(''Admin Updated Company'')',
        'Company admin can update the company'
    );

    -- Test 2.8: Regular member cannot update the company
    PERFORM tests.authenticate_as('company_member');
    PERFORM is_empty(
        format('UPDATE "public"."companies" SET name = ''Member Updated Company'' WHERE id = ''%s'' RETURNING name', company_id),
        'Regular member cannot update the company'
    );
END $$;

-- Test group 3: Company Members RLS policies
SELECT diag('Test Group 3: Company Members RLS policies');

-- Create test users for company members tests
SELECT tests.create_supabase_user('admin_user');
SELECT tests.create_supabase_user('member_user');
SELECT tests.create_supabase_user('non_admin_user');
SELECT tests.create_supabase_user('inactive_member_user');
SELECT tests.create_supabase_user('other_admin_user');
SELECT tests.create_supabase_user('other_member_user');

-- Get user IDs and run tests
DO $$
DECLARE
    admin_user_id UUID;
    member_user_id UUID;
    non_admin_user_id UUID;
    inactive_member_user_id UUID;
    other_admin_user_id UUID;
    other_member_user_id UUID;
    company_id_1 UUID;
    company_id_2 UUID;
BEGIN
    -- Get the user IDs
    SELECT tests.get_supabase_uid('admin_user') INTO admin_user_id;
    SELECT tests.get_supabase_uid('member_user') INTO member_user_id;
    SELECT tests.get_supabase_uid('non_admin_user') INTO non_admin_user_id;
    SELECT tests.get_supabase_uid('inactive_member_user') INTO inactive_member_user_id;
    SELECT tests.get_supabase_uid('other_admin_user') INTO other_admin_user_id;
    SELECT tests.get_supabase_uid('other_member_user') INTO other_member_user_id;

    -- Create company1 with admin_user as the creator
    PERFORM tests.authenticate_as('admin_user');
    INSERT INTO "public"."companies" (name, status, created_by)
    VALUES ('Company One', 'trial', admin_user_id)
    RETURNING id INTO company_id_1;

    -- Create company2 with other_admin_user as the creator
    PERFORM tests.authenticate_as('other_admin_user');
    INSERT INTO "public"."companies" (name, status, created_by)
    VALUES ('Company Two', 'trial', other_admin_user_id)
    RETURNING id INTO company_id_2;

    -- Add members to company1
    PERFORM tests.authenticate_as('admin_user');
    INSERT INTO "public"."company_members" (company_id, user_id, role, display_name, is_active)
    VALUES 
    (company_id_1, admin_user_id, 'admin', 'Admin User', true),
    (company_id_1, member_user_id, 'member', 'Active Member', true),
    (company_id_1, non_admin_user_id, 'member', 'Non-Admin User', true),
    (company_id_1, inactive_member_user_id, 'member', 'Inactive Member', false);

    -- Add members to company2
    PERFORM tests.authenticate_as('other_admin_user');
    INSERT INTO "public"."company_members" (company_id, user_id, role, display_name, is_active)
    VALUES 
    (company_id_2, other_admin_user_id, 'admin', 'Other Admin User', true),
    (company_id_2, other_member_user_id, 'member', 'Other Active Member', true);

    -- Test 3.1: Admin User Can See All Members in Their Company
    PERFORM tests.authenticate_as('admin_user');
    PERFORM results_eq(
        format('SELECT count(*) FROM "public"."company_members" WHERE company_id = ''%s''', company_id_1),
        'VALUES(4::bigint)',
        'Admin user can see all members in their company'
    );

    -- Test 3.2: Admin User Cannot See Members in Other Companies
    PERFORM results_eq(
        format('SELECT count(*) FROM "public"."company_members" WHERE company_id = ''%s''', company_id_2),
        'VALUES(0::bigint)',
        'Admin user cannot see members in other companies'
    );

    -- Test 3.3: Regular Member Can See All Members in Their Company
    PERFORM tests.authenticate_as('member_user');
    PERFORM results_eq(
        format('SELECT count(*) FROM "public"."company_members" WHERE company_id = ''%s''', company_id_1),
        'VALUES(4::bigint)',
        'Regular member can see all members in their company'
    );

    -- Test 3.4: Admin User Can Update Active Company Member
    PERFORM tests.authenticate_as('admin_user');
    PERFORM results_eq(
        format('UPDATE "public"."company_members" SET display_name = ''Updated Name'' WHERE user_id = ''%s'' AND company_id = ''%s'' RETURNING display_name', member_user_id, company_id_1),
        'VALUES(''Updated Name'')',
        'Admin user can update active company member'
    );

    -- Test 3.5: Non-Admin User Cannot Update Company Member
    PERFORM tests.authenticate_as('non_admin_user');
    PERFORM is_empty(
        format('UPDATE "public"."company_members" SET display_name = ''Should Fail'' WHERE user_id = ''%s'' AND company_id = ''%s'' RETURNING display_name', member_user_id, company_id_1),
        'Non-admin user cannot update company member'
    );

    -- Test 3.6: User Can Update Their Own Member Record
    PERFORM results_eq(
        format('UPDATE "public"."company_members" SET display_name = ''Self Updated'' WHERE user_id = ''%s'' AND company_id = ''%s'' RETURNING display_name', non_admin_user_id, company_id_1),
        'VALUES(''Self Updated'')',
        'User can update their own member record'
    );

    -- Test 3.7: Admin User Cannot Update Themselves Through Admin Policy
    PERFORM tests.authenticate_as('admin_user');
    -- First, try updating through the admin policy
    PERFORM is_empty(
        format('UPDATE "public"."company_members" SET display_name = ''Should Fail'' WHERE user_id = ''%s'' AND company_id = ''%s'' AND EXISTS (
            SELECT 1 FROM company_members cm
            WHERE cm.user_id = ''%s'' AND cm.role = ''admin''
        ) RETURNING display_name', admin_user_id, company_id_1, admin_user_id),
        'Admin user cannot update themselves through admin policy'
    );

    -- Test 3.8: Admin User Can Update Themselves Through Self-Update Policy
    PERFORM results_eq(
        format('UPDATE "public"."company_members" SET display_name = ''Self Admin Update'' WHERE user_id = ''%s'' AND company_id = ''%s'' RETURNING display_name', admin_user_id, company_id_1),
        'VALUES(''Self Admin Update'')',
        'Admin user can update themselves through self-update policy'
    );

    -- Test 3.9: Admin User from Company 1 Cannot Update Member from Company 2
    PERFORM is_empty(
        format('UPDATE "public"."company_members" SET display_name = ''Should Fail'' WHERE user_id = ''%s'' AND company_id = ''%s'' RETURNING display_name', other_member_user_id, company_id_2),
        'Admin user from Company 1 cannot update member from Company 2'
    );

    -- Test 3.10: Admin User Cannot Update Inactive Member
    PERFORM is_empty(
        format('UPDATE "public"."company_members" SET display_name = ''Should Fail'' WHERE user_id = ''%s'' AND company_id = ''%s'' RETURNING display_name', inactive_member_user_id, company_id_1),
        'Admin user cannot update inactive member'
    );

    -- Test 3.11: Admin Can Delete a Member From Their Company
    PERFORM results_eq(
        format('DELETE FROM "public"."company_members" WHERE user_id = ''%s'' AND company_id = ''%s'' RETURNING user_id::text', member_user_id, company_id_1),
        format('VALUES(''%s'')', member_user_id),
        'Admin can delete a member from their company'
    );
END $$;

SELECT * FROM finish();

ROLLBACK; 