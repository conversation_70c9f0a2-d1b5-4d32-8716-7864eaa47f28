-- Test file for companies table RLS policies
BEGIN;

-- We don't need to install the extension since we have the functions in 00-supabase-test-helpers.sql
-- CREATE EXTENSION IF NOT EXISTS "basejump-supabase_test_helpers";

select plan(8);

-- Create test users
select tests.create_supabase_user('company_creator');
select tests.create_supabase_user('company_admin');
select tests.create_supabase_user('company_member');
select tests.create_supabase_user('non_member');

-- Get user IDs
SELECT tests.get_supabase_uid('company_creator') as company_creator_id,
       tests.get_supabase_uid('company_admin') as company_admin_id,
       tests.get_supabase_uid('company_member') as company_member_id,
       tests.get_supabase_uid('non_member') as non_member_id
INTO company_creator_id, company_admin_id, company_member_id, non_member_id;

-- Create a test company
select tests.authenticate_as('company_creator');
INSERT INTO "public"."companies" (name, status, created_by)
VALUES ('Test Company', 'trial', company_creator_id)
RETURNING id INTO company_id;

-- Add members to the company
INSERT INTO "public"."company_members" (company_id, user_id, role, display_name, is_active)
VALUES 
(company_id, company_creator_id, 'admin', 'Creator Admin', true),
(company_id, company_admin_id, 'admin', 'Another Admin', true),
(company_id, company_member_id, 'member', 'Regular Member', true);

-- Test 1: Company creator can see their company
SELECT
    results_eq(
        $$ SELECT count(*) FROM "public"."companies" WHERE id = '$$|| company_id ||$$' $$,
        $$ VALUES(1::bigint) $$,
        'Company creator can see their company'
    );

-- Test 2: Company admin can see the company
select tests.authenticate_as('company_admin');
SELECT
    results_eq(
        $$ SELECT count(*) FROM "public"."companies" WHERE id = '$$|| company_id ||$$' $$,
        $$ VALUES(1::bigint) $$,
        'Company admin can see the company'
    );

-- Test 3: Company member can see the company
select tests.authenticate_as('company_member');
SELECT
    results_eq(
        $$ SELECT count(*) FROM "public"."companies" WHERE id = '$$|| company_id ||$$' $$,
        $$ VALUES(1::bigint) $$,
        'Company member can see the company'
    );

-- Test 4: Non-member cannot see the company
select tests.authenticate_as('non_member');
SELECT
    results_eq(
        $$ SELECT count(*) FROM "public"."companies" WHERE id = '$$|| company_id ||$$' $$,
        $$ VALUES(0::bigint) $$,
        'Non-member cannot see the company'
    );

-- Test 5: Anonymous user cannot see the company
select tests.clear_authentication();
SELECT
    results_eq(
        $$ SELECT count(*) FROM "public"."companies" WHERE id = '$$|| company_id ||$$' $$,
        $$ VALUES(0::bigint) $$,
        'Anonymous user cannot see the company'
    );

-- Test 6: Company creator can update their own company
select tests.authenticate_as('company_creator');
SELECT
    results_eq(
        $$ UPDATE "public"."companies" SET name = 'Updated Test Company' WHERE id = '$$|| company_id ||$$' RETURNING name $$,
        $$ VALUES('Updated Test Company') $$,
        'Company creator can update their own company'
    );

-- Test 7: Company admin (not creator) can update the company via RLS membership policy
select tests.authenticate_as('company_admin');
SELECT
    results_eq(
        $$ UPDATE "public"."companies" SET name = 'Admin Updated Company' WHERE id = '$$|| company_id ||$$' RETURNING name $$,
        $$ VALUES('Admin Updated Company') $$,
        'Company admin can update the company'
    );

-- Test 8: Regular member cannot update the company
select tests.authenticate_as('company_member');
SELECT
    is_empty(
        $$ UPDATE "public"."companies" SET name = 'Member Updated Company' WHERE id = '$$|| company_id ||$$' RETURNING name $$,
        'Regular member cannot update the company'
    );

select * from finish();

ROLLBACK; 