-- Setup file for Basejump test helpers
BEGIN;

-- Install Basejump test helpers
CREATE EXTENSION IF NOT EXISTS "basejump-supabase_test_helpers";

-- We have to run some tests to get this to pass as the first test file.
select plan(7);
select function_returns('tests', 'create_supabase_user', Array['text', 'text', 'text', 'jsonb'], 'uuid');
select function_returns('tests', 'get_supabase_uid', Array['text'], 'uuid');
select function_returns('tests', 'get_supabase_user', Array['text'], 'json');
select function_returns('tests', 'authenticate_as', Array['text'], 'void');
select function_returns('tests', 'clear_authentication', Array[null], 'void');
select function_returns('tests', 'rls_enabled', Array['text', 'text'], 'text');
select function_returns('tests', 'rls_enabled', Array['text'], 'text');
select * from finish();

ROLLBACK; 