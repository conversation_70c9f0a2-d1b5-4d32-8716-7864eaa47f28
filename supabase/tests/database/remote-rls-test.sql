-- Test to verify <PERSON><PERSON> is enabled on tables in the remote database
BEGIN;

-- Load pgTAP extension
CREATE EXTENSION IF NOT EXISTS pgtap;

-- Plan tests - one for table existence and one for RLS check per table
SELECT plan(6);

-- Check the existence of the tables
SELECT has_table('public', 'base_users', 'Should have base_users table');
SELECT has_table('public', 'companies', 'Should have companies table'); 
SELECT has_table('public', 'company_members', 'Should have company_members table');

-- Check that RLS is enabled on these tables using the system catalog
SELECT ok(
    EXISTS (
        SELECT 1 
        FROM pg_class c
        JOIN pg_namespace n ON n.oid = c.relnamespace
        WHERE n.nspname = 'public'
        AND c.relname = 'base_users'
        AND c.relrowsecurity = true
    ),
    'RLS should be enabled on base_users table'
);

SELECT ok(
    EXISTS (
        SELECT 1 
        FROM pg_class c
        JOIN pg_namespace n ON n.oid = c.relnamespace
        WHERE n.nspname = 'public'
        AND c.relname = 'companies'
        AND c.relrowsecurity = true
    ),
    'RLS should be enabled on companies table'
);

SELECT ok(
    EXISTS (
        SELECT 1 
        FROM pg_class c
        JOIN pg_namespace n ON n.oid = c.relnamespace
        WHERE n.nspname = 'public'
        AND c.relname = 'company_members'
        AND c.relrowsecurity = true
    ),
    'RLS should be enabled on company_members table'
);

-- Check the policies on these tables (uncomment and adjust when ready to test specific policies)
-- SELECT policies_are(
--     'public',
--     'companies',
--     ARRAY [
--         'Company members can view their companies',
--         'Company admins can update their companies',
--         'Company admins can delete their companies'
--     ],
--     'companies table should have the expected policies'
-- );

-- SELECT policies_are(
--     'public',
--     'company_members',
--     ARRAY [
--         'Users can view members of their companies',
--         'Company admins can update members',
--         'Company admins can delete members'
--     ],
--     'company_members table should have the expected policies'
-- );

-- Finish the test
SELECT * FROM finish();

ROLLBACK; 