-- Test to verify RLS policies on tables in the actual database
BEGIN;

-- Load pgTAP extension
CREATE EXTENSION IF NOT EXISTS pgtap;

-- Plan tests - one for each table's policies
SELECT plan(3);

-- Check policies on base_users table
SELECT policies_are(
    'public',
    'base_users',
    ARRAY [
        'Users can view their own profile',
        'Users can update their own profile',
        'Base users visibility policy',
        'Users can update their own base profile'
    ],
    'base_users table should have the expected policies'
);

-- Check policies on companies table
SELECT policies_are(
    'public',
    'companies',
    ARRAY [
        'Company members can view their companies',
        'Allow authenticated users to create companies',
        'Allow authenticated users to delete their own companies',
        'Allow authenticated users to select their own companies',
        'Allow authenticated users to update their own companies',
        'Members can view their companies'
    ],
    'companies table should have the expected policies'
);

-- Check policies on company_members table
SELECT policies_are(
    'public',
    'company_members',
    ARRAY [
        'Allow admins to delete company members',
        'Allow admins to update company members',
        'Allow company admins to add members',
        'Allow active company members to view other members',
        'Allow users to create their own membership',
        'Allow users to delete their own company membership',
        'Allow users to update their own company membership',
        'Allow users to view their own company memberships',
        'Users can view members of their companies'
    ],
    'company_members table should have the expected policies'
);

-- Finish the test
SELECT * FROM finish();

ROLLBACK; 