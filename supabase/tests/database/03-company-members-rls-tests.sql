-- Test file for company_members table RLS policies
BEGIN;

-- We don't need to install the extension since we have the functions in 00-supabase-test-helpers.sql
-- CREATE EXTENSION IF NOT EXISTS "basejump-supabase_test_helpers";

select plan(11);

-- Create test users
select tests.create_supabase_user('admin_user');
select tests.create_supabase_user('member_user');
select tests.create_supabase_user('non_admin_user');
select tests.create_supabase_user('inactive_member_user');
select tests.create_supabase_user('other_admin_user');
select tests.create_supabase_user('other_member_user');

-- Get user IDs
SELECT tests.get_supabase_uid('admin_user') as admin_user_id,
       tests.get_supabase_uid('member_user') as member_user_id,
       tests.get_supabase_uid('non_admin_user') as non_admin_user_id,
       tests.get_supabase_uid('inactive_member_user') as inactive_member_user_id,
       tests.get_supabase_uid('other_admin_user') as other_admin_user_id,
       tests.get_supabase_uid('other_member_user') as other_member_user_id
INTO admin_user_id, member_user_id, non_admin_user_id, inactive_member_user_id, other_admin_user_id, other_member_user_id;

-- Create company1 with admin_user as the creator
select tests.authenticate_as('admin_user');
INSERT INTO "public"."companies" (name, status, created_by)
VALUES ('Company One', 'trial', admin_user_id)
RETURNING id INTO company_id_1;

-- Create company2 with other_admin_user as the creator
select tests.authenticate_as('other_admin_user');
INSERT INTO "public"."companies" (name, status, created_by)
VALUES ('Company Two', 'trial', other_admin_user_id)
RETURNING id INTO company_id_2;

-- Add members to company1
select tests.authenticate_as('admin_user');
INSERT INTO "public"."company_members" (company_id, user_id, role, display_name, is_active)
VALUES 
(company_id_1, admin_user_id, 'admin', 'Admin User', true),
(company_id_1, member_user_id, 'member', 'Active Member', true),
(company_id_1, non_admin_user_id, 'member', 'Non-Admin User', true),
(company_id_1, inactive_member_user_id, 'member', 'Inactive Member', false);

-- Add members to company2
select tests.authenticate_as('other_admin_user');
INSERT INTO "public"."company_members" (company_id, user_id, role, display_name, is_active)
VALUES 
(company_id_2, other_admin_user_id, 'admin', 'Other Admin User', true),
(company_id_2, other_member_user_id, 'member', 'Other Active Member', true);

-- Test 1: Admin User Can See All Members in Their Company
select tests.authenticate_as('admin_user');
SELECT
    results_eq(
        $$ SELECT count(*) FROM "public"."company_members" WHERE company_id = '$$|| company_id_1 ||$$' $$,
        $$ VALUES(4::bigint) $$,
        'Admin user can see all members in their company'
    );

-- Test 2: Admin User Cannot See Members in Other Companies
SELECT
    results_eq(
        $$ SELECT count(*) FROM "public"."company_members" WHERE company_id = '$$|| company_id_2 ||$$' $$,
        $$ VALUES(0::bigint) $$,
        'Admin user cannot see members in other companies'
    );

-- Test 3: Regular Member Can See All Members in Their Company
select tests.authenticate_as('member_user');
SELECT
    results_eq(
        $$ SELECT count(*) FROM "public"."company_members" WHERE company_id = '$$|| company_id_1 ||$$' $$,
        $$ VALUES(4::bigint) $$,
        'Regular member can see all members in their company'
    );

-- Test 4: Admin User Can Update Active Company Member
select tests.authenticate_as('admin_user');
SELECT
    results_eq(
        $$ UPDATE "public"."company_members" SET display_name = 'Updated Name' WHERE user_id = '$$|| member_user_id ||$$' AND company_id = '$$|| company_id_1 ||$$' RETURNING display_name $$,
        $$ VALUES('Updated Name') $$,
        'Admin user can update active company member'
    );

-- Test 5: Non-Admin User Cannot Update Company Member
select tests.authenticate_as('non_admin_user');
SELECT
    is_empty(
        $$ UPDATE "public"."company_members" SET display_name = 'Should Fail' WHERE user_id = '$$|| member_user_id ||$$' AND company_id = '$$|| company_id_1 ||$$' RETURNING display_name $$,
        'Non-admin user cannot update company member'
    );

-- Test 6: User Can Update Their Own Member Record
SELECT
    results_eq(
        $$ UPDATE "public"."company_members" SET display_name = 'Self Updated' WHERE user_id = '$$|| non_admin_user_id ||$$' AND company_id = '$$|| company_id_1 ||$$' RETURNING display_name $$,
        $$ VALUES('Self Updated') $$,
        'User can update their own member record'
    );

-- Test 7: Admin User Cannot Update Themselves Through Admin Policy
select tests.authenticate_as('admin_user');
-- First, try updating through the admin policy
SELECT
    is_empty(
        $$ UPDATE "public"."company_members" SET display_name = 'Should Fail' WHERE user_id = '$$|| admin_user_id ||$$' AND company_id = '$$|| company_id_1 ||$$' AND EXISTS (
            SELECT 1 FROM company_members cm
            WHERE cm.user_id = '$$|| admin_user_id ||$$' AND cm.role = 'admin'
        ) RETURNING display_name $$,
        'Admin user cannot update themselves through admin policy'
    );

-- Test 8: Admin User Can Update Themselves Through Self-Update Policy
SELECT
    results_eq(
        $$ UPDATE "public"."company_members" SET display_name = 'Self Admin Update' WHERE user_id = '$$|| admin_user_id ||$$' AND company_id = '$$|| company_id_1 ||$$' RETURNING display_name $$,
        $$ VALUES('Self Admin Update') $$,
        'Admin user can update themselves through self-update policy'
    );

-- Test 9: Admin User from Company 1 Cannot Update Member from Company 2
SELECT
    is_empty(
        $$ UPDATE "public"."company_members" SET display_name = 'Should Fail' WHERE user_id = '$$|| other_member_user_id ||$$' AND company_id = '$$|| company_id_2 ||$$' RETURNING display_name $$,
        'Admin user from Company 1 cannot update member from Company 2'
    );

-- Test 10: Admin User Cannot Update Inactive Member
SELECT
    is_empty(
        $$ UPDATE "public"."company_members" SET display_name = 'Should Fail' WHERE user_id = '$$|| inactive_member_user_id ||$$' AND company_id = '$$|| company_id_1 ||$$' RETURNING display_name $$,
        'Admin user cannot update inactive member'
    );

-- Test 11: Admin Can Delete a Member From Their Company
SELECT
    results_eq(
        $$ DELETE FROM "public"."company_members" WHERE user_id = '$$|| member_user_id ||$$' AND company_id = '$$|| company_id_1 ||$$' RETURNING user_id::text $$,
        $$ VALUES('$$|| member_user_id ||$$') $$,
        'Admin can delete a member from their company'
    );

select * from finish();

ROLLBACK; 