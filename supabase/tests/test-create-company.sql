-- Test file to verify company creation works without infinite recursion
BEGIN;

-- Load pgTAP
CREATE EXTENSION IF NOT EXISTS pgtap;

-- Plan the tests
SELECT plan(2);

-- Clean up any existing test data
DO $$
BEGIN
    -- Use service role for cleanup to bypass RLS
    SET LOCAL ROLE service_role;
    
    -- First delete company members
    DELETE FROM public.company_members 
    WHERE user_id IN (
        SELECT id FROM public.base_users WHERE email LIKE '<EMAIL>'
    );

    -- Then delete companies
    DELETE FROM public.companies 
    WHERE created_by IN (
        SELECT id FROM auth.users WHERE email LIKE '<EMAIL>'
    );

    -- Then delete identities
    DELETE FROM auth.identities 
    WHERE user_id IN (
        SELECT id FROM auth.users WHERE email LIKE '<EMAIL>'
    );

    -- Then delete base_users
    DELETE FROM public.base_users 
    WHERE email LIKE '<EMAIL>';

    -- Finally delete auth users
    DELETE FROM auth.users 
    WHERE email LIKE '<EMAIL>';

    -- Ensure we have no leftover test users
    ASSERT (
        SELECT COUNT(*) FROM auth.users WHERE email LIKE '<EMAIL>'
    ) = 0, 'All test users should be deleted';
END $$;

-- Create a test user and company
DO $$
DECLARE
    test_user_id UUID := gen_random_uuid();
    test_email TEXT := 'test-user-' || test_user_id || '@example.com';
    test_company_name TEXT := 'Test Company ' || gen_random_uuid();
    created_company_id UUID;
    test_result BOOLEAN;
BEGIN
    -- Use service role for all operations to bypass RLS
    SET LOCAL ROLE service_role;
    
    -- Create test user in auth.users
    INSERT INTO auth.users (
        id,
        email,
        raw_user_meta_data,
        raw_app_meta_data,
        role,
        email_confirmed_at
    ) VALUES (
        test_user_id,
        test_email,
        jsonb_build_object('sub', test_user_id::text, 'email', test_email),
        '{}'::jsonb,
        'authenticated',
        now()
    );

    -- base_users entry is created automatically by trigger
    
    -- Create a company as the service role
    INSERT INTO public.companies (
        name,
        created_by,
        status,
        is_public
    ) VALUES (
        test_company_name,
        test_user_id,
        'trial',  -- Using the actual enum value
        false
    ) RETURNING id INTO created_company_id;
    
    -- Test 1: Company was created
    PERFORM ok(
        created_company_id IS NOT NULL,
        'Company should be created successfully'
    );
    
    -- Test 2: The after_company_insert trigger created a company member record
    -- Use service role to verify the record exists (bypassing RLS)
    SELECT EXISTS (
        SELECT 1 
        FROM public.company_members cm
        WHERE cm.company_id = created_company_id
        AND cm.user_id = test_user_id
        AND cm.role = 'admin'  -- Using the actual enum value
        AND cm.is_active = true
    ) INTO test_result;
    
    PERFORM ok(
        test_result,
        'Company member record should be created with admin role'
    );
END $$;

-- Finish the tests
SELECT * FROM finish();

ROLLBACK; 