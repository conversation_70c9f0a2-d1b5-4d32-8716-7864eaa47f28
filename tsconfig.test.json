{"extends": "./tsconfig.json", "compilerOptions": {"types": ["node", "jest"], "esModuleInterop": true, "allowSyntheticDefaultImports": true, "moduleResolution": "bundler", "module": "ESNext", "target": "ESNext", "strict": true, "skipLibCheck": true, "isolatedModules": true, "verbatimModuleSyntax": false, "allowJs": true, "resolveJsonModule": true, "lib": ["ESNext", "DOM"], "baseUrl": ".", "paths": {"$lib/*": ["src/lib/*"], "$env/*": ["src/mocks/*"]}}, "include": ["src/**/*.ts", "src/**/*.js", "src/**/*.d.ts"], "exclude": ["node_modules"]}