# Policy Dependencies Analysis

## Views to Preserve
1. `company_user_profiles`
   - Critical view for user management
   - Used in multiple migrations and current schema
   - No function dependencies found

2. `company_users_extended`
   - Used for extended company user information
   - No function dependencies found

3. `view_totm_inbox_questions_responses`
   - Used for Q&A system
   - No direct function dependencies found

## RLS Policies Analysis

### Safe to Remove
No RLS policies found that depend on the functions we plan to remove. The policies primarily use:
- `auth.uid()`
- EXISTS clauses with subqueries
- Direct column comparisons

### Key Policy Categories
1. **Document Access**
   - All document-related policies use EXISTS clauses
   - No function dependencies found

2. **Company Management**
   - Company admin policies use EXISTS with subqueries
   - No dependencies on deprecated functions

3. **User Management**
   - Uses `auth.uid()` directly
   - No custom function dependencies

4. **File Access**
   - Uses EXISTS clauses with company membership checks
   - No function dependencies

## Findings

1. **No Direct Dependencies**
   - None of the functions marked for removal are used in RLS policies
   - Views don't rely on the deprecated functions

2. **Safe to Remove**
   - All identified functions can be safely removed
   - No impact on existing RLS policies
   - No impact on view definitions

3. **Verification Steps**
   - Double-checked all policy definitions
   - Verified view dependencies
   - Confirmed no hidden function usage in subqueries

## Recommendations

1. Proceed with function removal as planned
2. Include thorough testing of:
   - Document access permissions
   - Company member management
   - File access controls
3. Keep monitoring logs during initial deployment 