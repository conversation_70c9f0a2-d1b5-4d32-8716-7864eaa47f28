{"issues": [], "patterns": [{"id": "db_testing_schema_rule", "type": "success", "pattern": "Test against actual database schemas, never create test schemas", "usage": "When writing database tests, always use the actual schemas and rely on transactions for isolation", "examples": ["BEGIN; -- Test against real schema -- ROLLBACK;", "Use SET LOCAL for temporary test settings", "Proper role and permission testing against actual schemas"], "alternatives": ["Using test schemas (discouraged)", "Creating temporary objects (use transaction rollback instead)"], "context": {"framework": "Supabase", "category": "database_testing"}}], "regression_tests": []}