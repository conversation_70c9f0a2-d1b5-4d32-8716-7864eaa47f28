# Function Cleanup Analysis

## Currently Used Functions
These functions are actively used in the codebase and should be preserved:

1. **Document Management**
   - `upsert_document` - Used in ingest.ts and base-processor.ts
   - `match_documents_public` - Used in test-retrieval API
   - `match_documents_by_phone` - Used in WhatsApp search API

2. **Company Management**
   - `handle_company_personalization_upsert` - Used in manage-company page
   - `create_secret` - Used in data-sources API
   - `add_response` - Used in company inbox

3. **Authentication & User Management**
   - `handle_new_user` - Critical auth trigger function
   - `sync_oauth_metadata` - Handles OAuth metadata syncing
   - `current_user_email` - Used in company invites

4. **Storage**
   - `storage.foldername` - Used for storage bucket organization

## Functions to Remove
These functions appear to be unused or deprecated:

1. **Old Q&A System Functions**
   - `add_question`
   - `add_question_providing_user_id`

2. **Deprecated Team Functions**
   - `add_user_to_team`
   - `create_team`

3. **Outdated Document Matching Functions**
   - `match_documents` (older version)
   - `match_documents_with_permissions` (replaced by newer implementations)
   - `match_documents_with_user_and_file_metadata` (superseded)

4. **Deprecated Company Management**
   - `add_user_to_company` (replaced by new company_members system)
   - `update_company_users` (trigger no longer needed)

## Migration Plan

1. **Phase 1: Verification**
   - Double-check all function dependencies
   - Verify no RLS policies depend on functions to be removed
   - Check for any trigger dependencies

2. **Phase 2: Create Migration**
   - Create migration file: `20250318000001_cleanup_unused_functions.sql`
   - Include proper down migration for safety
   - Add detailed comments explaining removals

3. **Phase 3: Testing**
   - Test all affected features after removal
   - Verify company management still works
   - Check document search functionality
   - Validate user authentication flows

## Dependencies to Check

1. **RLS Policies**
   - Review all table policies
   - Check for function usage in policy definitions

2. **Triggers**
   - Audit trigger definitions
   - Verify trigger function dependencies

3. **Views**
   - Check view definitions for function usage

## Next Steps

1. Create the cleanup migration
2. Test in development environment
3. Document changes in changelog
4. Plan production deployment strategy

## Notes

- Keep vector-related extension functions (pgvector provides these)
- Preserve all authentication-related functions
- Maintain backwards compatibility where possible
- Document any breaking changes 