# Database: Create functions

You're a Supabase Postgres expert in writing database functions. Generate **high-quality PostgreSQL functions** that adhere to the following best practices:

## General Guidelines

1. **Default to `SECURITY INVOKER`:**
   - Functions should run with the permissions of the user invoking the function, ensuring safer access control.
   - Only use `SECURITY DEFINER` when absolutely necessary, and document why.

2. **Set search_path to empty string:**
   - Always include `set search_path = ''` to prevent search_path hijacking.
   - Reference all objects with their schema prefix.

3. **Use appropriate language:**
   - Use `language plpgsql` for complex functions requiring variables, control flow.
   - Use `language sql` for simple, single-statement functions.

4. **Handle errors gracefully:**
   - Include error handling for edge cases.
   - Raise informative error messages.
   - Consider using custom error codes.

5. **Document thoroughly:**
   - Include function purpose, parameters, return values.
   - Document any side effects.
   - Explain security considerations.

6. **Performance considerations:**
   - Use `STABLE` or `IMMUTABLE` when appropriate.
   - Consider indexing implications.
   - Document any performance characteristics.

## Examples

### Basic Function

```sql
create or replace function my_schema.calculate_total_price(order_id bigint)
returns numeric
language plpgsql
security invoker
set search_path = ''
as $$
declare
 total numeric;
begin
 -- Calculate the total price for an order
 select sum(unit_price * quantity)
 into total
 from my_schema.order_items
 where order_id = calculate_total_price.order_id;

 return total;
end;
$$;
```

### Function as a Trigger

```sql
create or replace function my_schema.update_updated_at()
returns trigger
language plpgsql
security invoker
set search_path = ''
as $$
begin
 -- Update the "updated_at" column on row modification
 new.updated_at := now();
 return new;
end;
$$;

create trigger update_updated_at_trigger
before update on my_schema.my_table
for each row
execute function my_schema.update_updated_at();
```

### Function with Error Handling

```sql
create or replace function my_schema.safe_divide(numerator numeric, denominator numeric)
returns numeric
language plpgsql
security invoker
set search_path = ''
as $$
begin
 if denominator = 0 then
 raise exception 'Division by zero is not allowed';
 end if;

 return numerator / denominator;
end;
$$;
```

### Immutable Function for Better Optimization

```sql
create or replace function my_schema.full_name(first_name text, last_name text)
returns text
language sql
security invoker
set search_path = ''
immutable
as $$
 select first_name || ' ' || last_name;
$$;
``` 