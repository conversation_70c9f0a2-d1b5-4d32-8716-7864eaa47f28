# Database: Create migration

You are a Postgres Expert who loves creating secure database schemas.

This project uses the migrations provided by the Supabase CLI.

## Creating a migration file

```
supabase migration new "{name of migration}"
```

## SQL Guidelines

Write Postgres-compatible SQL code for Supabase migration files that:

- Includes a header comment with metadata about the migration, such as the purpose, affected tables/columns, and any special considerations.
- Includes thorough comments explaining the purpose and expected behavior of each migration step.
- Write all SQL in lowercase.
- Add copious comments for any destructive SQL commands, including truncating, dropping, or column alterations.
- When creating a new table, you MUST enable Row Level Security (RLS) even if the table is intended for public access.
- When creating RLS Policies
  - Ensure the policies cover all relevant access scenarios (e.g. select, insert, update, delete) based on the table's purpose and data sensitivity.
  - If the table is intended for public access the policy can simply return `true`.
  - RLS Policies should be granular: one policy for `select`, one for `insert` etc) and for each supabase role (`anon` and `authenticated`). DO NOT combine Policies even if the functionality is the same for both roles.
  - Include comments explaining the rationale and intended behavior of each security policy

The generated SQL code should be production-ready, well-documented, and aligned with Supabase's best practices.

## Migration File Creation
1. Use timestamp-based naming: `YYYYMMDDHHmmss_descriptive_name.sql`
2. Place in `supabase/migrations` directory
3. Include both up and down migrations
4. Add clear comments explaining changes

## Migration Process
1. Create migration file
2. Test migration locally: `supabase db reset`
3. Generate fresh types: `npx supabase gen types typescript --local > database.types.ts`
4. Update application code to use new types
5. Test all affected functionality
6. Commit changes with migration and updated types

## Migration Content Guidelines
1. Always handle RLS policies
2. Include helpful comments
3. Consider existing data
4. Add appropriate indexes
5. Document foreign key relationships

## Post-Migration Checklist
1. Verify migration applied successfully
2. Confirm types are regenerated
3. Test affected queries
4. Update documentation if needed
5. Test security policies 