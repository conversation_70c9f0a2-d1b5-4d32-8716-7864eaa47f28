# Database Type Management Rules

## Type Generation
- Always use `npx supabase gen types typescript --local > database.types.ts` to generate types
- Never manually modify database.types.ts
- Types must be regenerated after any schema changes or migrations

## When to Generate Types
1. After running any migration
2. After modifying database schema through Supabase Studio
3. After pulling schema changes from other environments
4. Before starting work on new features that interact with the database

## Migration Process
1. Apply migrations: `supabase db reset`
2. Generate fresh types: `npx supabase gen types typescript --local > database.types.ts`
3. Update affected queries to use new types
4. Test affected routes/components

## Type Usage Best Practices
1. Create minimal intermediate types that match exact query shape
2. Use `as unknown as IntermediateType[]` pattern for safe type casting
3. Remove unnecessary complex types when simpler ones will suffice
4. Prefer querying from views when they exist

## Common Patterns
```typescript
// Example of proper type usage with Supabase query
type QueryResult = {
    specific_field: string;
    nested_data: Database["public"]["Tables"]["table_name"]["Row"];
};

const { data } = await supabase
    .from("view_or_table")
    .select(`
        specific_field,
        nested_data (*)
    `);

const typedData = (data || []) as unknown as QueryResult[];
``` 