# Playwright E2E Testing Guidelines

These rules and patterns ensure consistent, reliable E2E tests based on our implementation experience.

## Recording Workflow

### Authentication Setup

- Create an auth.setup.ts file that logs in and saves authentication state
- Use `npx playwright codegen --load-storage=playwright/.auth/user.json --ignore-https-errors`
- Store auth state in playwright/.auth/user.json
- Use `storageState: 'playwright/.auth/user.json'` in test.use()
- Separate authentication from test logic
- Increase timeouts for auth operations (recommended: 5 minutes)

### Test Recording

- Command: `npx playwright codegen [URL] --load-storage=playwright/.auth/user.json --ignore-https-errors`
- Record the basic happy path first
- Clean up the generated code to make it robust
- Add assertions for key elements
- Test in headless mode to verify

## Test Structure

### Setup

- Use descriptive test names that explain the workflow
- Store test data in global variables for cleanup
- Use timestamps in test data to ensure uniqueness
- Set appropriate timeouts for the entire test

```typescript
// Store company details globally for cleanup
let testCompanyId: string | null = null;
let testCompanyName: string | null = null;

test('company creation and management workflow', async ({ page }) => {
  // Increase test timeout to 3 minutes for the entire workflow
  test.setTimeout(180000);

  // Create a new test company with timestamp to ensure uniqueness
  testCompanyName = `Test Company ${new Date().toISOString().slice(0, 16).replace('T', ' ')}`;
}
```

### Organization

- Divide tests into logical sections with clear comments
- Use try/catch blocks for each major section
- Continue the test even if non-critical sections fail
- Use console.log to provide clear progress information

```typescript
// PART 1: Company Creation
console.log('🔍 Starting company creation test...');

try {
	// Test logic here
	console.log('✅ Company creation test completed successfully');
} catch (error) {
	console.error('❌ Company creation failed:', error);
	throw error; // Fail the test if company creation fails
}

// PART 2: Users page test
console.log('🔍 Starting users page test...');

try {
	// Test logic here
	console.log('✅ Users page test completed successfully');
} catch (error) {
	console.error('❌ Users page test failed:', error);
	// Continue test even if this part fails
}
```

### Cleanup

- Implement test.afterEach() for cleanup operations
- Be prepared for cleanup to fail - provide manual cleanup instructions
- Consider background cleanup scripts for test data instead of in-test cleanup
- Log cleanup status clearly

```typescript
test.afterEach(async ({ page }) => {
	if (testCompanyId && testCompanyName) {
		console.log(`🧹 Test company may need manual deletion: ${testCompanyName} (${testCompanyId})`);
		console.log('💡 To delete manually, go to the company page and delete the test company');

		// We could attempt UI-based deletion, but it's unreliable in headless mode
		// For E2E tests, it's common to have a separate cleanup script that runs periodically
		// to clean up test data, rather than doing it in the test itself
	}
});
```

### Resilience

- Use permissive selectors that won't break with minor UI changes
- Handle expected redirects and authentication flows
- Add reasonable timeouts to all async operations
- Use .first() when multiple elements might match

## Selector Strategies

### Priorities

- Prefer role-based selectors (getByRole) over CSS selectors
- Use data-testid attributes for critical elements
- For text content, use getByText() with case-insensitive regex
- Use .filter() to refine selections when necessary

Example:

```typescript
// Good - Role-based with filter
const currentUserRow = page.getByRole('row').filter({ hasText: 'Admin' }).first();

// Good - Text with regex for case insensitivity
const chatInput = page.getByRole('textbox', { name: /Talk to TOTM|Type a message/i });

// Good - Data test ID when available
const msAccount = page.locator('[data-test-id="<EMAIL>"]');
```

### Dynamic Content

- Use waitForURL with patterns for dynamic URL elements
- Add explicit waits when loading dynamic content
- Check element visibility before interacting with it
- Handle 'not found' cases gracefully

```typescript
// Wait for dynamic URL with regex pattern
await page.waitForURL(
	new RegExp(`/company/${testCompanyId}/vault/.*?/root\\?requestType=navigation`),
	{ timeout: 30000 }
);

// Check visibility before interacting
if (await userMessage.isVisible({ timeout: 5000 }).catch(() => false)) {
	// Proceed with interaction
}
```

## Common Patterns

### Form Submission

- Fill form fields with unique values
- Submit form with button click or Enter press
- Wait for navigation or success indicator
- Verify redirect or success state

### Navigation

- Use navigation links when possible instead of direct URL navigation
- Wait for load state after navigation ('networkidle' recommended)
- Verify current URL contains expected path
- Check for expected elements after navigation

```typescript
// Navigate using UI instead of direct URL
await page.getByRole('link', { name: 'Chat' }).click();

// Wait for load state
await page.waitForLoadState('networkidle');

// Verify URL
expect(page.url()).toContain('/chat');
```

### Data Verification

- Check for specific text or elements that confirm success
- Use permissive assertions for content that might vary
- Log actual values for debugging purposes
- Implement retries for flaky verifications

### Error Handling

- Wrap test sections in try/catch blocks
- Log detailed error information
- Continue test execution for non-critical failures
- Fail the test immediately for critical failures
