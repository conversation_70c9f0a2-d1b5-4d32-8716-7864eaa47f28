# Schema Testing Rules

## Purpose

To ensure consistent and reliable testing against actual database schemas.

## Guidelines

1. Always test against actual database schemas
2. Never create test schemas or duplicate schema objects
3. Use transactions to ensure test isolation
4. Roll back all changes after tests complete

## Examples

```sql
-- ✅ Correct: Testing against actual schema
BEGIN;
-- Test code here
ROLLBACK;

-- ❌ Incorrect: Creating test schema
CREATE SCHEMA tests;
-- Test code here
```

## Best Practices

- Use transactions to maintain database state
- Clean up test data through transaction rollback
- Test against the same schema that production uses
- Avoid creating temporary or test-specific schemas
- Use proper role and permission settings that match production
