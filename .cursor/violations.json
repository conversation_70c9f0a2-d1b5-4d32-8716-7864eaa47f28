{"violations": [{"id": "DRY001", "rule_violated": "DRY principles - Extract repeated logic into reusable utility functions", "detection_date": "2024-02-13T12:00:00Z", "location": {"files": ["src/lib/utils/test1.ts", "src/lib/utils/test2.ts"], "line": "1-7"}, "violation_type": "dry", "description": "Duplicate implementation of formatDate function", "fix_applied": "Consolidated into src/lib/utils/date.ts", "fix_date": "2024-02-13T12:30:00Z", "prevention": "Create shared utility functions in feature-specific files"}, {"id": "STRUCT001", "rule_violated": "Project structure - Components should be in src/lib/components", "detection_date": "2024-02-13T12:00:00Z", "location": {"file": "src/components/Button.svelte", "line": "entire file"}, "violation_type": "structure", "description": "Component created outside of designated components directory", "fix_applied": "Removed incorrect component", "fix_date": "2024-02-13T12:30:00Z", "prevention": "Always create components in src/lib/components directory"}, {"id": "PATTERN001", "rule_violated": "UI Components - Use shadcn-svelte components first", "detection_date": "2024-02-13T12:00:00Z", "location": {"file": "src/lib/components/CustomButton.svelte", "line": "entire file"}, "violation_type": "pattern", "description": "Custom button implementation when shadcn-svelte Button component exists", "fix_applied": "Replaced with shadcn-svelte <PERSON>ton component", "fix_date": "2024-02-13T12:30:00Z", "prevention": "Check shadcn-svelte component library before creating custom components"}]}