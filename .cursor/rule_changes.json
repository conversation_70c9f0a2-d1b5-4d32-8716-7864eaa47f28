{"changes": [{"id": "db_type_management_20250130", "timestamp": "2025-01-30T11:47:39Z", "rule_path": ".cursor/rules/supabase/database-types.md", "previous_value": null, "new_value": "Added comprehensive rules for database type management and generation", "reason": "Ensure consistent type safety and reduce type-related errors", "approved_by": "user_timestamp_20250130114739"}, {"id": "migration_process_20250130", "timestamp": "2025-01-30T11:47:39Z", "rule_path": ".cursor/rules/supabase/database-create-migration.md", "previous_value": "Basic migration guidelines", "new_value": "Updated with type generation requirements and comprehensive process", "reason": "Standardize migration process and maintain type safety", "approved_by": "user_timestamp_20250130114739"}, {"id": "rule_management_20250130", "timestamp": "2025-01-30T11:47:39Z", "rule_path": ".cursorrules", "previous_value": "Manual rule management process", "new_value": "Added automated rule management system with synchronization and documentation", "reason": "Streamline rule updates and ensure consistency across documentation", "approved_by": "user_timestamp_20250130114739", "impact": {"files_affected": [".cursorrules", ".cursor/rule_changes.json", ".cursor/rules/**/*.md"], "improvements": ["Automated rule synchronization", "Standardized documentation format", "Better change tracking", "Validation of rule consistency"]}}, {"id": "rule_testing_playwright_20250317", "timestamp": "2025-03-17T16:55:00Z", "rule_path": ".cursor/rules/testing/playwright.md", "previous_value": null, "new_value": "Added comprehensive guidelines for Playwright E2E testing based on implementation experience", "reason": "Capturing effective patterns for E2E testing with <PERSON>wright, including authentication, test structure, cleanup, and selector strategies", "approved_by": "user-20250317T165500Z"}, {"id": "rule_testing_20240201_001", "timestamp": "2024-02-01T12:00:00Z", "rule_path": ".cursor/rules/testing/schema_rules.md", "previous_value": "No explicit rule about test schema usage", "new_value": "Always test against actual database schemas. Never create test schemas or duplicate schema objects. Use transactions to ensure test isolation.", "reason": "Creating test schemas leads to confusion, inconsistency, and doesn't test against the actual database structure", "approved_by": "user_confirmation_20240201"}], "required_files": {"database": {"type_management": ".cursor/rules/supabase/database-types.md", "migrations": ".cursor/rules/supabase/database-create-migration.md"}}, "meta": {"last_updated": "2025-01-30T11:47:39Z", "version": "1.0.0"}}