#!/usr/bin/env bash

# Enable bash strict mode if running in bash
if [ -n "$BASH_VERSION" ]; then
  # We're in bash, so we can use bash-specific options
  set -eu
  # pipefail is bash-specific
  if [[ "$BASH_VERSION" =~ ^[3-9] ]]; then
    set -o pipefail
  fi
else
  # We're in a more basic shell, use more compatible options
  set -e
fi

# Check if running in a Unix/Linux environment
if [ "$(uname -s)" = "Linux" ] || [ "$(uname -s)" = "Darwin" ] || [ "$(uname -s)" = "FreeBSD" ]; then
  echo "✅ Running in a Unix-like environment"
else
  echo "⚠️ Warning: Not running in a Unix-like environment."
  echo "This script is designed for Linux/Unix systems."
  # Uncomment the next line if you want to enforce Unix/Linux usage
  # exit 1
fi

# Source nvm and use latest Node version if available
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && . "$NVM_DIR/nvm.sh" >/dev/null 2>&1

# Check if nvm command exists and use it
if command -v nvm >/dev/null 2>&1; then
  nvm use --lts >/dev/null 2>&1 || nvm use default >/dev/null 2>&1
fi

echo "🏁 Running pre-commit hook..."

# Skip Node.js version check and just use prettier directly
echo "🔍 Formatting staged files..."
STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(js|ts|jsx|tsx|svelte|css|scss|json|md)$' || echo "")
if [ -n "$STAGED_FILES" ]; then
  for FILE in $STAGED_FILES; do
    # Use without installing globally
    prettier --write "$FILE"
    git add "$FILE"
  done
fi

# Generate Supabase TypeScript types
echo "🔄 Generating Supabase TypeScript types..."

# Wrap Supabase operations in error handling
{
  # Check if Supabase is already running - use existing container if available
  if ! supabase status 2>/dev/null | grep -q "Started"; then
    echo "🚀 Starting Supabase..."
    supabase start --exclude=studio,imgproxy,inbucket,logflare,vector,supavisor
  else
    echo "✓ Using existing Supabase container"
  fi

  # Generate types
  echo "🔄 Generating Supabase types..."
  supabase gen types typescript --local > database.types.ts
  echo "✅ Types generated successfully"

  # Add types file to commit if it has changed
  if git diff --quiet database.types.ts; then
    echo "ℹ️ No changes to database.types.ts detected"
  else
    echo "🔄 Changes to database.types.ts detected, adding to commit"
    git add database.types.ts
  fi
} || {
  echo "⚠️ Supabase operations failed. There might be an issue with your Supabase config."
  echo "You can try running 'supabase status --debug' to troubleshoot."
  echo "Continuing with commit without updating Supabase types..."
}
