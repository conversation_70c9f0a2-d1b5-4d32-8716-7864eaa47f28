#!/usr/bin/env bash

# Simple self-contained pre-commit hook for RLS testing

# Enable error handling
set -e

echo "🏁 Running pre-commit hook (standalone version)..."

# Format staged files
echo "🔍 Formatting staged files..."
STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(js|ts|jsx|tsx|svelte|css|scss|json|md)$' || echo "")
if [ -n "$STAGED_FILES" ]; then
  for FILE in $STAGED_FILES; do
    prettier --write "$FILE" || true
    git add "$FILE"
  done
fi

# Generate Supabase TypeScript types
echo "🔄 Generating Supabase TypeScript types..."
if command -v supabase >/dev/null 2>&1; then
  if ! supabase status 2>/dev/null | grep -q "Started"; then
    echo "🚀 Starting Supabase..."
    supabase start --exclude=studio,imgproxy,inbucket,logflare,vector,supavisor || true
  fi
  
  supabase gen types typescript --local > database.types.ts || true
  
  if ! git diff --quiet database.types.ts; then
    git add database.types.ts
  fi
fi

# Check for Supabase migration or config changes
SUPABASE_CHANGES=$(git diff --cached --name-only | grep -E 'supabase/migrations/|supabase/config.toml' || true)

# If there are changes in target directories, run RLS tests
if [ -n "$SUPABASE_CHANGES" ]; then
  echo "📝 Changes detected in Supabase migration or config files"
  echo "🔒 Running RLS security tests..."
  
  supabase test db \
    supabase/tests/database/01-rls-test.sql \
    supabase/tests/database/02-base-users-rls.sql \
    supabase/tests/database/03-companies-rls.sql \
    supabase/tests/database/04-company-members-rls.sql \
    supabase/tests/database/05-user-creation-trigger.sql \
    supabase/tests/database/06-company-creation-trigger.sql
  
  if [ $? -ne 0 ]; then
    echo "❌ RLS tests failed! Please fix the issues before committing."
    exit 1
  else
    echo "✅ RLS tests passed!"
  fi
fi

exit 0 