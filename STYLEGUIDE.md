# Totm Search Style Guide

This document outlines the coding styles, conventions, and best practices for the Totm Search project. Adhering to this guide ensures consistency, maintainability, and quality across the codebase.

## Table of Contents

1.  [General Principles](#general-principles)
2.  [Code Style](#code-style)
    - [Language & Framework](#language--framework)
    - [Styling](#styling)
    - [Package Management](#package-management)
    - [Code Patterns](#code-patterns)
    - [UI Components](#ui-components)
    - [DRY Principles](#dry-principles)
3.  [Database](#database)
    - [General Database Rules](#general-database-rules)
    - [Type Management](#type-management)
    - [AI Prompts for Database Work](#ai-prompts-for-database-work)
    - [Migrations, Functions, RLS](#migrations-functions-rls)
4.  [Authentication & Authorization](#authentication--authorization)
5.  [Testing](#testing)
    - [Structure](#structure)
    - [Supabase Testing](#supabase-testing)
    - [End-to-End (E2E) Testing](#end-to-end-e2e-testing)
    - [Mocking](#mocking)
    - [CI Integration](#ci-integration)
    - [Test Maintenance](#test-maintenance)
    - [Playwright Workflow](#playwright-workflow)
6.  [Documentation](#documentation)
    - [Living Documentation](#living-documentation)
    - [Learning Persistence](#learning-persistence)
7.  [AI Interaction](#ai-interaction)
8.  [Violation Handling](#violation-handling)

---

## 1. General Principles

- **Consistency:** Follow the established patterns and conventions throughout the codebase.
- **Clarity:** Write code that is easy to understand and maintain.
- **Safety:** Prioritize type safety and security, especially regarding database interactions and authentication.
- **Efficiency:** Strive for efficient code and database queries.

## 2. Code Style

### Language & Framework

- **Language:** TypeScript
- **Framework:** SvelteKit
- **Routing:** Follow SvelteKit's file-based routing conventions. Keep route handlers (`+page.server.ts`, `+server.ts`) focused.
- **State Management:** Prefer SvelteKit stores over complex state management libraries.
- **API Structure:** Use `+server.ts` for API endpoints.
- **Form Handling:** Use Superforms with Zod for validation.

### Styling

- **Library:** TailwindCSS
- **Plugins:** `@tailwindcss/typography`
- **Icons:** Lucide icons (`lucide-svelte`)
- **Consistency:** Verify TailwindCSS classes follow existing project patterns. Refer to `src/app.css` for theme variables and custom utilities.

### Package Management

- **Manager:** PNPM
- **Commands:**
  - Install: `pnpm install`
  - Add: `pnpm add [package]`
  - Add Dev: `pnpm add -D [package]`
  - Remove: `pnpm remove [package]`
  - Update: `pnpm update`
  - Audit: `pnpm audit`
  - Clean Store: `pnpm store prune`
- **Rules:**
  - Always use PNPM.
  - Use exact versions for production dependencies (remove `^` or `~`).
  - Group dev dependencies using the `-D` flag.
  - Maintain a clean `pnpm-lock.yaml`.

### Code Patterns

- **Components:**
  - Location: `src/lib/components`
  - Naming: `PascalCase.svelte`
- **Routes:**
  - Location: `src/routes`
  - Structure: Feature-based
  - Handlers: `+page.svelte`, `+page.server.ts`, `+server.ts`
- **Types:**
  - Location: `src/lib/types`
  - Naming: `camelCase.types.ts` (unless generated, like `database.types.ts`)
  - Organization: Group related types in domain-specific files.
- **Utilities:**
  - Location: `src/lib/utils`
  - Naming: `camelCase.ts`
  - Organization: Group related utilities in feature-specific files.
- **Constants:**
  - Location: `src/lib/constants`
  - Naming: `UPPER_SNAKE_CASE`
  - Organization: Group related constants by feature or domain.

### UI Components

- **Primary Library:** `shadcn-svelte`
- **Installation:** Use the CLI: `pnpm dlx shadcn-svelte@latest add [component-name]`
- **Location:** `src/lib/components/ui`
- **Rules:**
  - Check `shadcn-svelte` first before building custom components.
  - Use the CLI for adding components.
  - Follow its theming (`src/app.css`) and accessibility patterns.
  - Use its form components with Superforms integration.
- **Customization:**
  - Use Tailwind classes first.
  - Extend through composition rather than direct modification.
  - Document extensions.

### DRY (Don't Repeat Yourself) Principles

- Search for existing implementations before creating new ones.
- Extract repeated logic into `src/lib/utils`.
- Create shared components (`src/lib/components/shared`) for repeated UI.
- Use shared types (`src/lib/types`) to avoid duplication.
- Leverage SvelteKit layouts for shared page structures.
- Use constants (`src/lib/constants`) for repeated values.

## 3. Database (Supabase)

### General Database Rules

- **Analysis:** Before modifying database-related code, analyze affected foreign keys and RLS policies.
- **Documentation:** Update `README.md`'s TODO section for database relationship changes and RLS policy documentation.

### Type Management

- **Master File:** `database.types.ts`
- **Generation:** Run `supabase gen types typescript --local > database.types.ts` after:
  - Running migrations.
  - Modifying schema via Supabase Studio.
  - Pulling schema changes.
  - Starting new features interacting with the database.
- **Best Practices:**
  - **Never manually modify `database.types.ts`.**
  - Create minimal intermediate types matching exact query shapes if needed.
  - Use `as unknown as IntermediateType[]` for safe casting when necessary, but prefer type-safe queries.
  - Prefer querying from database views when available and appropriate.
  - Remove unnecessary complex types.

### AI Prompts for Database Work

- **Requirement:** Include relevant AI prompt files using `@file` before asking the AI to perform database-related operations.
- **Files:**
  - SQL Formatting/Style: `.cursor/rules/supabase/code-format-sql.md`
  - Migrations: `.cursor/rules/supabase/database-create-migration.md`
  - Functions: `.cursor/rules/supabase/database-functions.md`
  - RLS Policies: `.cursor/rules/supabase/database-rls-policies.md`
  - Type Management: `.cursor/rules/supabase/database-types.md` (Include when working with types/queries)
- **Example:** `@file .cursor/rules/supabase/database-create-migration.md Create a migration for...`

### Migrations, Functions, RLS

- Strictly follow the guidelines defined in the respective `.cursor/rules/supabase/` files regarding naming, style, security, and required comments. Violations will be flagged.

## 4. Authentication & Authorization

- **RLS:** Maintain and document Supabase RLS policies in `README.md` when modifying auth or user-related code.
- **Analysis:** Before modifying Supabase code, analyze the impact on RLS policies and data access patterns.
- **Testing:** Thoroughly test authentication flows and RLS policies (see [Testing](#testing) section).

## 5. Testing

### Structure

- **E2E:** `tests/e2e/[feature].test.ts` (Playwright)
- **Integration:** `tests/integration/[feature].test.ts` (Supabase/API endpoints)
- **Unit:** `src/**/__tests__/[filename].test.ts` (Components/Utilities)
- **Utils:** `tests/utils/[utility].ts`
- **Fixtures:** `tests/fixtures/`
- **Placement:** Place test files near the code they test when feasible.

### Supabase Testing

- **Environment:** Use a separate test database, reset state before suites, use test-specific RLS.
- **Data:** Clean up test data after each test, use unique identifiers.
- **Auth:** Create unique test users per suite, clean them up, test authenticated/unauthenticated states, session handling (expiration, logout, refresh).
- **RLS:** Test access to own/other's resources, different roles, policy combinations (positive and negative cases).

### End-to-End (E2E) Testing (Playwright)

- **Selectors:** Use `data-testid` attributes, avoid brittle selectors. Prefer `getByRole`, then `getByText`.
- **Workflows:** Test complete user workflows.
- **State:** Handle loading, transition, and error states.
- **Required Tests:** Cover Auth (login, signup, reset), Data (CRUD, search, filter, pagination), UI (responsive, errors, loading).
- **Supabase Integration:**
  - Use test email domains (`@e2e-test.com`).
  - Isolate test data per suite, use prefixes (`prefix_[test_suite]_...`).
  - Clean up users and data thoroughly.
  - Test RLS scenarios (anon, auth, roles, isolation).
- **State Management:** Test store initialization, persistence, synchronization, cleanup.
- **Performance:** Monitor page load, search response, UI interaction times.
- **Error Handling:** Test network failures, DB issues, invalid input, rate limiting.
- **Accessibility:** Test against WCAG 2.1, use Playwright a11y tools, test keyboard navigation, screen reader compatibility.

### Mocking

- **Strategies:** Use test clients/controlled responses for Supabase, mock external APIs, use test-specific environment variables.
- **Best Practices:** Keep mocks realistic, version them with schema changes, document assumptions.

### CI Integration

- **Requirements:** Run all tests in CI, maintain coverage, report results clearly, cache dependencies.
- **Workflow:** Unit -> Integration -> E2E. Parallelize unit/integration tests where possible.

### Test Maintenance

- Update tests/mocks with schema/code changes.
- Review and remove obsolete tests.
- Document test setup, mocks, and patterns.

### Playwright Workflow

- **Authentication:**
  - Use `auth.setup.ts` to save state to `playwright/.auth/user.json`.
  - Load state in tests: `test.use({ storageState: 'playwright/.auth/user.json' });`.
  - Increase auth timeouts (e.g., 5 minutes).
- **Recording:**
  - Use `npx playwright codegen [URL] --load-storage=playwright/.auth/user.json --ignore-https-errors`.
  - Record happy path, then refactor generated code for robustness and add assertions.
- **Test Structure:**
  - Use descriptive names.
  - Use `test.afterEach()` for cleanup; have manual backup plans.
  - Use `try/catch` for major sections, log progress/errors clearly.
- **Selectors:** Prioritize role-based, then `data-testid`, then text. Use `waitForURL`, explicit waits, and visibility checks for dynamic content.
- **Patterns:** Follow established patterns for form submission, navigation, data verification, and error handling within tests.

## 6. Documentation

### Living Documentation

- **Database Schema:** Automatically update `README.md#TODO` with FK relationships/cascade behaviors on schema change.
- **RLS Policies:** Document new/modified RLS policies in `README.md#RLS_Policies`.
- **Dependencies:** Keep `package.json` organized (prod vs. dev).

### Learning Persistence

- **Storage:** `.cursor/lessons.json` tracks issues, patterns, and regression tests.
- **Triggers:** Errors, identified patterns, performance issues, and reported regressions trigger updates to `lessons.json`.
- **Reference:** Before suggesting solutions or encountering errors, check `lessons.json` for historical context. Document new solutions/preventions.

## 7. AI Interaction

- **Supabase Analysis:** Before modifying Supabase code via AI, ensure the AI analyzes impacts on RLS and data access.
- **OpenAI Integration:** Ensure proper error handling and rate limiting when using OpenAI APIs.
- **Database Prompts:** Always include the relevant `.cursor/rules/supabase/` file via `@file` when asking the AI for database-related tasks (SQL, migrations, functions, RLS, types).

## 8. Violation Handling

- **Detection:** The AI assistant will flag violations of these rules (DRY, structure, patterns, critical issues like security/types, migration format, missing AI prompts) using the ⚡ or ❌ emoji.
- **Resolution:** Follow the proposed fix or discuss alternatives. Approved fixes will be implemented.
- **Documentation:** Violations and resolutions are tracked in `.cursor/violations.json`.
- **Prevention:** Lessons learned from violations are added to `lessons.json`, and regression tests may be created.

---

_This style guide is a living document and may be updated as the project evolves._
