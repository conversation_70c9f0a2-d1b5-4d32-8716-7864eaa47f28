/** @type {import('ts-jest').JestConfigWithTsJest} */
export default {
  preset: 'ts-jest/presets/default-esm',
  moduleNameMapper: {
    '^\\$lib/(.*)$': '<rootDir>/src/lib/$1',
    '^\\$env/dynamic/public$': '<rootDir>/src/mocks/dynamic/public.ts',
    '^svelte/(.*)$': ['<rootDir>/src/mocks/svelte/$1.ts'],
    '^svelte$': ['<rootDir>/src/mocks/svelte/index.ts']
  },
  extensionsToTreatAsEsm: ['.ts'],
  transform: {
    '^.+\\.m?[tj]sx?$': ['ts-jest', {
      useESM: true,
      tsconfig: 'tsconfig.test.json',
      isolatedModules: true
    }]
  },
  testEnvironment: 'node',
  setupFiles: ['<rootDir>/src/setupTests.ts'],
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  transformIgnorePatterns: [
    'node_modules/(?!(svelte|@sveltejs)/)'
  ],
  testMatch: [
    '**/__tests__/**/*.[jt]s?(x)',
    '**/?(*.)+(spec|test).[jt]s?(x)'
  ]
}; 