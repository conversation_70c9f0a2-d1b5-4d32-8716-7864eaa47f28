import { describe, test, expect } from 'vitest';
import { createClient } from '@supabase/supabase-js';

// Create a Supabase client for testing
const supabase = createClient(
	'http://localhost:54321', // Local Supabase instance
	'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' // Anon key for testing
);

describe('Upstack platform type and product migration', () => {
	// Check if platform_type enum includes 'upstack'
	test('platform_type enum should include upstack', async () => {
		const { data, error } = await supabase.rpc('get_enum_values', {
			enum_name: 'platform_type'
		});

		expect(error).toBeNull();
		expect(data).toContain('upstack');
	});

	// Check if Upstack product is in the products table
	test('products table should contain Upstack Integration', async () => {
		const { data, error } = await supabase
			.from('products')
			.select('*')
			.eq('name', 'Upstack Integration')
			.single();

		expect(error).toBeNull();
		expect(data).not.toBeNull();
		expect(data?.name).toBe('Upstack Integration');
		expect(data?.category).toBe('Document Storage');
		expect(data?.is_coming_soon).toBe(false);

		// Features should be in an array
		expect(Array.isArray(data?.features)).toBe(true);
		expect(data?.features).toContain('Entity management');
	});
});
