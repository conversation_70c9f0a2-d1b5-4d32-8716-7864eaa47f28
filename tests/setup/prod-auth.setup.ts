import { test as setup } from '@playwright/test';
import * as dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: '.env.test' });

const authFile = path.join('playwright', '.auth', 'prod-user.json');

// Configure global test settings
setup.use({
	headless: false,
	ignoreHTTPSErrors: true,
	viewport: { width: 1280, height: 720 },
	launchOptions: {
		slowMo: 100 // Add a small delay between actions
	}
});

setup('authenticate', async ({ page }) => {
	try {
		// Navigate to the auth page
		console.log('Navigating to production auth page...');
		await page.goto('https://app.totm.space/auth');

		// Wait for the page to load
		console.log('Waiting for page load...');
		await page.waitForLoadState('networkidle');

		// Check if we need to authenticate
		console.log('Checking authentication status...');
		const signInButton = page.getByRole('button', {
			name: 'Sign in with Azure'
		});
		const isSignInVisible = await signInButton.isVisible();

		if (isSignInVisible) {
			console.log('Starting authentication process...');

			// Click the sign in button
			await signInButton.click();
			console.log('Clicked sign in button');

			// Wait for authentication to complete
			console.log('Waiting for authentication (up to 5 minutes)...');

			// Instead of waiting for a specific URL, wait for navigation to complete and
			// then check for elements that indicate successful authentication
			await page.waitForLoadState('networkidle', { timeout: 300000 });

			// Wait for a common element that appears after successful authentication
			// This could be a header, navbar, or main content element
			await page.waitForSelector('[data-button-root]', { timeout: 60000 });

			console.log('Authentication completed successfully');
		} else {
			console.log('Already authenticated');
		}

		// Save signed-in state to 'prod-user.json'
		await page.context().storageState({ path: authFile });
		console.log(`Authentication state saved to ${authFile}`);
	} catch (error) {
		console.error('Error during authentication:', error);
		throw error;
	}
});
