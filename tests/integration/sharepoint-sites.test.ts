import { beforeAll, describe, expect, test, vi } from 'vitest';
import { exchangeRefreshToken, type TokenResponse } from '../../src/lib/server/microsoft';

// Mock environment variables
vi.mock('$env/static/public', () => ({
	PUBLIC_AZURE_CLIENT_ID: '1d6dcf04-3524-4367-8982-d506bdd76462',
	PUBLIC_AZURE_TENANT_ID: '182b06a2-3eb5-411c-9664-bd3b31843244'
}));

vi.mock('$env/static/private', () => ({
	PRIVATE_AZURE_CLIENT_SECRET: '****************************************'
}));

// Mock fetch for token exchange
const mockTokenResponse: TokenResponse = {
	access_token: 'mock_access_token',
	refresh_token: 'mock_refresh_token',
	expires_in: 3600,
	token_type: 'Bearer'
};

const mockErrorResponse = {
	error: 'invalid_grant',
	error_description: 'Invalid refresh token',
	correlation_id: 'mock-correlation-id'
};

describe('SharePoint Authentication and Sites Integration', () => {
	beforeAll(() => {
		// Mock the global fetch function with proper types
		global.fetch = vi.fn(async (input: RequestInfo | URL, init?: RequestInit) => {
			const url = typeof input === 'string' ? input : input.toString();
			const body = init?.body?.toString() || '';

			// Check if this is a valid token request
			const isValidToken = body.includes('valid-token');

			return new Response(JSON.stringify(isValidToken ? mockTokenResponse : mockErrorResponse), {
				status: isValidToken ? 200 : 400,
				statusText: isValidToken ? 'OK' : 'Bad Request',
				headers: {
					'Content-Type': 'application/json'
				}
			});
		}) as unknown as typeof fetch;
	});

	afterAll(() => {
		vi.restoreAllMocks();
	});

	test('should successfully exchange refresh token for access token', async () => {
		const response = await exchangeRefreshToken('valid-token');
		expect(response).toEqual(mockTokenResponse);
		expect(response.token_type).toBe('Bearer');
		expect(response.access_token).toBeDefined();
	});

	test('should fail to exchange invalid refresh token', async () => {
		await expect(exchangeRefreshToken('invalid-token')).rejects.toThrow(
			'Invalid grant: Invalid refresh token'
		);
	});

	describe('SharePoint API Integration', () => {
		test.todo('should list SharePoint sites');
		test.todo('should get site details');
	});
});
