import { expect, test } from "@playwright/test";
import { createSupabaseClient } from "../utils/supabase-client";
import { createTestUsers, deleteTestUsers } from "../utils/test-users";
import { v4 as uuidv4 } from "uuid";

// Test data
let ownerUser: any;
let nonMemberUser: any;
let companyId: string;
let invitationToken: string;

test.describe("Company Invitation System", () => {
  test.beforeAll(async () => {
    // Create test users
    [ownerUser, nonMemberUser] = await createTestUsers(2);

    // Create a test company as the owner
    const ownerClient = createSupabaseClient(
      ownerUser.email,
      ownerUser.password,
    );
    const { data: company, error: createError } = await ownerClient
      .from("companies")
      .insert({
        name: `Test Company ${uuidv4()}`,
        description: "Test company for invitation tests",
      })
      .select()
      .single();

    expect(createError).toBeNull();
    companyId = company.id;
  });

  test.afterAll(async () => {
    // Clean up - delete test company and members
    const ownerClient = createSupabaseClient(
      ownerUser.email,
      ownerUser.password,
    );
    await ownerClient.from("company_members").delete().eq(
      "company_id",
      companyId,
    );
    await ownerClient.from("companies").delete().eq("id", companyId);

    // Delete test users
    await deleteTestUsers([ownerUser, nonMemberUser]);
  });

  test("Owner can create an invitation", async () => {
    const ownerClient = createSupabaseClient(
      ownerUser.email,
      ownerUser.password,
    );

    const { data: invitation, error } = await ownerClient
      .from("company_invitations")
      .insert({
        company_id: companyId,
        role: "member",
        invitation_type: "one_time",
      })
      .select()
      .single();

    expect(error).toBeNull();
    expect(invitation).not.toBeNull();
    expect(invitation.token).toBeTruthy();

    invitationToken = invitation.token;
  });

  test("Non-member can view invitation details", async () => {
    const nonMemberClient = createSupabaseClient(
      nonMemberUser.email,
      nonMemberUser.password,
    );

    const { data, error } = await nonMemberClient
      .rpc("get_invitation_details", { token: invitationToken });

    expect(error).toBeNull();
    expect(data).not.toBeNull();
    expect(data.valid).toBe(true);
    expect(data.company_name).toBeTruthy();
  });

  test("Non-member can accept invitation", async () => {
    const nonMemberClient = createSupabaseClient(
      nonMemberUser.email,
      nonMemberUser.password,
    );

    const { data, error } = await nonMemberClient
      .rpc("accept_company_invitation", { token: invitationToken });

    expect(error).toBeNull();
    expect(data).not.toBeNull();
    expect(data.company_id).toBe(companyId);
  });

  test("Non-member becomes member after accepting invitation", async () => {
    const nonMemberClient = createSupabaseClient(
      nonMemberUser.email,
      nonMemberUser.password,
    );

    // Should now be able to access the company
    const { data, error } = await nonMemberClient
      .from("companies")
      .select()
      .eq("id", companyId)
      .single();

    expect(error).toBeNull();
    expect(data).not.toBeNull();
    expect(data.id).toBe(companyId);
  });

  test("One-time invitation is deleted after use", async () => {
    const ownerClient = createSupabaseClient(
      ownerUser.email,
      ownerUser.password,
    );

    const { data, error } = await ownerClient
      .from("company_invitations")
      .select()
      .eq("token", invitationToken);

    expect(error).toBeNull();
    expect(data).not.toBeNull();
    expect(data.length).toBe(0);
  });
});
