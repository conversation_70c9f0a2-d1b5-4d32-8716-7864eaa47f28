import { expect, test } from "@playwright/test";
import { createSupabaseClient } from "../utils/supabase-client";
import { createTestUsers, deleteTestUsers } from "../utils/test-users";
import { v4 as uuidv4 } from "uuid";

// Test data
let ownerUser: any;
let memberUser: any;
let nonMemberUser: any;
let companyId: string;

test.describe("Company RLS Policies", () => {
  test.beforeAll(async () => {
    // Create three test users with different roles
    [ownerUser, memberUser, nonMemberUser] = await createTestUsers(3);

    // Create a test company as the owner
    const ownerClient = createSupabaseClient(
      ownerUser.email,
      ownerUser.password,
    );
    const { data: company, error: createError } = await ownerClient
      .from("companies")
      .insert({
        name: `Test Company ${uuidv4()}`,
        description: "Test company for RLS tests",
      })
      .select()
      .single();

    expect(createError).toBeNull();
    companyId = company.id;

    // Add memberUser as a regular member
    const { error: addMemberError } = await ownerClient
      .from("company_members")
      .insert({
        company_id: companyId,
        user_id: memberUser.id,
        role: "member",
        is_active: true,
      });

    expect(addMemberError).toBeNull();
  });

  test.afterAll(async () => {
    // Clean up - delete test company and members
    const ownerClient = createSupabaseClient(
      ownerUser.email,
      ownerUser.password,
    );
    await ownerClient.from("company_members").delete().eq(
      "company_id",
      companyId,
    );
    await ownerClient.from("companies").delete().eq("id", companyId);

    // Delete test users
    await deleteTestUsers([ownerUser, memberUser, nonMemberUser]);
  });

  test("Owner can view company", async () => {
    const ownerClient = createSupabaseClient(
      ownerUser.email,
      ownerUser.password,
    );
    const { data, error } = await ownerClient
      .from("companies")
      .select()
      .eq("id", companyId)
      .single();

    expect(error).toBeNull();
    expect(data).not.toBeNull();
    expect(data.id).toBe(companyId);
  });

  test("Member can view company", async () => {
    const memberClient = createSupabaseClient(
      memberUser.email,
      memberUser.password,
    );
    const { data, error } = await memberClient
      .from("companies")
      .select()
      .eq("id", companyId)
      .single();

    expect(error).toBeNull();
    expect(data).not.toBeNull();
    expect(data.id).toBe(companyId);
  });

  test("Non-member cannot view company", async () => {
    const nonMemberClient = createSupabaseClient(
      nonMemberUser.email,
      nonMemberUser.password,
    );
    const { data, error } = await nonMemberClient
      .from("companies")
      .select()
      .eq("id", companyId)
      .single();

    expect(error).not.toBeNull();
    expect(data).toBeNull();
  });

  test("Owner can update company", async () => {
    const ownerClient = createSupabaseClient(
      ownerUser.email,
      ownerUser.password,
    );
    const newName = `Updated Company ${uuidv4()}`;
    const { data, error } = await ownerClient
      .from("companies")
      .update({ name: newName })
      .eq("id", companyId)
      .select()
      .single();

    expect(error).toBeNull();
    expect(data).not.toBeNull();
    expect(data.name).toBe(newName);
  });

  test("Member cannot update company", async () => {
    const memberClient = createSupabaseClient(
      memberUser.email,
      memberUser.password,
    );
    const { error } = await memberClient
      .from("companies")
      .update({ name: "Attempt Update" })
      .eq("id", companyId);

    expect(error).not.toBeNull();
  });

  test("Owner can view company members", async () => {
    const ownerClient = createSupabaseClient(
      ownerUser.email,
      ownerUser.password,
    );
    const { data, error } = await ownerClient
      .from("company_members")
      .select()
      .eq("company_id", companyId);

    expect(error).toBeNull();
    expect(data).not.toBeNull();
    expect(data.length).toBe(2); // Owner and member
  });

  test("Owner can add, update, and remove members", async () => {
    const ownerClient = createSupabaseClient(
      ownerUser.email,
      ownerUser.password,
    );

    // Create a temporary test user
    const tempUser = await createTestUsers(1)[0];

    // Add new member
    const { data: addedMember, error: addError } = await ownerClient
      .from("company_members")
      .insert({
        company_id: companyId,
        user_id: tempUser.id,
        role: "member",
        is_active: true,
      })
      .select()
      .single();

    expect(addError).toBeNull();
    expect(addedMember).not.toBeNull();

    // Update member
    const { error: updateError } = await ownerClient
      .from("company_members")
      .update({ role: "admin" })
      .eq("company_id", companyId)
      .eq("user_id", tempUser.id);

    expect(updateError).toBeNull();

    // Remove member
    const { error: deleteError } = await ownerClient
      .from("company_members")
      .delete()
      .eq("company_id", companyId)
      .eq("user_id", tempUser.id);

    expect(deleteError).toBeNull();

    // Clean up
    await deleteTestUsers([tempUser]);
  });

  test("Member cannot add new company members", async () => {
    const memberClient = createSupabaseClient(
      memberUser.email,
      memberUser.password,
    );
    const { error } = await memberClient
      .from("company_members")
      .insert({
        company_id: companyId,
        user_id: nonMemberUser.id,
        role: "member",
        is_active: true,
      });

    expect(error).not.toBeNull();
  });
});
