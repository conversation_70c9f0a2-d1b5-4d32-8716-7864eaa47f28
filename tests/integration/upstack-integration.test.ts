import { describe, test, expect, vi, beforeEach, afterEach } from 'vitest';
import { cleanup } from '@testing-library/svelte';
import { redirect } from '@sveltejs/kit';
import { UpstackClient } from '../../src/lib/server/upstack';
import type {
	UpstackEntityCreationResponse,
	UpstackTokenResponse
} from '../../src/lib/types/upstack.types';

// Mock SvelteKit modules
vi.mock('@sveltejs/kit', () => ({
	redirect: vi.fn(),
	error: vi.fn().mockImplementation((status, message) => {
		throw new Error(`Status ${status}: ${message}`);
	}),
	fail: vi.fn().mockImplementation((status, data) => ({ status, type: 'failure', data }))
}));

// Mock Supabase client
const mockSupabase = {
	from: vi.fn().mockReturnThis(),
	select: vi.fn().mockReturnThis(),
	eq: vi.fn().mockReturnThis(),
	single: vi.fn(),
	insert: vi.fn().mockReturnThis(),
	rpc: vi.fn()
};

// Mock locals object
const mockLocals = {
	supabase: mockSupabase,
	user: { id: 'test-user-id' }
};

// Import the page server module
import {
	load,
	actions
} from '../../src/routes/company/[companyId]/vault/connect/upstack/+page.server';

// Mock fetch
global.fetch = vi.fn();

describe('Upstack Integration', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	afterEach(() => {
		cleanup();
	});

	describe('Page Load', () => {
		test('should check if Upstack product is enabled', async () => {
			// Mock company member data
			mockSupabase.single.mockResolvedValueOnce({
				data: { id: 'test-member-id', role: 'admin' },
				error: null
			});

			// Mock product data with Upstack enabled
			mockSupabase.select.mockReturnValueOnce({
				eq: vi.fn().mockResolvedValueOnce({
					data: [
						{
							id: 'test-product-id',
							is_enabled: true,
							products: { id: 'upstack-id', name: 'Upstack Integration' }
						}
					],
					error: null
				})
			});

			// Call the load function
			const result = await load({
				locals: mockLocals,
				params: { companyId: 'test-company-id' }
			} as any);

			// Verify the expected behavior
			expect(mockSupabase.from).toHaveBeenCalledWith('company_members');
			expect(mockSupabase.eq).toHaveBeenCalledWith('company_id', 'test-company-id');
			expect(mockSupabase.from).toHaveBeenCalledWith('company_products');
			expect(result).toEqual({ companyId: 'test-company-id' });
		});

		test('should redirect if Upstack product is not enabled', async () => {
			// Mock company member data
			mockSupabase.single.mockResolvedValueOnce({
				data: { id: 'test-member-id', role: 'admin' },
				error: null
			});

			// Mock product data with Upstack NOT enabled
			mockSupabase.select.mockReturnValueOnce({
				eq: vi.fn().mockResolvedValueOnce({
					data: [
						{
							id: 'test-product-id',
							is_enabled: true,
							products: { id: 'other-id', name: 'Other Product' }
						}
					],
					error: null
				})
			});

			// Call the load function and expect it to redirect
			await expect(
				load({
					locals: mockLocals,
					params: { companyId: 'test-company-id' }
				} as any)
			).rejects.toThrow();

			// Verify the redirect was called
			expect(redirect).toHaveBeenCalledWith(303, expect.stringContaining('/manage-company'));
		});
	});

	describe('Form Action', () => {
		test('should create a new Upstack entity', async () => {
			// Mock the UUID generation for consistent testing
			const mockUUID = 'test-uuid-123';
			vi.spyOn(crypto, 'randomUUID').mockReturnValue(mockUUID);

			// Mock the secrets creation
			mockSupabase.rpc.mockResolvedValueOnce({
				data: ['secret-id-1', 'secret-id-2'],
				error: null
			});

			// Mock the data source creation
			mockSupabase.insert.mockReturnValueOnce({
				select: vi.fn().mockReturnValueOnce({
					single: vi.fn().mockResolvedValueOnce({
						data: { id: 'datasource-id' },
						error: null
					})
				})
			});

			// Create a mock request with form data
			const mockRequest = {
				formData: vi.fn().mockResolvedValueOnce({
					get: (key: string) => (key === 'entityName' ? 'Test Entity' : 'Test Description')
				})
			};

			// Call the action
			await expect(
				actions.default({
					request: mockRequest as any,
					locals: mockLocals,
					params: { companyId: 'test-company-id' }
				} as any)
			).rejects.toThrow();

			// Verify that redirect was called (success case redirects)
			expect(redirect).toHaveBeenCalledWith(303, '/company/test-company-id/vault');

			// Verify the data source was created with the correct data
			expect(mockSupabase.from).toHaveBeenCalledWith('data_sources');
			expect(mockSupabase.insert).toHaveBeenCalledWith({
				company_id: 'test-company-id',
				platform_type: 'upstack',
				display_name: 'Test Entity',
				secret_oauth: 'secret-id-2',
				access_token: 'secret-id-1',
				metadata: {
					entity_id: mockUUID,
					entity_name: 'Test Entity',
					entity_description: 'Test Description'
				}
			});
		});

		test('should return an error if entity name is missing', async () => {
			// Create a mock request with missing entity name
			const mockRequest = {
				formData: vi.fn().mockResolvedValueOnce({
					get: () => undefined
				})
			};

			// Call the action
			const result = await actions.default({
				request: mockRequest as any,
				locals: mockLocals,
				params: { companyId: 'test-company-id' }
			} as any);

			// Verify that fail was called with the appropriate error
			expect(result.status).toBe(400);
			expect(result.data.error).toBe('Entity name is required');
		});

		test('should handle errors in token storage', async () => {
			// Mock the secrets creation to fail
			mockSupabase.rpc.mockResolvedValueOnce({
				data: null,
				error: { message: 'Failed to create secrets' }
			});

			// Create a mock request with form data
			const mockRequest = {
				formData: vi.fn().mockResolvedValueOnce({
					get: (key: string) => (key === 'entityName' ? 'Test Entity' : 'Test Description')
				})
			};

			// Call the action
			const result = await actions.default({
				request: mockRequest as any,
				locals: mockLocals,
				params: { companyId: 'test-company-id' }
			} as any);

			// Verify error handling
			expect(result.status).toBe(500);
			expect(result.data.error).toBe('Failed to store authentication credentials');
		});
	});

	describe('UpstackClient', () => {
		let upstackClient: UpstackClient;

		beforeEach(() => {
			upstackClient = new UpstackClient('test-client-id', 'test-client-secret');
			vi.resetAllMocks();
		});

		afterEach(() => {
			vi.clearAllMocks();
		});

		describe('authenticate', () => {
			it('should authenticate with client credentials and return token data', async () => {
				// Mock response
				const mockTokenResponse: UpstackTokenResponse = {
					access_token: 'test-access-token',
					refresh_token: 'test-refresh-token',
					token_type: 'bearer',
					expires_in: 3600
				};

				// Setup mock
				global.fetch = vi.fn().mockResolvedValueOnce({
					ok: true,
					json: async () => mockTokenResponse
				});

				// Call method
				const result = await upstackClient.authenticate();

				// Verify
				expect(global.fetch).toHaveBeenCalledWith(
					'https://api.upstack.com/v1/auth/token',
					expect.objectContaining({
						method: 'POST',
						headers: {
							'Content-Type': 'application/json'
						},
						body: JSON.stringify({
							client_id: 'test-client-id',
							client_secret: 'test-client-secret',
							grant_type: 'client_credentials'
						})
					})
				);

				expect(result).toEqual(mockTokenResponse);
			});

			it('should throw an error when authentication fails', async () => {
				// Mock response
				const mockErrorResponse = {
					error: 'invalid_client',
					error_description: 'Invalid client credentials',
					status: 401,
					timestamp: new Date().toISOString()
				};

				// Setup mock
				global.fetch = vi.fn().mockResolvedValueOnce({
					ok: false,
					status: 401,
					json: async () => mockErrorResponse
				});

				// Verify
				await expect(upstackClient.authenticate()).rejects.toThrow();
			});
		});

		describe('createEntity', () => {
			it('should create an entity and return the response', async () => {
				// Mock token response for authentication
				const mockTokenResponse: UpstackTokenResponse = {
					access_token: 'test-access-token',
					refresh_token: 'test-refresh-token',
					token_type: 'bearer',
					expires_in: 3600
				};

				// Mock entity creation response
				const mockEntityResponse: UpstackEntityCreationResponse = {
					id: 'test-entity-id',
					name: 'Test Entity',
					description: 'Test Description',
					status: 'success'
				};

				// Setup mocks
				global.fetch = vi
					.fn()
					.mockResolvedValueOnce({
						// First call for authentication
						ok: true,
						json: async () => mockTokenResponse
					})
					.mockResolvedValueOnce({
						// Second call for entity creation
						ok: true,
						json: async () => mockEntityResponse
					});

				// Call method
				const result = await upstackClient.createEntity('Test Entity', 'Test Description');

				// Verify authentication was called
				expect(global.fetch).toHaveBeenNthCalledWith(
					1,
					'https://api.upstack.com/v1/auth/token',
					expect.anything()
				);

				// Verify entity creation
				expect(global.fetch).toHaveBeenNthCalledWith(
					2,
					'https://api.upstack.com/v1/entities',
					expect.objectContaining({
						method: 'POST',
						headers: {
							'Content-Type': 'application/json',
							Authorization: 'Bearer test-access-token'
						},
						body: JSON.stringify({
							name: 'Test Entity',
							description: 'Test Description',
							metadata: {}
						})
					})
				);

				expect(result).toEqual(mockEntityResponse);
			});

			it('should throw an error when entity creation fails', async () => {
				// Mock token response for authentication
				const mockTokenResponse: UpstackTokenResponse = {
					access_token: 'test-access-token',
					refresh_token: 'test-refresh-token',
					token_type: 'bearer',
					expires_in: 3600
				};

				// Mock error response
				const mockErrorResponse = {
					error: 'invalid_request',
					error_description: 'Entity creation failed',
					message: 'Entity creation failed due to validation errors',
					status: 400,
					timestamp: new Date().toISOString()
				};

				// Setup mocks
				global.fetch = vi
					.fn()
					.mockResolvedValueOnce({
						// First call for authentication
						ok: true,
						json: async () => mockTokenResponse
					})
					.mockResolvedValueOnce({
						// Second call for entity creation (fails)
						ok: false,
						status: 400,
						json: async () => mockErrorResponse
					});

				// Verify
				await expect(
					upstackClient.createEntity('Test Entity', 'Test Description')
				).rejects.toThrow();
			});
		});
	});
});
