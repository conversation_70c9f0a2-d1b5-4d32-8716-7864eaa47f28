import { afterEach, beforeEach, describe, expect, test, vi } from "vitest";
import type { SupabaseClient } from "@supabase/supabase-js";

describe("Row Level Security", () => {
  let mockSupabase: Partial<SupabaseClient>;
  const testUserId = "test-user-id";

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();

    // Create mock Supabase client with proper method chaining
    mockSupabase = {
      from: vi.fn().mockImplementation(() => ({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnValue({
          data: [{
            user_id: testUserId,
            company_id: "test-company",
            role: "member",
          }],
          error: null,
        }),
        neq: vi.fn().mockReturnValue({
          data: [],
          error: null,
        }),
      })),
    };
  });

  test("users can only access their own company data", async () => {
    const { data: companyData, error: companyError } = await mockSupabase
      .from!("company_user_profiles")
      .select("*")
      .eq("user_id", testUserId);

    expect(companyError).toBeNull();
    expect(companyData).toBeDefined();
    expect(Array.isArray(companyData)).toBe(true);
    expect(companyData.every((row) => row.user_id === testUserId)).toBe(true);
  });

  test("users cannot access other users data", async () => {
    const { data: otherUserData, error: otherUserError } = await mockSupabase
      .from!("company_user_profiles")
      .select("*")
      .neq("user_id", testUserId);

    expect(otherUserError).toBeNull();
    expect(otherUserData).toHaveLength(0);
  });
});
