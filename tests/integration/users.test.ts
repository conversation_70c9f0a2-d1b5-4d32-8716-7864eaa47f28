import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { load } from "../../src/routes/company/[companyId]/users/+page.server";
import { createClient } from "@supabase/supabase-js";

// Mock the external dependencies
vi.mock("$env/static/private", () => ({
  PRIVATE_AZURE_CLIENT_SECRET: "mock-secret",
}));

vi.mock("$env/static/public", () => ({
  PUBLIC_AZURE_CLIENT_ID: "mock-client-id",
}));

vi.mock("$lib/server/vault", () => ({
  getSecretById: vi.fn().mockResolvedValue("mock-refresh-token"),
}));

vi.mock("$lib/server/microsoft", () => ({
  exchangeRefreshToken: vi.fn().mockResolvedValue({
    access_token: "mock-access-token",
    refresh_token: "mock-refresh-token",
  }),
  getGraphUsers: vi.fn().mockResolvedValue([
    {
      id: "graph-user-1",
      displayName: "Graph User 1",
      mail: "<EMAIL>",
      mobilePhone: "44123456789",
    },
  ]),
  matchUserByEmail: vi.fn().mockImplementation((graphUser, companyUser) =>
    graphUser.mail?.toLowerCase() === companyUser.user_email?.toLowerCase()
  ),
  processGraphUser: vi.fn().mockImplementation((graphUser) => ({
    user_name: graphUser.displayName,
    user_email: graphUser.mail,
    user_phone_number: graphUser.mobilePhone,
  })),
  sharepointScopes: ["User.Read", "Files.Read"],
}));

describe("Users Route Load Function", () => {
  let mockSupabase;
  let mockEvent;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();

    // Create mock Supabase client with proper method chaining
    mockSupabase = {
      from: vi.fn().mockImplementation((table) => {
        if (table === "company_user_profiles") {
          return {
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnValue({
              data: [],
              error: null,
            }),
          };
        }
        if (table === "data_sources") {
          return {
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnValue({
              data: [],
              error: null,
            }),
          };
        }
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnValue({
            data: null,
            error: null,
          }),
        };
      }),
    };

    // Create mock event object
    mockEvent = {
      params: { companyId: "test-company-id" },
      locals: {
        supabase: mockSupabase,
        user: { id: "test-user-id" },
      },
    };
  });

  it("should handle case with no company users", async () => {
    const result = await load(mockEvent);

    expect(result.companyUsers).toHaveLength(0);
    expect(result.needsConsent).toBe(false);
  });

  it("should combine company users with Graph users", async () => {
    // Mock Supabase responses
    mockSupabase.from = vi.fn().mockImplementation((table) => {
      if (table === "company_user_profiles") {
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnValue({
            data: [
              {
                user_id: "user-1",
                user_email: "<EMAIL>",
                is_active: true,
                role: "member",
                user_status: "active",
                raw_user_meta_data: { full_name: "User 1" },
              },
              {
                user_id: "user-2",
                user_email: "<EMAIL>",
                is_active: true,
                role: "admin",
                user_status: "active",
                raw_user_meta_data: { full_name: "User 2" },
              },
            ],
            error: null,
          }),
        };
      }
      if (table === "data_sources") {
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnValue({
            data: [{
              site_id: "test-site",
              tenant_id: "test-tenant",
              secret_oauth: "test-secret",
            }],
            error: null,
          }),
        };
      }
      return {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnValue({
          data: null,
          error: null,
        }),
      };
    });

    const result = await load(mockEvent);

    expect(result.companyUsers).toHaveLength(2);
    expect(result.needsConsent).toBe(false);
    const mergedUser = result.companyUsers.find((u) =>
      u.user_email === "<EMAIL>"
    );
    expect(mergedUser?.user_phone_number).toBe("44123456789");
  });

  it("should handle Graph API consent errors", async () => {
    // Mock Graph API error
    const { exchangeRefreshToken } = await import("$lib/server/microsoft");
    vi.mocked(exchangeRefreshToken).mockRejectedValueOnce(
      new Error("invalid_grant"),
    );

    // Mock company users
    mockSupabase.from = vi.fn().mockImplementation((table) => {
      if (table === "company_user_profiles") {
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnValue({
            data: [{
              user_id: "user-1",
              user_email: "<EMAIL>",
              is_active: true,
              role: "member",
              user_status: "active",
            }],
            error: null,
          }),
        };
      }
      if (table === "data_sources") {
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnValue({
            data: [{
              site_id: "test-site",
              tenant_id: "test-tenant",
              secret_oauth: "test-secret",
            }],
            error: null,
          }),
        };
      }
      return {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnValue({
          data: null,
          error: null,
        }),
      };
    });

    const result = await load(mockEvent);

    expect(result.needsConsent).toBe(true);
    expect(result.companyUsers).toBeDefined();
    expect(result.companyUsers[0].has_file_access).toBe(false);
  });

  it("should handle missing data sources gracefully", async () => {
    // Mock empty data sources response
    mockSupabase.from = vi.fn().mockImplementation((table) => {
      if (table === "company_user_profiles") {
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnValue({
            data: [{
              user_id: "user-1",
              user_email: "<EMAIL>",
              is_active: true,
              role: "member",
              user_status: "active",
            }],
            error: null,
          }),
        };
      }
      if (table === "data_sources") {
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnValue({
            data: [],
            error: null,
          }),
        };
      }
      return {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnValue({
          data: null,
          error: null,
        }),
      };
    });

    const result = await load(mockEvent);

    expect(result.needsConsent).toBe(false);
    expect(result.companyUsers).toHaveLength(1);
    expect(result.companyUsers[0].has_file_access).toBe(false);
  });
});
