import { expect, test, describe, beforeAll } from 'vitest';
import { createClient, type SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_URL, SUPABASE_ANON_KEY } from '$env/static/private';
import type { Database } from 'database.types';

describe('Courses Integration Migration Tests', () => {
	let supabase: SupabaseClient<Database>;

	beforeAll(() => {
		supabase = createClient<Database>(SUPABASE_URL, SUPABASE_ANON_KEY);
	});

	test('platform_type enum includes courses', async () => {
		const { data, error } = await supabase.rpc('get_enum_values', {
			enum_name: 'platform_type'
		});

		expect(error).toBeNull();
		expect(data).toBeDefined();
		expect(Array.isArray(data)).toBe(true);
		expect(data?.includes('courses')).toBe(true);
	});

	test('products table has categories column', async () => {
		const { data, error } = await supabase
			.from('information_schema.columns')
			.select('*')
			.eq('table_name', 'products')
			.eq('column_name', 'categories');

		expect(error).toBeNull();
		expect(data).toBeDefined();
		expect(data?.length).toBeGreaterThan(0);
		expect(data?.[0]?.data_type).toBe('ARRAY');
	});

	test('products table has requires_product_id column', async () => {
		const { data, error } = await supabase
			.from('information_schema.columns')
			.select('*')
			.eq('table_name', 'products')
			.eq('column_name', 'requires_product_id');

		expect(error).toBeNull();
		expect(data).toBeDefined();
		expect(data?.length).toBeGreaterThan(0);
		expect(data?.[0]?.data_type).toBe('uuid');
	});

	test('Course Creator product exists', async () => {
		const { data, error } = await supabase
			.from('products')
			.select('*')
			.eq('id', '0d7b3945-0900-4096-bd8a-2b0e7d3f95ef');

		expect(error).toBeNull();
		expect(data).toBeDefined();
		expect(data?.length).toBe(1);
		expect(data?.[0]?.name).toBe('Course Creator');

		// Check categories array
		const categories = data?.[0]?.categories;
		expect(Array.isArray(categories)).toBe(true);
		expect(categories).toContain('Learning');
		expect(categories).toContain('Course Management');
	});

	test('Courses product exists with reference to Course Creator', async () => {
		const { data, error } = await supabase
			.from('products')
			.select('*')
			.eq('id', 'fc1a72b2-2c33-4857-b387-c28902dc3fb8');

		expect(error).toBeNull();
		expect(data).toBeDefined();
		expect(data?.length).toBe(1);
		expect(data?.[0]?.name).toBe('Courses');

		// Check categories array
		const categories = data?.[0]?.categories;
		expect(Array.isArray(categories)).toBe(true);
		expect(categories).toContain('Document Storage');

		// Check reference to Course Creator
		expect(data?.[0]?.requires_product_id).toBe('0d7b3945-0900-4096-bd8a-2b0e7d3f95ef');
	});

	test('courses_data_sources_select policy exists', async () => {
		const { data, error } = await supabase
			.from('pg_policies')
			.select('*')
			.eq('tablename', 'data_sources')
			.eq('policyname', 'courses_data_sources_select');

		expect(error).toBeNull();
		expect(data).toBeDefined();
		expect(data?.length).toBeGreaterThan(0);
	});

	test('connected_accounts_by_company view includes courses', async () => {
		// This is more complex to test directly, so we'll check if the view exists
		const { data, error } = await supabase
			.from('information_schema.views')
			.select('*')
			.eq('table_name', 'connected_accounts_by_company');

		expect(error).toBeNull();
		expect(data).toBeDefined();
		expect(data?.length).toBeGreaterThan(0);
	});

	test('data_sources_by_company_members view includes courses', async () => {
		// This is more complex to test directly, so we'll check if the view exists
		const { data, error } = await supabase
			.from('information_schema.views')
			.select('*')
			.eq('table_name', 'data_sources_by_company_members');

		expect(error).toBeNull();
		expect(data).toBeDefined();
		expect(data?.length).toBeGreaterThan(0);
	});
});
