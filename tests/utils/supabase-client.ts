import { createClient } from "@supabase/supabase-js";
import type { Database } from "../../database.types";

// Create a Supabase client for testing
export function createSupabaseClient(email: string, password: string) {
  const supabase = createClient<Database>(
    process.env.VITE_PUBLIC_SUPABASE_URL!,
    process.env.VITE_PUBLIC_SUPABASE_ANON_KEY!,
  );

  // Sign in as the provided user
  supabase.auth.signInWithPassword({
    email,
    password,
  });

  return supabase;
}
