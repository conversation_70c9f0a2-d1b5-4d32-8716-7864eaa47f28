import { createClient } from "@supabase/supabase-js";
import { v4 as uuidv4 } from "uuid";
import type { Database } from "../../database.types";

const adminSupabase = createClient<Database>(
  process.env.VITE_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
);

// Create test users for testing
export async function createTestUsers(count: number = 1) {
  const users = [];

  for (let i = 0; i < count; i++) {
    const uuid = uuidv4();
    const email = `test-${uuid}@example.com`;
    const password = `Password123!${uuid}`;

    // Create user with admin client
    const { data: { user }, error } = await adminSupabase.auth.admin.createUser(
      {
        email,
        password,
        email_confirm: true,
      },
    );

    if (error) {
      throw new Error(`Failed to create test user: ${error.message}`);
    }

    users.push({
      id: user!.id,
      email,
      password,
    });
  }

  return users;
}

// Delete test users after testing
export async function deleteTestUsers(users: any[]) {
  for (const user of users) {
    await adminSupabase.auth.admin.deleteUser(user.id);
  }
}
