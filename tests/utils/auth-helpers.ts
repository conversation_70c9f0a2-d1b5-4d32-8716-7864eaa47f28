import type { Page } from '@playwright/test';

/**
 * Logs in as a test user with provided credentials or default test user
 * @param page Playwright page object
 * @param email Optional email to use for login
 * @param password Optional password to use for login
 */
export async function loginAsTestUser(
	page: Page,
	email: string = '<EMAIL>',
	password: string = 'password123'
): Promise<void> {
	// Go to sign-in page
	await page.goto('/auth/signin');

	// Fill in credentials
	await page.getByLabel('Email').fill(email);
	await page.getByLabel('Password').fill(password);

	// Submit the form
	await page.getByRole('button', { name: 'Sign In' }).click();

	// Wait for successful login and redirect
	await page.waitForURL('/app', { timeout: 10000 });
}

/**
 * Logs out the current user
 * @param page Playwright page object
 */
export async function logout(page: Page): Promise<void> {
	// Click on the user menu
	await page.getByRole('button', { name: 'User menu' }).click();

	// Click logout
	await page.getByRole('menuitem', { name: 'Sign out' }).click();

	// Wait for redirect to login page
	await page.waitForURL('/auth/signin');
}

/**
 * Creates a test company with a unique name
 * @param page Playwright page object
 * @param companyName Optional company name, will use timestamp if not provided
 * @returns The ID of the created company
 */
export async function createTestCompany(page: Page, companyName?: string): Promise<string> {
	// Use provided name or generate one with timestamp
	const name = companyName || `Test Company ${Date.now()}`;

	// Navigate to companies page
	await page.goto('/app/companies');

	// Click create new company
	await page.getByText('Create New Company').click();

	// Fill in company name
	await page.getByLabel('Company Name').fill(name);

	// Submit form
	await page.getByRole('button', { name: 'Create Company' }).click();

	// Wait for redirect to company page
	await page.waitForURL(/\/company\/([^/]+)/);

	// Extract company ID from URL
	const url = page.url();
	const match = url.match(/\/company\/([^/]+)/);

	if (!match || !match[1]) {
		throw new Error('Could not extract company ID from URL');
	}

	return match[1];
}

/**
 * Deletes a test company by ID
 * @param page Playwright page object
 * @param companyId The ID of the company to delete
 */
export async function deleteTestCompany(page: Page, companyId: string): Promise<void> {
	// Navigate to company settings
	await page.goto(`/company/${companyId}/settings`);

	// Scroll to the bottom where delete button typically is
	await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));

	// Click the danger zone / delete company button
	await page.getByText('Delete Company').click();

	// Confirm deletion in modal
	await page.getByRole('button', { name: 'Delete' }).click();

	// Wait for redirect back to companies list
	await page.waitForURL('/app/companies');
}
