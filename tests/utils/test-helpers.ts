import { createClient } from '@supabase/supabase-js';

export function getSupabaseClient() {
	return createClient('https://localhost:54321', 'test-key');
}

export async function createTestUser() {
	const supabase = getSupabaseClient();
	const email = `test-${Date.now()}@example.com`;
	const password = 'test-password';

	const {
		data: { user },
		error
	} = await supabase.auth.signUp({
		email,
		password
	});

	if (error) throw error;
	return user;
}

export async function setupTestData(userId: string) {
	const supabase = getSupabaseClient();

	// Create test company
	const { data: company } = await supabase
		.from('companies')
		.insert([
			{
				name: 'Test Company',
				domain: 'test.com'
			}
		])
		.select()
		.single();

	// Add user to company
	await supabase.from('company_members').insert([
		{
			company_id: company.id,
			user_id: userId,
			role: 'member'
		}
	]);

	return { company };
}

export async function cleanupTestUser(userId: string) {
	const supabase = getSupabaseClient();

	// Clean up company memberships
	await supabase.from('company_members').delete().eq('user_id', userId);

	// Clean up user
	await supabase.auth.admin.deleteUser(userId);
}
