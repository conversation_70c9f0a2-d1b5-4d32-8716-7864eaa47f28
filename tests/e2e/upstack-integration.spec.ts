import { test, expect } from '@playwright/test';

// Test user credentials - would normally come from environment or test config
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'password123';

test('Upstack Integration - Entity Creation Flow', async ({ page }) => {
	try {
		// Initial logging - helps with debugging in CI
		console.log('Starting Upstack integration test...');

		// Step 1: Login
		await page.goto('/auth/login');
		await page.waitForLoadState('networkidle');
		console.log('Login page loaded');

		await page.fill('input[name="email"]', TEST_EMAIL);
		await page.fill('input[name="password"]', TEST_PASSWORD);
		await page.click('button[type="submit"]');

		// Wait for login to complete
		await page.waitForURL(/\/company\/.*/);
		console.log('Login successful');

		// Step 2: Navigate to company management
		const companyUrl = page.url();
		const companyIdMatch = companyUrl.match(/\/company\/([^/]+)/);
		const companyId = companyIdMatch ? companyIdMatch[1] : null;

		if (!companyId) {
			throw new Error('Failed to extract company ID from URL');
		}

		console.log('Extracted company ID:', companyId);

		// Navigate to company management page
		await page.goto(`/company/${companyId}/manage-company`);
		await page.waitForLoadState('networkidle');
		console.log('Company management page loaded');

		// Step 3: Enable Upstack product if not already enabled
		// This assumes there's a tab/section for products in the management page
		await page.getByRole('tab', { name: /integrations|products/i }).click();
		console.log('Navigated to integrations tab');

		// Look for Upstack in the product list
		const upstackProductRow = page.locator('text=Upstack Integration').first();

		if (await upstackProductRow.isVisible()) {
			// Check if it's already enabled
			const enableButton = upstackProductRow.locator('button:has-text("Enable")');
			if (await enableButton.isVisible()) {
				console.log('Enabling Upstack product');
				await enableButton.click();
				await page.waitForTimeout(1000); // Allow time for toggle to take effect
			} else {
				console.log('Upstack product already enabled');
			}
		} else {
			console.log('Upstack product not found in the list, skipping enablement');
		}

		// Step 4: Navigate to vault page
		await page.goto(`/company/${companyId}/vault`);
		await page.waitForLoadState('networkidle');
		console.log('Vault page loaded');

		// Step 5: Click "Add Data Source" dropdown
		await page.getByRole('button', { name: /add data source/i }).click();

		// Step 6: Select Upstack from dropdown
		await page.getByRole('menuitem', { name: /upstack/i }).click();

		// Wait for the upstack connection page to load
		await page.waitForURL(`/company/${companyId}/vault/connect/upstack`);
		console.log('Upstack connection page loaded');

		// Step 7: Fill and submit the entity creation form
		const entityName = `Test Entity ${new Date().toISOString()}`;
		await page.fill('input[name="entityName"]', entityName);
		await page.fill('textarea[name="entityDescription"]', 'Created by E2E test');

		console.log('Submitting entity creation form');
		await page.click('button[type="submit"]:has-text("Create Entity")');

		// Expect to be redirected back to vault page
		await page.waitForURL(`/company/${companyId}/vault`);
		console.log('Redirected back to vault page');

		// Step 8: Verify the entity appears in the data sources list
		await page.waitForSelector(`text=${entityName}`);

		const newEntityCard = page.locator(`text=${entityName}`).first();
		expect(await newEntityCard.isVisible()).toBeTruthy();

		// Also check that it shows as Upstack type
		const typeIndicator = newEntityCard.locator(
			'xpath=./ancestor::div[contains(@class, "card")]//p[contains(text(), "upstack")]'
		);
		expect(await typeIndicator.isVisible()).toBeTruthy();

		console.log('Test completed successfully');
	} catch (error) {
		console.error('Test failed with error:', error);
		// Take screenshot on failure to help with debugging
		await page.screenshot({ path: 'upstack-integration-failure.png' });
		throw error;
	}
});
