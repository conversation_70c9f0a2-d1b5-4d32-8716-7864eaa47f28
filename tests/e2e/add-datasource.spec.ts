import { expect, test } from '@playwright/test';

test.use({
	ignoreHTTPSErrors: true,
	storageState: 'playwright/.auth/user.json'
});

test('should link SharePoint data source to an existing company', async ({ page }) => {
	console.log('Starting SharePoint data source linking test...');

	try {
		// Navigate to companies page
		await page.goto('/company');
		console.log('Navigated to companies page');

		// Click on the test company
		const companyLink = page.getByRole('link', {
			name: 'test company creation'
		});
		await expect(companyLink).toBeVisible({ timeout: 10000 });
		await companyLink.click();
		console.log('Clicked on test company');

		// Navigate to Vault tab
		const vaultTab = page.getByRole('link', { name: 'Vault' });
		await expect(vaultTab).toBeVisible({ timeout: 10000 });
		await vaultTab.click();
		console.log('Navigated to Vault tab');

		// Click SharePoint link button
		const sharePointButton = page.getByRole('button', {
			name: 'Link SharePoint Data Source'
		});
		await expect(sharePointButton).toBeVisible({ timeout: 10000 });
		await sharePointButton.click();
		console.log('Clicked SharePoint link button');

		// Select SharePoint site
		const sitePicker = page.locator('[data-test-id="darren\\@linked-up\\.co\\.za"]');
		await expect(sitePicker).toBeVisible({ timeout: 10000 });
		await sitePicker.click();
		console.log('Selected SharePoint site');

		// Verify the data source was added successfully
		// Note: Add appropriate assertion based on your UI feedback
		// For example, looking for a success message or the connected site in the list
		await expect(page.getByText('SharePoint site linked successfully')).toBeVisible({
			timeout: 10000
		});
		console.log('SharePoint data source linked successfully');
	} catch (error) {
		console.error('Test failed:', error);
		console.log('Current URL:', await page.url());

		// Capture page state on error
		await page.screenshot({ path: 'test-results/datasource-error.png' });
		throw error;
	}
});
