import { expect, test } from '@playwright/test';

test.use({
	ignoreHTTPSErrors: true,
	storageState: 'playwright/.auth/user.json'
});

test('should create a new company and attempt to link SharePoint', async ({ page }) => {
	console.log('Starting company creation test...');

	try {
		// Navigate to the company page first
		await page.goto('/company/setup-company');
		console.log('Navigated to company page');

		// Now that we're on our domain, check auth state
		const cookies = await page.context().cookies();
		console.log('Current cookies:', cookies);

		const localStorageData = await page.evaluate(() => {
			const data = {};
			for (let i = 0; i < localStorage.length; i++) {
				const key = localStorage.key(i);
				data[key] = localStorage.getItem(key);
			}
			return data;
		});
		console.log('LocalStorage data:', localStorageData);

		// Wait for and click the new company button
		const newCompanyButton = await page.waitForSelector('[data-button-root]', {
			timeout: 60000
		});
		await newCompanyButton.click();
		console.log('Clicked new company button');

		// Fill in company details
		await page.fill('input[name="name"]', 'Test Company');
		await page.fill('input[name="description"]', 'Test Description');
		console.log('Filled company details');

		// Submit the form
		const submitButton = await page.waitForSelector('button[type="submit"]');
		await submitButton.click();
		console.log('Submitted company form');

		// Wait for SharePoint link button
		const sharePointButton = await page.waitForSelector('text=Link SharePoint', { timeout: 60000 });
		await sharePointButton.click();
		console.log('Clicked SharePoint link button');
		// Wait for the Azure OAuth screen to appear
		await page.waitForSelector('text=Sign in with your Azure account', {
			timeout: 60000
		});
		console.log('Azure OAuth screen is visible');

		// Select the first option from the Azure OAuth screen
		const firstOption = await page.waitForSelector('text=Personal Account', {
			timeout: 60000
		});
		await firstOption.click();
		console.log('Selected the first option from the Azure OAuth screen');

		// Verify SharePoint linking UI is visible
		await expect(page.locator('text=SharePoint Connection')).toBeVisible({
			timeout: 60000
		});
		console.log('SharePoint linking UI is visible');
	} catch (error) {
		console.error('Test failed with error:', error);
		console.log('Current URL:', page.url());

		// Get page content for debugging
		const content = await page.content();
		console.log('Page content:', content);

		throw error;
	}
});
