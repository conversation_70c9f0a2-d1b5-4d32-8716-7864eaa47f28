import { test, expect } from '@playwright/test';
import { loginAsTestUser } from '../utils/auth-helpers';

test.describe('Course Creator & Courses integration', () => {
	test('enables Course Creator product and connects a Courses entity', async ({ page }) => {
		// Login to the application
		await loginAsTestUser(page);

		// Navigate to the companies page
		await page.goto('/app/companies');

		// Create a test company if needed or use existing
		const companyName = `Test Company ${Date.now()}`;
		await page.getByText('Create New Company').click();
		await page.getByLabel('Company Name').fill(companyName);
		await page.getByRole('button', { name: 'Create Company' }).click();

		// Wait for company creation and redirect
		await page.waitForURL(/\/company\/([^/]+)/);

		// Get company ID from URL
		const companyUrl = page.url();
		const companyIdMatch = companyUrl.match(/\/company\/([^/]+)/);
		const companyId = companyIdMatch ? companyIdMatch[1] : '';

		// Navigate to company settings to enable Course Creator product
		await page.goto(`/company/${companyId}/settings`);

		// Look for the Learning Integrations card
		const learningSection = page.getByText('Learning Integrations');
		await expect(learningSection).toBeVisible();

		// Find the Course Creator toggle and enable it
		const courseCreatorRow = page.getByText('Course Creator').first();
		await expect(courseCreatorRow).toBeVisible();

		// Look for the switch in the same container as the Course Creator text
		const courseCreatorToggle = page.getByRole('switch').filter({ has: courseCreatorRow });

		// If not already enabled, click to enable
		if ((await courseCreatorToggle.isChecked()) === false) {
			await courseCreatorToggle.click();
			// Wait for the toast notification
			await expect(page.getByText('Product enabled successfully')).toBeVisible();
		}

		// Navigate to vault to add a Courses data source
		await page.goto(`/company/${companyId}/vault`);

		// Click on the Add Storage Provider button
		await page.getByRole('button', { name: 'Add Storage Provider' }).click();

		// Verify Courses option is visible in the dropdown
		await expect(page.getByText('Courses')).toBeVisible();

		// Click on Courses option
		await page.getByText('Courses').click();

		// Verify we're on the Courses connection page
		await page.waitForURL(`/company/${companyId}/vault/connect/courses`);

		// Fill in the connection form
		await page.getByLabel('Account Name').fill('Test Courses Account');
		await page.getByLabel('API Key').fill('test-api-key');
		await page.getByLabel('API Secret').fill('test-api-secret');
		await page.getByLabel('Domain (Optional)').fill('courses.example.com');

		// Submit the form
		await page.getByRole('button', { name: 'Connect' }).click();

		// Verify successful connection with toast and redirect
		await expect(page.getByText('Connected to Courses!')).toBeVisible();

		// Should be redirected back to vault
		await page.waitForURL(`/company/${companyId}/vault`);

		// Verify the Courses entity shows up in the list
		await expect(page.getByText('Test Courses Account')).toBeVisible();

		// Cleanup: Can add deletion steps here if needed
	});
});
