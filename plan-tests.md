# Test Plan for Supabase Project

## Implementation Tools

### Database Testing with Basejump

- Install pgtap: `create extension pgtap with schema extensions;`
- Install dbdev following [database.dev](https://database.dev) instructions
- Install Basejump test helpers: `select dbdev.install('basejump-supabase_test_helpers');`
- Run tests using: `supabase test db`

### E2E Testing with Playwright

- Install Playwright: `pnpm add -D @playwright/test`
- Install browsers: `pnpm exec playwright install`
- Configure Playwright for Supabase authentication
- Run tests: `pnpm exec playwright test`

### Integration Testing

- Use Supabase's testing tools for database integration
- Leverage Inbucket for email testing (available at localhost:54324)
- Use mocked external services for third-party integrations

## Priority Order and Implementation Steps

### Phase 1: Core Authentication and User Management

1. Database Tests (Using Basejump)

   ```sql
   -- Example test for handle_new_user trigger
   begin;
   select plan(3);

   select has_function(
       'public',
       'handle_new_user',
       ARRAY['auth.users'],
       'Should have handle_new_user trigger function'
   );

   -- Test user creation
   select lives_ok(
       $$
       insert into auth.users (id, email)
       values ('00000000-0000-0000-0000-000000000000', '<EMAIL>');
       $$,
       'Should create base_user record'
   );

   -- Verify user record
   select results_eq(
       $$
       select count(*) from public.base_users
       where auth_id = '00000000-0000-0000-0000-000000000000'
       $$,
       ARRAY[1::bigint],
       'Should have created exactly one base_user record'
   );

   select * from finish();
   rollback;
   ```

2. E2E Tests (Using Playwright)

   ```typescript
   // tests/e2e/auth/signup.test.ts
   import { test, expect } from '@playwright/test';

   test('new user signup flow', async ({ page }) => {
   	await page.goto('/auth/signup');
   	await page.fill('[data-testid=email-input]', '<EMAIL>');
   	await page.fill('[data-testid=password-input]', 'password123');
   	await page.click('[data-testid=signup-button]');

   	// Wait for redirect to dashboard
   	await expect(page).toHaveURL('/dashboard');

   	// Verify user profile is created
   	const profileElement = await page.waitForSelector('[data-testid=user-profile]');
   	await expect(profileElement).toBeVisible();
   });
   ```

### Phase 2: Company Management

1. Database Tests

   ```sql
   -- Example test for company creation
   begin;
   select plan(4);

   -- Test company creation function
   select has_function(
       'public',
       'create_company',
       ARRAY['text', 'uuid'],
       'Should have create_company function'
   );

   -- More tests...

   select * from finish();
   rollback;
   ```

2. E2E Tests

   ```typescript
   // tests/e2e/companies/company-creation.test.ts
   test('company creation flow', async ({ page }) => {
   	// First login
   	await loginUser(page);

   	// Create company
   	await page.goto('/companies/new');
   	await page.fill('[data-testid=company-name]', 'Test Company');
   	await page.click('[data-testid=create-company]');

   	// Verify company creation
   	await expect(page).toHaveURL(/\/companies\/[\w-]+\/dashboard/);
   });
   ```

### Phase 3: Document Management and Search

1. Database Tests

   ```sql
   -- Example test for document search
   begin;
   select plan(3);

   -- Test search function existence
   select has_function(
       'public',
       'match_documents',
       ARRAY['text', 'uuid'],
       'Should have match_documents function'
   );

   -- More tests...

   select * from finish();
   rollback;
   ```

2. E2E Tests

   ```typescript
   // tests/e2e/documents/document-search.test.ts
   test('document search functionality', async ({ page }) => {
   	await loginUser(page);
   	await setupTestDocuments(page);

   	await page.goto('/documents');
   	await page.fill('[data-testid=search-input]', 'test document');
   	await page.click('[data-testid=search-button]');

   	// Verify search results
   	const results = await page.locator('[data-testid=search-result]').count();
   	expect(results).toBeGreaterThan(0);
   });
   ```

## Test Helper Functions

```typescript
// tests/helpers/auth.ts
export async function loginUser(page: Page) {
	await page.goto('/auth/login');
	await page.fill('[data-testid=email-input]', process.env.TEST_USER_EMAIL);
	await page.fill('[data-testid=password-input]', process.env.TEST_USER_PASSWORD);
	await page.click('[data-testid=login-button]');
	await page.waitForURL('/dashboard');
}

// tests/helpers/test-data.ts
export async function setupTestDocuments(page: Page) {
	// Setup code for creating test documents
}
```

## Todo List (In Priority Order)

1. Setup Testing Infrastructure

   - [ ] Install pgtap extension
   - [ ] Install dbdev
   - [ ] Install Basejump test helpers
   - [ ] Setup Playwright with necessary configurations
   - [ ] Create test helper functions
   - [ ] Setup test database with proper schema

2. Authentication Tests

   - [ ] Write database tests for user creation trigger
   - [ ] Write E2E tests for signup flow
   - [ ] Write E2E tests for login flow
   - [ ] Write database tests for user profile management
   - [ ] Test RLS policies for user data

3. Company Management Tests

   - [ ] Write database tests for company creation
   - [ ] Write E2E tests for company creation flow
   - [ ] Write database tests for company invitations
   - [ ] Write E2E tests for invitation flow
   - [ ] Test RLS policies for company data

4. Document Management Tests

   - [ ] Write database tests for document creation
   - [ ] Write E2E tests for document upload
   - [ ] Write database tests for search functionality
   - [ ] Write E2E tests for search interface
   - [ ] Test RLS policies for document access

5. Integration Tests

   - [ ] Setup email testing with Inbucket
   - [ ] Write SharePoint integration tests
   - [ ] Write WhatsApp integration tests
   - [ ] Test external service error handling

6. Performance and Load Tests
   - [ ] Write search performance tests
   - [ ] Test concurrent user scenarios
   - [ ] Test large dataset handling

## Test Environment Setup

```bash
# Install testing dependencies
pnpm add -D @playwright/test
pnpm exec playwright install

# Database setup
supabase start
psql -h localhost -U postgres -d postgres -c 'create extension pgtap with schema extensions;'
psql -h localhost -U postgres -d postgres -c "select dbdev.install('basejump-supabase_test_helpers');"

# Run tests
pnpm test:e2e    # Runs Playwright tests
supabase test db # Runs database tests
```

## Continuous Integration

Add the following to your CI pipeline:

```yaml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'

      - name: Install dependencies
        run: pnpm install

      - name: Setup Playwright
        run: pnpm exec playwright install --with-deps

      - name: Run E2E tests
        run: pnpm test:e2e

      - name: Run database tests
        run: supabase test db
```

## 1. Authentication and User Management Tests

- Test `
