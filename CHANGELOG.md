# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added

- Vector search capabilities with pgvector
- Document-level and file-level embeddings
- Advanced PowerPoint processing with metadata extraction
- WebSocket support for real-time updates

### Changed

- Improved RLS policies for better security
- Enhanced company isolation in search results
- Updated authentication flow for better security

### Fixed

- RLS policy conflicts in companies and company_members tables
- SharePoint token refresh handling
- Document processing error handling

## [1.0.0] - 2024-03-08

### Added

- Initial release of TOTM Search
- Multi-platform data source integration
- Company-based data isolation
- Role-based access control
- Document search and processing
- Real-time chat functionality

### Security

- Implemented comprehensive RLS policies
- Added OAuth2 integration with PKCE
- Secure token storage in Supabase Vault

## [0.9.0] - 2024-02-15

### Added

- Beta release with core functionality
- Basic search capabilities
- File upload and processing
- User management and authentication
- Company management

### Changed

- Improved database schema
- Enhanced security policies
- Updated UI components

## [0.8.0] - 2024-01-30

### Added

- Alpha release for testing
- Basic document management
- Simple search functionality
- User authentication

### Known Issues

- Limited RLS policies
- Basic search capabilities
- No real-time updates

[Unreleased]: https://github.com/yourusername/totm-search/compare/v1.0.0...HEAD
[1.0.0]: https://github.com/yourusername/totm-search/compare/v0.9.0...v1.0.0
[0.9.0]: https://github.com/yourusername/totm-search/compare/v0.8.0...v0.9.0
[0.8.0]: https://github.com/yourusername/totm-search/releases/tag/v0.8.0
