name: 'Database Tests'

on:
  push:
    branches: [staging]
    paths:
      - 'supabase/migrations/**'
      - 'supabase/tests/**'
      - '.github/workflows/database-tests.yml'
  pull_request:
    branches: [staging]
    paths:
      - 'supabase/migrations/**'
      - 'supabase/tests/**'
      - '.github/workflows/database-tests.yml'

jobs:
  test:
    runs-on: ubuntu-latest

    env:
      SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
      SUPABASE_DB_PASSWORD: ${{ secrets.SUPABASE_DB_PASSWORD }}
      SUPABASE_PROJECT_ID: ${{ secrets.SUPABASE_PROJECT_ID }}

    steps:
      - uses: actions/checkout@v3

      - name: Setup Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: latest

      - name: Start Supabase Local Development Setup
        run: supabase start

      - name: Run Database Tests
        run: supabase test db

      - name: Stop Supabase Local Development Setup
        if: always()
        run: supabase stop

      - name: Report Test Results
        if: always()
        uses: dorny/test-reporter@v1
        with:
          name: Database Tests
          path: supabase/tests/**/*.sql
          reporter: pgtap-json 