import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig, loadEnv } from 'vite';
import basicSsl from '@vitejs/plugin-basic-ssl';

export default defineConfig(({ mode }) => {
	// Load env file based on mode in all environments
	const env = loadEnv(mode, process.cwd(), '');

	return {
		plugins: [sveltekit(), basicSsl()],
		define: {
			// Expose env variables
			'process.env.NODE_ENV': JSON.stringify(mode),
			'process.env.PUBLIC_FB_APP_ID': JSON.stringify(env.PUBLIC_FB_APP_ID),
			'process.env.PUBLIC_FB_CONFIG_ID': JSON.stringify(env.PUBLIC_FB_CONFIG_ID),
			'process.env.PRIVATE_FB_SYSTEM_USER_TOKEN': JSON.stringify(env.PRIVATE_FB_SYSTEM_USER_TOKEN)
		},
		server: {
			port: 5173,
			strictPort: true,
			host: true
		}
	};
});
