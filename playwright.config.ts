import type { PlaywrightTestConfig } from '@playwright/test';
import { devices } from '@playwright/test';

/**
 * Read environment variables from file.
 * https://github.com/motdotla/dotenv
 */
// import dotenv from 'dotenv';
// import path from 'path';
// dotenv.config({ path: path.resolve(__dirname, '.env') });

/**
 * See https://playwright.dev/docs/test-configuration.
 */
const config: PlaywrightTestConfig = {
	testDir: './tests/e2e',
	timeout: 30 * 1000,
	expect: {
		timeout: 5000
	},
	/* Run tests in files in parallel */
	fullyParallel: true,
	/* Fail the build on CI if you accidentally left test.only in the source code. */
	forbidOnly: !!process.env.CI,
	/* Retry on CI only */
	retries: process.env.CI ? 2 : 0,
	/* Opt out of parallel tests on CI. */
	workers: process.env.CI ? 1 : undefined,
	/* Reporter to use. See https://playwright.dev/docs/test-reporters */
	reporter: 'html',
	/* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
	use: {
		headless: false, // Run tests in headed (visible browser) mode
		actionTimeout: 0,
		baseURL: 'https://localhost:5173',
		trace: 'on-first-retry',
		ignoreHTTPSErrors: true,
		screenshot: 'only-on-failure',
		video: 'retain-on-failure'
	},

	/* Configure projects for major browsers */
	projects: [
		{
			name: 'setup',
			testDir: './tests/setup',
			testMatch: /auth\.setup\.ts/,
			timeout: 5 * 60 * 1000, // 5 minutes timeout for auth setup
			use: {
				headless: false,
				ignoreHTTPSErrors: true,
				actionTimeout: 60000 // Increase action timeout for auth flow
			}
		},
		{
			name: 'chromium',
			use: {
				...devices['Desktop Chrome'],
				storageState: 'playwright/.auth/user.json',
				ignoreHTTPSErrors: true
			}
		},
		{
			name: 'authenticated firefox',
			use: {
				...devices['Desktop Firefox'],
				storageState: './playwright/.auth/user.json'
			},
			dependencies: ['setup']
		},
		{
			name: 'authenticated webkit',
			use: {
				...devices['Desktop Safari'],
				storageState: './playwright/.auth/user.json'
			},
			dependencies: ['setup']
		}
		/* Test against mobile viewports. */
		// {
		//   name: 'Mobile Chrome',
		//   use: { ...devices['Pixel 5'] },
		// },
		// {
		//   name: 'Mobile Safari',
		//   use: { ...devices['iPhone 12'] },
		// },

		/* Test against branded browsers. */
		// {
		//   name: 'Microsoft Edge',
		//   use: { ...devices['Desktop Edge'], channel: 'msedge' },
		// },
		// {
		//   name: 'Google Chrome',
		//   use: { ...devices['Desktop Chrome'], channel: 'chrome' },
		// },
	],
	/* Run your local dev server before starting the tests */
	webServer: {
		command: 'pnpm run dev',
		url: 'https://localhost:5173',
		reuseExistingServer: !process.env.CI,
		timeout: 120000,
		ignoreHTTPSErrors: true,
		env: {
			// Ensure we're using the WSL environment
			PLAYWRIGHT_BROWSERS_PATH: '0'
		}
	}
};

export default config;
