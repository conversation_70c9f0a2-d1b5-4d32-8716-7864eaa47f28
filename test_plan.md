# RLS Testing Plan

## Overview

This document outlines our plan for testing Row-Level Security (RLS) policies in our Supabase database, focusing on companies and company_members tables.

## Prerequisites

- Supabase project with pgTAP extension
- Basejump test helpers extension

## Test Structure

We'll organize our tests in separate SQL files, each containing related test cases:

1. `00-test-helpers-setup.sql` - Setup for Basejump test helpers
2. `01-rls-enabled-check.sql` - Verify RLS is enabled on all tables
3. `02-companies-rls-tests.sql` - Test RLS policies for companies table
4. `03-company-members-rls-tests.sql` - Test RLS policies for company_members table

## Company Business Rules

1. Users should only see companies they are members of
2. Users should only be able to update companies where they are admins
3. Only authenticated users should be able to access any company data
4. Company creation should be allowed for authenticated users
5. Company deletion should only be allowed for company admins

## Company Members Business Rules

1. Company admins should be able to add members to their companies
2. Company admins should be able to update members of their companies (except themselves)
3. Company admins should not be able to update members of other companies
4. Company admins should not be able to update inactive members
5. Regular members should not be able to update any member records
6. Users should only see members from companies they belong to
7. Only admins should be able to delete members from their companies

## Implementation Steps

1. Check current database schema
2. Verify existing RLS policies
3. Implement and test each policy one by one
4. Ensure all tests pass before proceeding to the next test

## Success Criteria

- All tests pass successfully
- RLS policies correctly implement the business rules
- No unauthorized access is possible through the tested scenarios
