#!/usr/bin/env bash

# Exit on error
set -e

# Source nvm if available
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"

# Check if nvm command exists and use it
if type nvm >/dev/null 2>&1; then
  echo "Using nvm to ensure compatible Node.js version..."
  nvm use --lts >/dev/null 2>&1 || nvm use default >/dev/null 2>&1 || echo "⚠️ Could not switch Node version with nvm"
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d 'v' -f2)
REQUIRED_VERSION="18.12.0"

# Function to compare versions
version_lt() {
  [ "$(printf '%s\n' "$1" "$2" | sort -V | head -n1)" = "$1" ]
}

# Check if Node version is too low
if version_lt "$NODE_VERSION" "$REQUIRED_VERSION"; then
  echo "⚠️ Warning: Node.js version $NODE_VERSION is lower than required version $REQUIRED_VERSION"
  echo "Some features may not work correctly."
  echo "Consider upgrading Node.js or installing nvm:"
  echo "  curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash"
  echo "  nvm install --lts"
fi

echo "Starting Supabase local service..."
# Check if Supabase is already running
if ! supabase status | grep -q "Started"; then
  supabase start
fi

echo "Generating TypeScript types from Supabase schema..."
supabase gen types typescript --local > database.types.ts

echo "Types successfully generated at database.types.ts" 