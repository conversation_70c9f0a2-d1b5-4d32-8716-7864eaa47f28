#!/bin/bash

# Check if we're starting or stopping local mode
if [ "$1" = "start" ]; then
    echo "Switching to local Supabase configuration..."
    
    # Backup the current config if it exists and isn't already a backup
    if [ -f "supabase/config.toml" ] && [ ! -f "supabase/config.staging.toml" ]; then
        mv supabase/config.toml supabase/config.staging.toml
    fi
    
    # Copy local config to active config
    if [ -f "supabase/config.local.toml" ]; then
        cp supabase/config.local.toml supabase/config.toml
        echo "Local Supabase configuration activated"
    else
        echo "Error: Local config file not found at supabase/config.local.toml"
        exit 1
    fi
    
elif [ "$1" = "stop" ]; then
    echo "Restoring staging Supabase configuration..."
    
    # Restore the staging config if it exists
    if [ -f "supabase/config.staging.toml" ]; then
        mv supabase/config.staging.toml supabase/config.toml
        echo "Staging Supabase configuration restored"
    else
        echo "Error: Staging config backup not found"
        exit 1
    fi
    
else
    echo "Usage: $0 [start|stop]"
    echo "  start: Switch to local Supabase configuration"
    echo "  stop:  Restore staging Supabase configuration"
    exit 1
fi 