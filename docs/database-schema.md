# Database Schema Documentation

## Overview

This document provides a comprehensive overview of the database schema, including table relationships, constraints, and indexes.

## Table Relationships

### Core Tables

#### base_users

- Primary Key: `id` (uuid)
- References:
  - `auth.users(id)` ON DELETE CASCADE, ON UPDATE CASCADE
- Columns:
  - `display_name` (text)
  - `avatar_url` (text)
  - `bio` (text)
  - `created_at` (timestamptz)
  - `updated_at` (timestamptz)
  - `email` (text)
  - `raw_user_meta_data` (jsonb)
  - `phone_number` (bigint)

#### companies

- Primary Key: `id` (uuid)
- No direct foreign keys
- Columns:
  - `name` (text)
  - `status` (company_status)
  - `transcription_time_allocated` (integer)
  - `transcription_time_used` (integer)
  - `created_at` (timestamptz)
  - `updated_at` (timestamptz)
  - `is_public` (boolean)
  - `created_by` (uuid)

#### company_members

- Primary Key: `id` (uuid)
- References:
  - `companies(id)` ON DELETE CASCADE
  - `users(id)` ON DELETE CASCADE
- Columns:
  - `role` (user_role)
  - `display_name` (text)
  - `avatar_url` (text)
  - `is_active` (boolean)
  - `created_at` (timestamptz)
  - `updated_at` (timestamptz)

### Authentication & Authorization

#### auth_events

- Primary Key: `id` (uuid)
- References:
  - `companies(id)` ON DELETE CASCADE
- Columns:
  - `event_type` (text) CHECK IN ('authorization', 'deauthorization', 'security_incident')
  - `platform` (text) CHECK IN ('whatsapp', 'slack', 'microsoft', 'google')
  - `metadata` (jsonb)
  - `created_at` (timestamptz)

#### company_invitations

- Primary Key: `id` (uuid)
- References:
  - `companies(id)` ON DELETE CASCADE
  - `auth.users(id)` ON DELETE CASCADE (invited_by)
- Columns:
  - `company_id` (uuid)
  - `invited_by` (uuid)
  - `role` (text)
  - `email` (text)
  - `token` (text) UNIQUE
  - `invitation_type` (invitation_type)
  - `metadata` (jsonb)
  - `expires_at` (timestamptz)
  - `created_at` (timestamptz)

#### company_invites

- Primary Key: `id` (uuid)
- References:
  - `companies(id)` ON DELETE CASCADE
  - `auth.users(id)` ON DELETE CASCADE (invited_by)
- Columns:
  - `company_id` (uuid)
  - `email` (text)
  - `role` (user_role)
  - `status` (invite_status)
  - `invited_by` (uuid)
  - `expires_at` (timestamptz)
  - `created_at` (timestamptz)
  - `updated_at` (timestamptz)

### Company Customization

#### company_personalization

- Primary Key: `id` (uuid)
- References:
  - `companies(id)` ON DELETE CASCADE
- Unique Constraint: `company_id`
- Columns:
  - `custom_prompt` (text)
  - `branding_logo` (text)
  - `small_logo` (text)
  - `slug` (text)
  - `created_at` (timestamptz)
  - `updated_at` (timestamptz)

### Team Management

#### teams

- Primary Key: `id` (uuid)
- References:
  - `companies(id)` ON DELETE CASCADE
  - `teams(id)` ON DELETE CASCADE (parent_team_id)
- Columns:
  - `name` (text)
  - `description` (text)
  - `parent_team_id` (uuid)
  - `team_id` (text)
  - `created_at` (timestamptz)
  - `updated_at` (timestamptz)

#### team_members

- Primary Key: `id` (uuid)
- References:
  - `teams(id)` ON DELETE CASCADE
  - `auth.users(id)` ON DELETE CASCADE
- Unique Constraint: `(team_id, user_id)`
- Columns:
  - `external_id` (text)
  - `platform_type` (platform_type)
  - `added_at` (timestamptz)

#### team_permissions

- Primary Key: `id` (uuid)
- References:
  - `teams(id)` ON DELETE CASCADE
  - `files(id)` ON DELETE CASCADE
- Columns:
  - `permission_level` (permission_level)
  - `created_at` (timestamptz)
  - `updated_at` (timestamptz)

### Document Management

#### documents

- Primary Key: `id` (uuid)
- References:
  - `files(id)` ON DELETE CASCADE, ON UPDATE CASCADE
- Columns:
  - `content` (text)
  - `metadata` (jsonb)
  - `created_at` (timestamptz)
  - `updated_at` (timestamptz)
  - `embedding` (vector(1024))

#### files

- Primary Key: `id` (uuid)
- References:
  - `companies(id)` ON DELETE CASCADE
- Columns:
  - `data_source_id` (uuid)
  - `external_id` (text)
  - `name` (text)
  - `path` (text)
  - `size` (bigint)
  - `mime_type` (text)
  - `summary` (text)
  - `added_to_knowledge_base` (boolean)
  - `is_ingested` (boolean)
  - `hash` (text)
  - `processing_error` (text)
  - `metadata` (jsonb)
  - `file_embedding` (vector(1024))

### Data Integration

#### data_sources

- Primary Key: `id` (uuid)
- References:
  - `companies(id)` ON DELETE CASCADE
  - `vault.secrets(id)` ON DELETE CASCADE
- Columns:
  - `platform_type` (platform_type)
  - `display_name` (text)
  - `access_token` (text)
  - `id_token` (text)
  - `secret_oauth` (text)
  - `tenant_id` (text)
  - `site_id` (text)
  - `drive_id` (text)
  - `deltas` (text)
  - `scope` (text)

### Communication

#### chat_messages

- Primary Key: `id` (uuid)
- References:
  - `companies(id)` ON DELETE CASCADE
  - `auth.users(id)` ON DELETE CASCADE
- Columns:
  - `content` (text)
  - `created_at` (timestamptz)

#### communication_channels

- Primary Key: `id` (uuid)
- References:
  - `companies(id)` ON DELETE CASCADE
  - `oauth_tokens(id)` ON DELETE SET NULL
  - `users(id)` ON DELETE SET NULL
- Columns:
  - `platform_type` (text)
  - `status` (text)
  - `deauthorized_at` (timestamptz)

### Support System

#### totm_inbox_questions

- Primary Key: `id` (uuid)
- References:
  - `companies(id)` ON DELETE CASCADE
  - `auth.users(id)` ON DELETE CASCADE (user_id)
  - `auth.users(id)` ON DELETE SET NULL (assigned_to)
- Columns:
  - `question_text` (text)
  - `status` (text)
  - `priority` (text)
  - `category` (text)
  - `created_at` (timestamptz)
  - `updated_at` (timestamptz)

#### totm_inbox_responses

- Primary Key: `id` (uuid)
- References:
  - `totm_inbox_questions(id)` ON DELETE CASCADE
  - `auth.users(id)` ON DELETE CASCADE (expert_id)
- Columns:
  - `response_text` (text)
  - `created_at` (timestamptz)
  - `updated_at` (timestamptz)

## Enums

### company_status

- `trial`
- `paid`
- `suspended`

### user_role

- `admin`
- `expert`
- `member`

### platform_type

- `sharepoint`
- `whatsapp`
- `slack`
- `email`
- `google`

### channel_status

- `connected`
- `disconnected`
- `pending`

### invitation_type

- `one_time`
- `24_hour`

### invite_status

- `pending`
- `accepted`
- `expired`

### permission_level

- `read`
- `write`
- `admin`

## Indexes

### Performance Indexes

- `documents_embedding_idx`: GiST index on documents(embedding)
- `files_company_id_idx`: B-tree index on files(company_id)
- `company_members_user_id_idx`: B-tree index on company_members(user_id)

### Unique Constraints

- `companies_name_key`: Unique constraint on companies(name)
- `base_users_email_key`: Unique constraint on base_users(email)
- `data_sources_company_platform_key`: Unique constraint on data_sources(company_id, platform_type)

## Cascade Behaviors

The database uses cascading deletes extensively to maintain referential integrity:

1. When a company is deleted:

   - All company members are deleted
   - All files are deleted
   - All documents are deleted
   - All data sources are deleted
   - All chat messages are deleted
   - All team permissions are deleted
   - All team members are deleted
   - All teams are deleted
   - All auth events are deleted
   - All company invitations are deleted
   - All company invites are deleted
   - All company personalization settings are deleted

2. When a file is deleted:

   - All associated documents are deleted
   - All permissions are deleted
   - All transcription records are deleted
   - All team permissions referencing the file are deleted

3. When a user is deleted:

   - All their chat messages are deleted
   - All their permissions are deleted
   - All their company memberships are deleted
   - All team memberships are deleted
   - All expert responses are deleted
   - All questions created by the user are deleted

4. When a team is deleted:
   - All team members are deleted
   - All team permissions are deleted
   - All child teams are deleted (recursive)

## Vector Search

The database uses pgvector for semantic search capabilities:

- Document embeddings: 1024-dimensional vectors
- File embeddings: 1024-dimensional vectors
- Similarity search using cosine distance
- Configurable similarity thresholds
- Company-based isolation for search results
