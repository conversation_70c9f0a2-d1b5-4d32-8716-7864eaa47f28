# Security Policies Documentation

## Overview

This document details the Row Level Security (RLS) policies and other security measures implemented in the application.

## Row Level Security (RLS) Policies

### Companies Table

#### Read Access

```sql
-- Members can view their companies
CREATE POLICY "Members can view their companies"
ON companies FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM company_members cm
    WHERE cm.company_id = companies.id
    AND cm.user_id = auth.uid()
    AND cm.is_active = true
  )
);

-- Allow authenticated users to select their own companies
CREATE POLICY "Allow authenticated users to select their own companies"
ON companies FOR SELECT
USING (created_by = auth.uid());
```

#### Write Access

```sql
-- Allow authenticated users to create companies
CREATE POLICY "Allow authenticated users to create companies"
ON companies FOR INSERT
WITH CHECK (created_by = auth.uid());

-- Allow authenticated users to update their own companies
CREATE POLICY "Allow authenticated users to update their own companies"
ON companies FOR UPDATE
USING (created_by = auth.uid())
WITH CHECK (created_by = auth.uid());

-- Allow authenticated users to delete their own companies
CREATE POLICY "Allow authenticated users to delete their own companies"
ON companies FOR DELETE
USING (created_by = auth.uid());
```

### Documents Table

#### Read Access

```sql
-- Allow company members to read documents
CREATE POLICY "allow company members to read documents"
ON documents FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM company_members cm
    JOIN files f ON f.company_id = cm.company_id
    WHERE cm.user_id = auth.uid()
    AND f.id = documents.file_id
  )
);
```

#### Write Access

```sql
-- Allow company admins and experts to create documents
CREATE POLICY "allow company admins and experts to create documents"
ON documents FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM company_members cm
    JOIN files f ON f.company_id = cm.company_id
    WHERE cm.user_id = auth.uid()
    AND f.id = documents.file_id
    AND cm.role IN ('admin', 'expert')
  )
);

-- Similar policies for UPDATE and DELETE operations
```

### Files Table

#### Read Access

```sql
-- Allow company members to read files
CREATE POLICY "allow company members to read files"
ON files FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM company_members cm
    WHERE cm.user_id = auth.uid()
    AND cm.company_id = files.company_id
  )
);
```

#### Write Access

```sql
-- Allow company admins and experts to manage files
CREATE POLICY "allow company admins and experts to create files"
ON files FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM company_members cm
    WHERE cm.user_id = auth.uid()
    AND cm.company_id = files.company_id
    AND cm.role IN ('admin', 'expert')
  )
);

-- Similar policies for UPDATE and DELETE operations
```

## Authentication Security

### Password Requirements

- Minimum length: 8 characters
- Must contain at least:
  - One uppercase letter
  - One lowercase letter
  - One number
  - One special character

### Session Management

- JWT tokens with 1-hour expiration
- Refresh tokens with 30-day expiration
- Automatic token refresh
- Session invalidation on password change

### OAuth2 Security

- PKCE flow for all OAuth2 integrations
- State parameter validation
- Secure token storage in Supabase Vault
- Automatic token refresh handling

## Data Protection

### Encryption

- Data at rest encryption using Supabase's built-in encryption
- Sensitive data stored in Supabase Vault
- TLS 1.3 for all API communications

### Access Controls

1. Role-Based Access Control (RBAC)

   - Admin: Full access
   - Expert: Document management and advanced search
   - Member: Basic access

2. Company Isolation
   - Data segregation by company
   - Cross-company access prevention
   - Public company exceptions

### API Security

1. Rate Limiting

   - 100 requests per minute per user
   - 1000 requests per minute per company
   - Configurable limits per endpoint

2. Input Validation
   - Request body validation
   - Query parameter sanitization
   - File type verification
   - Size limits enforcement

## Security Best Practices

### Development Guidelines

1. SQL Injection Prevention

   - Use parameterized queries
   - Validate and sanitize inputs
   - Avoid dynamic SQL generation

2. XSS Prevention

   - Content Security Policy (CSP)
   - Input sanitization
   - Output encoding

3. CSRF Protection
   - CSRF tokens
   - SameSite cookie attributes
   - Origin validation

### Monitoring and Auditing

1. Security Logging

   - Authentication attempts
   - Policy violations
   - Data access patterns
   - System changes

2. Audit Trail
   - User actions
   - Data modifications
   - Policy changes
   - Access patterns

## Compliance

### Data Privacy

- GDPR compliance capabilities
- Data retention policies
- Right to be forgotten implementation
- Data portability support

### Security Standards

- OWASP Top 10 compliance
- SOC 2 readiness
- NIST cybersecurity framework alignment
