# SharePoint Integration

## Overview
The application integrates with SharePoint to allow users to connect their SharePoint sites as data sources. This enables users to access and manage their SharePoint content through our application.

## Authentication Flow
1. User initiates the flow by clicking "Link SharePoint Data Source" in the vault page
2. The button triggers a POST to `/api/link-sharepoint` which:
   - Generates a code verifier and challenge for PKCE
   - Stores necessary state in cookies
   - Redirects to Microsoft's OAuth endpoint
3. User authenticates with Microsoft and grants permissions
4. Microsoft redirects back to `/auth/add-data-source` with an auth code
5. The auth code is exchanged for access tokens
6. User is redirected to `/company/[companyId]/vault/new-data-source`

## Data Source Selection
1. The new-data-source page receives the tokens and user's SharePoint sites
2. User selects a site from the dropdown
3. On selection confirmation:
   - Site details are fetched
   - A new data source is created in the database
   - Site permissions are retrieved
4. User can then:
   - Activate users for the site
   - Start ingesting files from the vault

## Database Schema
The `data_sources` table stores:
- `id`: UUID primary key
- `company_id`: Reference to companies table
- `type`: Data source type (e.g., "sharepoint")
- `display_name`: Name of the SharePoint site
- `secret_oauth`: Reference to vault.secrets for OAuth refresh token
- `id_token`: Reference to vault.secrets for ID token
- `access_token`: Reference to vault.secrets for access token
- `tenant_id`: Microsoft tenant ID
- `scope`: OAuth scopes granted
- `site_id`: SharePoint site ID
- `drive_id`: SharePoint drive ID
- `platform`: Platform type (default: "sharepoint")

## Security
- OAuth tokens are stored securely in the vault.secrets table
- Row Level Security (RLS) policies ensure users can only access their company's data sources
- PKCE (Proof Key for Code Exchange) is used in the OAuth flow for enhanced security

## Implementation Details

### 1. Initiating the OAuth Flow (`/api/link-sharepoint`)
```typescript
// POST handler generates PKCE challenge and redirects to Microsoft
export const POST: RequestHandler = async ({ cookies }) => {
  const codeVerifier = generateCodeVerifier();
  const codeChallenge = generateCodeChallenge(codeVerifier);
  const state = generateState({ purpose: "addDataSource" });
  
  // Store in cookies for verification
  cookies.set("code_verifier", codeVerifier, { 
    path: "/",
    httpOnly: true,
    secure: true,
    sameSite: "lax"
  });
  
  // Redirect to Microsoft OAuth
  const authUrl = buildMicrosoftAuthUrl({
    codeChallenge,
    state,
    scopes: [
      "Files.Read.All",
      "Sites.Read.All",
      "User.Read",
      "offline_access"
    ]
  });
  
  return new Response(null, { 
    status: 302, 
    headers: { location: authUrl }
  });
};
```

### 2. Handling the OAuth Callback (`/auth/add-data-source`)
```typescript
// Exchange code for tokens and redirect to site selection
export const GET: RequestHandler = async ({ url, cookies }) => {
  const code = url.searchParams.get("code");
  const codeVerifier = cookies.get("code_verifier");
  
  // Exchange code for tokens
  const tokens = await exchangeCodeForTokens(
    code,
    codeVerifier,
    AZURE_CLIENT_SECRET
  );
  
  // Store tokens securely
  const { data: tokenSecrets } = await supabase.rpc(
    "create_secrets_batch",
    { secrets: [tokens.access, tokens.refresh] }
  );
  
  // Redirect to site selection
  return redirect(
    `/company/${companyId}/vault/new-data-source?tokens=${tokenSecrets}`
  );
};
```

### 3. Site Selection Page (`/company/[companyId]/vault/new-data-source`)
```typescript
// Load available SharePoint sites
export const load: PageServerLoad = async ({ 
  params, locals, cookies, url 
}) => {
  const tokens = url.searchParams.get("tokens");
  
  // Get user's SharePoint sites
  const sites = await fetchUserSites(tokens.access);
  
  return {
    sites,
    tokens
  };
};

// Handle site selection
export const actions = {
  selectSite: async ({ request, locals }) => {
    const formData = await request.formData();
    const siteId = formData.get("siteId");
    
    // Create data source
    const { data: dataSource } = await supabase
      .from("data_sources")
      .insert({
        company_id: companyId,
        type: "sharepoint",
        site_id: siteId,
        // ... other fields
      })
      .select()
      .single();
      
    return {
      success: true,
      dataSource
    };
  }
};
```

## Required Environment Variables
```env
PUBLIC_AZURE_CLIENT_ID=your_client_id
PRIVATE_AZURE_CLIENT_SECRET=your_client_secret
PUBLIC_AZURE_TENANT_ID=your_tenant_id
```

## Error Handling
- Invalid/expired tokens: Redirect to re-authenticate
- Missing permissions: Display clear error message
- Network issues: Retry with exponential backoff
- Invalid state/PKCE: Restart authentication flow 