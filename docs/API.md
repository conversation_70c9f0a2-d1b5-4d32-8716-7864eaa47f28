# API Documentation

## Overview

This document details the API endpoints, authentication methods, and usage examples for the TOTM Search application.

## Authentication

### Headers

```typescript
{
  "Authorization": "Bearer <access_token>",
  "Content-Type": "application/json"
}
```

### Authentication Endpoints

#### Magic Link Authentication

```http
POST /auth/magic-link
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

#### OAuth2 Authentication

```http
GET /auth/oauth/microsoft
GET /auth/oauth/google
```

## Companies API

### Create Company

```http
POST /api/companies
Content-Type: application/json

{
  "name": "Example Company",
  "status": "trial"
}
```

### List Companies

```http
GET /api/companies
```

### Update Company

```http
PATCH /api/companies/:id
Content-Type: application/json

{
  "name": "Updated Company Name",
  "status": "paid"
}
```

## Documents API

### Search Documents

```http
POST /api/documents/search
Content-Type: application/json

{
  "query": "search term",
  "filters": {
    "company_id": "uuid",
    "mime_type": ["application/pdf", "text/plain"]
  },
  "limit": 10,
  "offset": 0
}
```

### Upload Document

```http
POST /api/documents
Content-Type: multipart/form-data

file: <file>
company_id: uuid
metadata: {
  "source": "upload",
  "tags": ["important", "contract"]
}
```

### Get Document

```http
GET /api/documents/:id
```

## Data Sources API

### Connect Data Source

```http
POST /api/data-sources
Content-Type: application/json

{
  "platform_type": "sharepoint",
  "company_id": "uuid",
  "display_name": "Company SharePoint"
}
```

### List Data Sources

```http
GET /api/data-sources?company_id=uuid
```

### Sync Data Source

```http
POST /api/data-sources/:id/sync
```

## Users API

### Invite User

```http
POST /api/companies/:id/invites
Content-Type: application/json

{
  "email": "<EMAIL>",
  "role": "member"
}
```

### List Company Members

```http
GET /api/companies/:id/members
```

### Update Member Role

```http
PATCH /api/companies/:company_id/members/:member_id
Content-Type: application/json

{
  "role": "expert"
}
```

## Chat API

### Send Message

```http
POST /api/chat/messages
Content-Type: application/json

{
  "content": "Message content",
  "company_id": "uuid"
}
```

### List Messages

```http
GET /api/chat/messages?company_id=uuid
```

## Webhooks

### SharePoint Webhook

```http
POST /api/webhooks/sharepoint
Content-Type: application/json

{
  "value": [
    {
      "subscriptionId": "string",
      "clientState": "string",
      "expirationDateTime": "datetime",
      "resource": "string",
      "tenantId": "string",
      "siteUrl": "string",
      "webId": "string"
    }
  ]
}
```

## Error Handling

### Error Response Format

```json
{
	"error": {
		"code": "string",
		"message": "string",
		"details": {}
	}
}
```

### Common Error Codes

- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `422`: Validation Error
- `429`: Rate Limit Exceeded
- `500`: Internal Server Error

## Rate Limiting

- Default: 100 requests per minute per user
- Bulk operations: 10 requests per minute
- Search operations: 50 requests per minute

Headers:

```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## Pagination

### Request

```http
GET /api/resources?limit=10&offset=0
```

### Response

```json
{
	"data": [],
	"pagination": {
		"total": 100,
		"limit": 10,
		"offset": 0,
		"has_more": true
	}
}
```

## Filtering

### Query Parameters

```http
GET /api/resources?filter[field]=value&filter[range][created_at][gt]=2023-01-01
```

### JSON Body Filters

```json
{
	"filters": {
		"field": "value",
		"range": {
			"created_at": {
				"gt": "2023-01-01",
				"lt": "2024-01-01"
			}
		}
	}
}
```

## Sorting

```http
GET /api/resources?sort=field&order=asc
GET /api/resources?sort=-field (descending)
```

## File Upload

### Single File

```http
POST /api/files
Content-Type: multipart/form-data

file: <file>
metadata: {
  "description": "Document description",
  "tags": ["important"]
}
```

### Multiple Files

```http
POST /api/files/bulk
Content-Type: multipart/form-data

files[]: <file1>
files[]: <file2>
metadata: {
  "batch_id": "uuid",
  "tags": ["batch-upload"]
}
```

## WebSocket API

### Connection

```javascript
const ws = new WebSocket('wss://api.example.com/ws', {
	headers: {
		Authorization: `Bearer ${token}`
	}
});
```

### Message Format

```json
{
	"type": "message_type",
	"payload": {},
	"id": "uuid"
}
```

### Event Types

- `chat_message`
- `document_updated`
- `sync_status`
- `notification`

## SDK Examples

### TypeScript/JavaScript

```typescript
import { TotmSearchClient } from '@totm/sdk';

const client = new TotmSearchClient({
	apiKey: 'your-api-key',
	baseUrl: 'https://api.example.com'
});

// Search documents
const results = await client.documents.search({
	query: 'search term',
	filters: {
		company_id: 'uuid'
	}
});

// Upload document
const document = await client.documents.upload({
	file: fileBlob,
	metadata: {
		tags: ['important']
	}
});
```

## Postman Collection

A Postman collection is available for testing the API:
[Download Postman Collection](./postman/totm-search-api.json)

## API Versioning

The API is versioned through the URL:

```http
https://api.example.com/v1/resources
```

Current versions:

- `v1`: Current stable version
- `v2-beta`: Beta version with new features
