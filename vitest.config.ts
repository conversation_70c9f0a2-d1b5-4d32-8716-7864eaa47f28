import { defineConfig } from "vitest/config";
import { sveltekit } from "@sveltejs/kit/vite";
import path from "path";

export default defineConfig({
  plugins: [sveltekit()],
  test: {
    include: ["tests/integration/**/*.{test,spec}.{js,ts}"],
    environment: "jsdom",
    globals: true,
    setupFiles: ["src/test-utils/test-setup.ts"],
    alias: {
      $lib: path.resolve(__dirname, "./src/lib"),
      $app: path.resolve(
        __dirname,
        "./node_modules/@sveltejs/kit/src/runtime/app",
      ),
      $env: path.resolve(__dirname, "./src/test-utils/env-mock"),
    },
  },
});
