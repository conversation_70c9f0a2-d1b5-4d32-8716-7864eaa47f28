Speak with me in english

- confirm understanding of the requirements, and confirm plan before writing/editing any code
- alter as few lines as possible
- write DRY code. Do not write code that has already been written elsewhere. Ask if function exists so it can be added before writing functions.
- write only valid svelteKit 4 and typescript

Components are in src/lib/components. the site is build mostly using shadcn svelte components. Before you write code, ask me if such a function or page exists already so you can have all the required context to write the code appropriately.
