node_modules

# Output
.output
.vercel
/.svelte-kit
/build

# OS
.DS_Store
Thumbs.db

# Env
.env
.env.*
!.env.example
!.env.test

# Vite
vite.config.js.timestamp-*
vite.config.ts.timestamp-*
.aider*
.env*.local

# Playwright
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/
playwright/.auth
playwright/.auth/user.json
test-results/.last-run.json
.env.test

# Supabase local config
supabase/config.local.toml
certs/prod-ca-2021.crt
supabase/config.toml
supabase-trash
playwright/.auth/user.json
test-results/.last-run.json
pnpm-workspace.yaml
package.json
pnpm-lock.yaml
old.env.local
supabase/config.toml
supabase/migrations/20250419114004_remote_schema.sql
package.json
